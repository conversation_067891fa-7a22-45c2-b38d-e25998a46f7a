---
description: Purpose: Type safety and consistency bridge between client and server through shared code When to Use: Schema design, type definitions, maintaining client-server consistency, database design, validation schemas
globs: 
alwaysApply: false
---
# Shared Code Guidelines

## Overview
- `shared/` directory contains code used by both client and server
- Ensures type safety and consistency across the application
- Primary focus on schema definitions, types, and validation

## Key Files
- `schema.ts` - Database schema definitions using Drizzle ORM
- `schema.types.ts` - TypeScript types and Zod validation schemas
- `router.types.ts` - tRPC router type definitions

## Schema Design Principles
- Tables defined using Drizzle ORM's `pgTable`
- Enums defined using `pgEnum` for constrained values
- Use CUID2 for ID generation
- Include common fields: id, upkeepCompanyId, timestamps, etc.
- Define indexes for frequently queried fields
- Document schema fields with comments

## Type Definitions
- Export TypeScript types for use throughout the application
- Define validation schemas using Zod
- Match validation rules to database constraints
- Keep types in sync with database schema

## tRPC Implementation
- Shared type definitions enable type-safe API communication
- Server defines procedures, client consumes them
- Use Zod schemas for input validation
- Handle errors in a type-safe manner

## Multi-tenancy
- All tables include `upkeepCompanyId` for data isolation
- All queries filter by company ID to ensure data separation

## Example Usage

### Schema Definition
```typescript
export const incidents = pgTable(
  "incidents",
  {
    id: varchar("id", { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar("upkeep_company_id", { length: 10 })
      .notNull(),
    title: varchar("title", { length: 255 }).notNull(),
    description: text("description"),
    status: statusEnum("status").notNull(),
    // more fields...
  },
  (table) => [
    index("incident_title_index").on(table.title),
  ]
);
```

### Type/Schema Definition
```typescript
export const CreateIncidentSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.enum(["open", "in_review", "closed"]),
  // more fields...
});

export type CreateIncident = z.infer<typeof CreateIncidentSchema>;
```

### Importing Shared Code
```typescript
// In server code
import { incidents } from '@shared/schema';
import { CreateIncident } from '@shared/schema.types';

// In client code
import { CreateIncidentSchema } from '@shared/schema.types';
```
