---
description: Content: Drizzle ORM schema definitions, ID strategy (CUID2), common fields, enum definitions, relationships, Zod validation, multi-tenancy, audit trails Purpose: Comprehensive guide for database schema design and implementation Key sections: Schema design, ID strategy, common fields, enums, relationships, validation When to use: Database schema design, table definitions, relationships, validation
globs: 
alwaysApply: false
---
# Schema Design Guidelines

## Core Schema Files
- `schema.ts` - Database table and enum definitions
- `schema.types.ts` - TypeScript interfaces and Zod validation schemas

## Database Schema Design
- Use Drizzle ORM's schema definition system
- Tables are defined using `pgTable`
- Enums are defined using `pgEnum`
- Indexes are defined inline with tables
- Use appropriate field types for data

## ID Strategy
- Use CUID2 for generating unique IDs
- Default function: `$defaultFn(() => createId())`
- Primary keys are string-based using varchar type

## Common Fields
- `id` - Primary key using CUID2
- `upkeepCompanyId` - Multi-tenant isolation field
- `createdAt` - Creation timestamp with default of current time
- `updatedAt` - Last update timestamp
- `deletedAt` - Soft deletion timestamp (null if not deleted)

## Enum Definitions
- Define constrained values as PostgreSQL enums 
- Examples: status, severity, incident categories, etc.
- Include all possible values with descriptive names
- Document values with comments

## Relationships
- Define foreign keys using `.references()`
- Use junction tables for many-to-many relationships
- Ensure appropriate indexes on foreign key fields

## Zod Schema Definitions
- Define validation schemas with Zod in `schema.types.ts`
- Match validation rules to database constraints
- Export TypeScript types derived from Zod schemas

## Multi-tenancy
- All tables should include `upkeepCompanyId` for data isolation
- All queries should filter by company ID

## Audit Trails
- Use the `auditTrail` table for tracking data changes
- Include entity ID, user ID, action type, and timestamp
- Store JSON snapshots of data for historical reference

## Example Schema Definition
```typescript
// Table definition example
export const incidents = pgTable(
  "incidents",
  {
    id: varchar("id", { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar("upkeep_company_id", { length: 10 })
      .notNull(),
    title: varchar("title", { length: 255 }).notNull(),
    description: text("description"),
    status: statusEnum("status").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at"),
    deletedAt: timestamp("deleted_at"),
  },
  (table) => [
    index("incident_title_index").on(table.title),
    index("incident_status_index").on(table.status),
  ]
);
```
