---
description: Content: Shared code organization, database schema definitions, TypeScript types, validation schemas, schema usage patterns, best practices for code sharing Purpose: Manages code shared between client and server for type safety and consistency Key sections: Schema design, type definitions, schema usage, best practices When to use: Schema definitions, type sharing, validation, consistency
globs: 
alwaysApply: false
---
# Shared Code Guidelines

## Overview
- The `shared/` directory contains code shared between client and server
- Primary focus on database schema, types, and validation
- Ensures type safety and consistency across frontend and backend

## Key Files
- `schema.ts` - Database schema definitions using Drizzle ORM
- `schema.types.ts` - TypeScript types derived from the schema
- `router.types.ts` - tRPC router type definitions

## Schema Design
- Database tables are defined using <PERSON><PERSON>zle ORM's schema builder
- Enums are defined for constrained values (status, severity, etc.)
- Indexes are defined for performance on frequently queried fields
- Schema includes documentation comments for clarity

## Type Definitions
- Types are exported for use in both client and server code
- Input validation schemas are defined using Zod
- Types should be kept in sync with the database schema

## Schema Usage
- Client and server code should import types from shared files
- Do not duplicate type definitions between client and server
- Use Zod schemas for input validation in API endpoints

## Example Usage
```typescript
// Importing schema and types
import { incidents } from '@shared/schema';
import { CreateIncident } from '@shared/schema.types';

// Using schema for database operations
const result = await db
  .select()
  .from(incidents)
  .where(eq(incidents.id, id));

// Using types for function parameters
function createIncident(input: CreateIncident, userId: string) {
  // Implementation here
}
```

## Best Practices
- Keep schema definitions DRY (Don't Repeat Yourself)
- Document complex schema fields with comments
- Use strict types to prevent runtime errors
- Consider backward compatibility when modifying schemas
- Keep validation logic in shared code when possible
