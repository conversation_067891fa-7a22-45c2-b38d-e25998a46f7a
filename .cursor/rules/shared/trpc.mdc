---
description: Content: tRPC implementation for type-safe API communication, server-side procedures, client-side hooks, input validation, error handling, context management Purpose: Guides the implementation of type-safe API communication between client and server Key sections: Server implementation, client implementation, procedure types, validation, error handling When to use: API development, type safety, client-server communication
globs: 
alwaysApply: false
---
# tRPC Implementation Guidelines

## Overview
- tRPC provides type-safe API communication between client and server
- Shared type definitions ensure consistency across the application
- Server defines procedures, client consumes them with full type safety

## Server-Side Implementation
- Base configuration in `server/trpc/trpc.ts`
- Router files in `server/trpc/router/`
- Each domain has its own router file (incident.router.ts, ai.router.ts, etc.)
- All procedures use middleware for logging and context enrichment

## Client-Side Implementation
- Client configuration in `client/src/providers/trpc.ts`
- Provider component wraps the application in `App.tsx`
- React hooks for data fetching and mutation

## Procedure Types
- **Query**: For retrieving data (GET operations)
- **Mutation**: For modifying data (POST, PUT, DELETE operations)
- **Subscription**: For real-time updates (not currently used)

## Input Validation
- Use Zod schemas for input validation
- Define schemas in `shared/schema.types.ts`
- Ensures runtime type safety and validation

## Error Handling
- Use `TRPCError` for standardized error responses
- Include appropriate error codes and messages
- Client can handle errors in a type-safe manner

## Context
- Request context contains user information
- Context is created via `createTrpcContext`
- Middleware enriches context with user data
- Context is available in all procedures

## Example Server Router
```typescript
export const incidentRouter = trpc.router({
  // Query procedure example
  getById: loggedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return await getIncidentById(input.id, ctx.user);
    }),

  // Mutation procedure example
  create: loggedProcedure
    .input(CreateIncidentSchema)
    .mutation(async ({ input, ctx }) => {
      return await createIncident(input, ctx.user);
    }),
});
```

## Example Client Usage
```typescript
// Using a query
const { data, isLoading, error } = api.incident.getById.useQuery({ id: "123" });

// Using a mutation
const mutation = api.incident.create.useMutation();
const handleSubmit = async (data) => {
  await mutation.mutateAsync(data);
};
```

## Best Practices
- Keep routers organized by domain/feature
- Use appropriate procedure types (query vs mutation)
- Implement proper error handling
- Leverage middleware for cross-cutting concerns
- Maintain clear procedure naming conventions
