---
description: Content: Project architecture overview, repository structure explanation, cross-cutting concerns, integration points, and development workflow Purpose: Serves as the main entry point for understanding the EHS project structure and how different components interact Key sections: Project placement in UpKeep ecosystem, monorepo structure (/client, /server, /shared), tRPC integration, authentication via UpKeep, CSP headers for iframe embedding When to use: Project onboarding, architectural decisions, understanding component relationships
globs: 
alwaysApply: false
---
# EHS Project Overview

## Project Overview
- UpKeep EHS is an integrated safety management solution with monorepo architecture
- The project contains both frontend and backend code in a single repository
- Primary focus: Providing a comprehensive safety incident management, OSHA compliance, and AI-assisted analysis solution
- Configured with Nginx to run at path "/ehs" on UpKeep domains (https://{app/local}.onupkeep.com/ehs)

## Placement
This app is part of the larger upkeep CMMS system, and is gonna be accessible on /ehs subroute proxied through nginx

## Repository Structure
- `/client/` - Frontend application code ([Client Rules](mdc:.cursor/rules/client/client.mdc))
- `/server/` - Backend server code ([Server Rules](mdc:.cursor/rules/server/server.mdc))
- `/shared/` - Common schemas and types shared between client and server ([Shared Rules](mdc:.cursor/rules/shared/shared.mdc))

## Cross-Cutting Concerns
- [Technology Stack](mdc:.cursor/rules/tech-stack.mdc)
- [Coding Conventions](mdc:.cursor/rules/coding-conventions.mdc)
- [Security Practices](mdc:.cursor/rules/security.mdc)
- [Feature Priorities](mdc:.cursor/rules/feature-priorities.mdc)

## Getting Started
- To understand the client-side implementation, see [Client Rules](mdc:.cursor/rules/client/client.mdc)
- To understand the server-side implementation, see [Server Rules](mdc:.cursor/rules/server/server.mdc)
- To understand the shared code, see [Shared Rules](mdc:.cursor/rules/shared/shared.mdc)
- For database design, see [Database Design](mdc:.cursor/rules/server/database.mdc)

## Integration Points
- tRPC connects frontend and backend for type-safe communication
- Client and server share schema definitions from `/shared/` directory
- Authentication is handled via UpKeep's authentication system
- Content Security Policy headers are set for secure iframe embedding

## Development Workflow
- Follow the [Coding Conventions](mdc:.cursor/rules/coding-conventions.mdc)
- Implement features according to [Feature Priorities](mdc:.cursor/rules/feature-priorities.mdc)
- Ensure all security measures are applied as defined in [Security Practices](mdc:.cursor/rules/security.mdc)
- Use appropriate technology as defined in [Technology Stack](mdc:.cursor/rules/tech-stack.mdc)

