---
description: Content: TypeScript conventions, API validation patterns, logging practices, error handling, documentation standards, and code organization principles Purpose: Maintains code quality and consistency across the development team Key sections: Strict typing, Zod schemas, tRPC patterns, JSDoc comments, barrel exports, enum usage When to use: Code reviews, writing new features, refactoring, maintaining consistency
globs: 
alwaysApply: false
---
# Coding Conventions

The EHS project follows these coding conventions to ensure consistency and maintainability:

## General Principles
- Use TypeScript with strict typing throughout the codebase
- Follow consistent naming conventions across all files
- Write clear, self-documenting code with appropriate comments
- Keep functions small and focused on a single responsibility
- Use async/await for asynchronous operations
- Organize related functionality in modules

## TypeScript Conventions
- Use strict typing whenever possible
- Prefer interfaces over types for object definitions
- Use generics to create reusable components and functions
- Always define return types for functions
- Use enums for fixed sets of values

## API and Data Validation
- Use Zod for schema validation and type safety
- Define all API input and output schemas using Zod
- Use tRPC with logging middleware for API communication
- Implement proper error handling with tRPC error utilities
- Use SuperJSON for proper serialization of dates and other complex types

## Logging and Error Handling
- Use the logger utility for structured logging
- Always catch and handle errors appropriately
- Use specific error types and messages
- Include context information in error logs
- Use try-catch blocks for error handling

## Documentation
- Include JSDoc comments for public functions
- Document complex logic with inline comments
- Keep documentation up-to-date with code changes
- Document API endpoints and their expected inputs/outputs

## Code Organization
- Group related functionality in the same directory
- Follow the project's file structure patterns
- Import from index files where appropriate
- Use barrel exports for cleaner imports
- Always check for enums, types and only use existing options before creating new ones
