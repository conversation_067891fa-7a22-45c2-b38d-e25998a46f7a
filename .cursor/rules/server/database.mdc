---
description: Content: PostgreSQL/Drizzle ORM patterns, schema structure, query examples, dynamic filtering techniques, data access best practices, multi-tenant isolation Purpose: Complete guide for database operations and query patterns using Drizzle ORM Key sections: Schema structure, query patterns, dynamic conditions with and(...conditions), soft deletes, transactions, multi-tenancy When to use: Database queries, schema design, data filtering, performance optimization
globs: 
alwaysApply: false
---
# Database Design and Operations

## Database Overview
- PostgreSQL database for persistent storage
- Drizzle ORM for type-safe database operations
- Schema defined in `shared/schema.ts`

## Schema Structure
- Core entities: incidents, capas (Corrective and Preventive Actions), oshaLogs
- Junction tables for many-to-many relationships
- Audit trail table for tracking changes

## Key Tables
- `incidents` - Safety incident records
- `capas` - Corrective and preventive actions
- `auditTrail` - Tracking changes for compliance and history
- `oshaLogs` - OSHA compliance reporting records
- `incidentsUsers` - Junction table linking incidents to involved users
- `incidentsOsha` - Junction table linking incidents to OSHA logs

## Database Access Patterns
- All database operations should go through the Drizzle ORM
- Avoid raw SQL queries when possible
- Use the `db` instance imported from `server/db/index.ts`

## Query Patterns
- Use Drizzle's query builder for type-safe operations
- Leverage indexes for performance on frequently queried fields
- Implement filtering using Drizzle's conditional operators

## Example Query Patterns
```typescript
// SELECT query with filtering
const incidents = await db
  .select()
  .from(incidents)
  .where(
    and(
      eq(incidents.upkeepCompanyId, companyId),
      isNull(incidents.deletedAt)
    )
  );

// INSERT with returning values
const inserted = await db
  .insert(incidents)
  .values({
    title: "New Incident",
    description: "Description here",
    // other fields...
  })
  .returning();

// UPDATE query
await db
  .update(incidents)
  .set({
    title: "Updated Title",
    updatedAt: new Date(),
  })
  .where(eq(incidents.id, incidentId));

// Soft delete pattern
await db
  .update(incidents)
  .set({
    deletedAt: new Date(),
  })
  .where(eq(incidents.id, incidentId));
```

## Dynamic Query Conditions with Drizzle
- Each `.where()` call **overwrites** the previous condition
- To build dynamic filters, accumulate conditions in an array and use `and(...conditions)`

```typescript
const conditions = [eq(incidents.upkeepCompanyId, companyId)];

if (!archived) {
  conditions.push(eq(incidents.archived, false));
}

if (search) {
  conditions.push(
    or(
      ilike(incidents.title, `%${search}%`),
      ilike(incidents.description, `%${search}%`),
      ilike(incidents.slug, `%${search}%`)
    )
  );
}

// other conditionals...

const query = db
  .select()
  .from(incidents)
  .where(and(...conditions));
```

## Data Access Best Practices
- Always filter by company ID for multi-tenant data isolation
- Use soft deletes (deletedAt field) rather than actually deleting records
- Include audit trail entries for data modifications
- Use transactions for operations that modify multiple tables
- Implement proper error handling for database operations
