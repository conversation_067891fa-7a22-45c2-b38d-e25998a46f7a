---
description: Content: Structured logging implementation, log levels, what to log, context inclusion, tRPC middleware logging, sensitive information handling, performance considerations Purpose: Ensures comprehensive and consistent logging across the application Key sections: Logger implementation, log levels, tRPC logging, sensitive data handling, audit trail vs logging When to use: Debugging, monitoring, audit requirements, error tracking
globs: 
alwaysApply: false
---
# Logging Guidelines

## Overview
- Comprehensive logging is essential for debugging, monitoring, and auditing
- All significant application events should be logged
- Logs should include relevant context information

## Logger Implementation
- The application uses a structured logger defined in `@server/utils/logger/index.ts`
- The logger is built on top of a logging library with support for different log levels
- Logs are outputted in a structured format (JSON) for easy parsing

## Log Levels
- **ERROR**: For errors that prevent normal operation
- **WARN**: For potential issues that don't prevent operation
- **INFO**: For general operational information
- **DEBUG**: For detailed debugging information (development only)

## What to Log
- API requests and responses (via middleware)
- Database operations (particularly writes)
- Authentication events (login, logout, auth failures)
- Business logic events (creating/updating/deleting entities)
- Error conditions and exceptions
- Performance information for slow operations

## Log Context
- Include relevant IDs (user ID, entity ID, request ID)
- Include operation type/name
- Include timestamps
- For errors, include error messages and stack traces
- Include performance metrics where relevant (duration)

## tRPC Logging
- All tRPC procedures are automatically logged via middleware
- Logs include procedure path, type, duration, and error info
- The middleware is applied via the `loggedProcedure` wrapper

## Example Logging Code
```typescript
// Importing the logger
import { logger } from '@server/utils/logger';

// Logging an informational event
logger.info('User registered', {
  userId: user.id,
  email: user.email,
  companyId: user.roleId,
});

// Logging an error with context
try {
  // Operation that might fail
} catch (error) {
  logger.error('Failed to create incident', {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    userId: ctx.user.id,
    incidentData: input,
  });
  
  // Rethrow or handle the error
}
```

## Sensitive Information
- Never log sensitive information (passwords, tokens, PII)
- Mask or redact sensitive fields in log output
- Be careful with error objects that might contain sensitive data

## Performance Considerations
- Log level should be configurable based on environment
- Avoid excessive logging in production (particularly DEBUG level)
- Consider log rotation and retention policies

## Audit Trail vs. Logging
- Logging is for operational monitoring and debugging
- The audit trail (database table) is for business event history
- Critical business events should be recorded in both
- The audit trail focuses on who did what and when
