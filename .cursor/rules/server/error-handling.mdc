---
description: Content: TRPCError usage, standardized error codes, service layer error handling, database error handling, logging errors, client-side error handling Purpose: Provides comprehensive error handling strategies for robust application behavior Key sections: Error codes, service layer patterns, database errors, logging, client handling When to use: Error management, API error responses, debugging, user experience
globs: 
alwaysApply: false
---
# Error Handling Guidelines

## Overview
- Robust error handling is critical for maintaining application stability
- Errors should be properly caught, logged, and returned to the client
- Use standardized error types and status codes

## tRPC Error Handling
- Use `TRPCError` for all API errors
- Specify appropriate error codes based on the error type
- Include descriptive error messages
- Example:
  ```typescript
  throw new TRPCError({
    code: 'NOT_FOUND',
    message: `Incident with ID ${id} not found`,
  });
  ```

## Error Codes
Use these standard tRPC error codes:
- `BAD_REQUEST`: Invalid input (400)
- `UNAUTHORIZED`: Not authenticated (401)
- `FORBIDDEN`: Not authorized (403)
- `NOT_FOUND`: Resource not found (404)
- `TIMEOUT`: Request timeout (408)
- `CONFLICT`: Resource conflict (409)
- `PRECONDITION_FAILED`: Precondition failed (412)
- `PAYLOAD_TOO_LARGE`: Request body too large (413)
- `UNPROCESSABLE_CONTENT`: Input validation failed (422)
- `TOO_MANY_REQUESTS`: Rate limit exceeded (429)
- `INTERNAL_SERVER_ERROR`: Unexpected error (500)

## Service Layer Error Handling
- Errors in service layer should be caught and converted to appropriate tRPC errors
- Include context in error messages (entity IDs, operation names, etc.)
- Use try/catch blocks for async operations that may fail

## Database Error Handling
- Handle database errors with specific error types
- Convert database errors to appropriate tRPC errors
- Log detailed information for debugging

## Logging Errors
- Log all errors with appropriate severity level
- Include stack traces for unexpected errors
- Add context information to error logs
- Use structured logging format

## Example Implementation
```typescript
// Router error handling
getById: loggedProcedure
  .input(z.object({ id: z.string() }))
  .query(async ({ input, ctx }) => {
    try {
      const incident = await getIncidentById(input.id, ctx.user);
      
      if (!incident) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Incident with ID ${input.id} not found`,
        });
      }
      
      return incident;
    } catch (error) {
      // If it's already a TRPCError, rethrow it
      if (error instanceof TRPCError) {
        throw error;
      }
      
      // Log unexpected errors
      logger.error('Error fetching incident', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        incidentId: input.id,
        userId: ctx.user.id,
      });
      
      // Convert to internal server error
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      });
    }
  });
```

## Client-Side Error Handling
- tRPC provides type-safe error handling on the client
- Use the error information to display appropriate messages
- Example:
  ```typescript
  const { data, isLoading, error } = api.incident.getById.useQuery({ id });
  
  if (error) {
    if (error.data?.code === 'NOT_FOUND') {
      return <div>Incident not found</div>;
    }
    return <div>An error occurred: {error.message}</div>;
  }
  ```
