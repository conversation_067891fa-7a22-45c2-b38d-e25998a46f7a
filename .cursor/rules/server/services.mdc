---
description: Content: Service layer organization, business logic encapsulation, database access patterns, error handling, audit trail implementation, multi-tenant isolation Purpose: Guides the implementation of the service layer for business logic separation Key sections: Service structure, database access, error handling, audit trails, example implementations When to use: Business logic implementation, service creation, data operations
globs: 
alwaysApply: false
---
# Service Layer Guidelines

## Overview
- Services encapsulate the business logic of the application
- Located in `server/services/` directory
- Each domain entity has its own service module
- Services are consumed by tRPC routers

## Service Organization
- **Incident Service**: Manages safety incidents (`incident.service.ts`)
- **User Service**: Handles user authentication and data (`user.service.ts`)
- **AI Service**: Provides AI-assisted analysis (`ai.service.ts`)

## Service Structure
- Exported functions for different operations
- Clear input and output typing
- Error handling with appropriate error types
- Logging for important operations
- Audit trail records for data modifications

## Database Access
- Services use the Drizzle ORM for database operations
- Import the `db` instance from `server/db/index.ts`
- Use Drizzle's query builder for type-safe operations
- Always filter queries by company ID for multi-tenant isolation

## Error Handling
- Use descriptive error messages
- Throw appropriate error types (e.g., TRPCError)
- Include context information in errors
- Log errors with details

## Audit Trail
- Important data modifications should be recorded in the audit trail
- Include entity ID, user ID, action type, and timestamp
- Store a JSON snapshot of the data for historical reference

## Example Service Implementation
```typescript
/**
 * Get incident by ID
 * 
 * @param id The ID of the incident to retrieve
 * @param user The authenticated user
 * @param logView Whether to log this view in the audit trail
 * @returns The incident or undefined if not found
 */
export const getIncidentById = async (id: string, user: User, logView = true) => {
  // Fetch the incident that matches the ID and company ID, and is not deleted
  const result = await db
    .select()
    .from(incidents)
    .where(
      and(
        eq(incidents.id, id),
        eq(incidents.upkeepCompanyId, user.roleId),
        isNull(incidents.deletedAt)
      )
    );

  // Return undefined if not found
  if (result.length === 0) {
    return undefined;
  }

  // Log the view in the audit trail if requested
  if (logView) {
    await db.insert(auditTrail).values({
      entityId: id,
      entityType: 'incident',
      details: JSON.stringify({ action: 'viewed' }),
      userId: user.id,
      upkeepCompanyId: user.roleId,
      timestamp: new Date(),
      action: 'viewed',
    });
  }

  // Return the incident
  return result[0];
};
```

## Best Practices
- Keep service functions focused on a single responsibility
- Use descriptive function names and documentation
- Implement proper error handling and logging
- Include audit trail records for data modifications
- Validate inputs at the service level
- Return appropriate data structures
- Ensure multi-tenant data isolation
- Use transactions for operations that modify multiple tables
