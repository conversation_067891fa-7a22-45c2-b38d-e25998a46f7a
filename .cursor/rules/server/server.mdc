---
description: Content: Node.js/Express architecture, tRPC setup, service layer patterns, database operations with Drizzle ORM, authentication via UpKeep, logging practices, audit trails, and coding practices Purpose: Comprehensive guide for backend development covering all aspects of server-side architecture Key sections: Directory structure, tRPC implementation, service layer pattern, database operations, error handling, authentication, logging, audit trails When to use: Backend development, API design, service implementation, database operations
globs: 
alwaysApply: false
---
# Server Architecture Guidelines

## Overview
- The server is built with Node.js and Express
- Uses tRPC for type-safe API communication with the client
- PostgreSQL database with Drizzle ORM for data persistence
- Integrates with UpKeep's authentication system

## Key Directories and Files
- `server/trpc/` - tRPC router definition and middleware
- `server/services/` - Business logic and service implementations
- `server/db/` - Database connection and configuration
- `server/utils/` - Utility functions including logging and request utilities
- UpKeep integration is distributed across services (user.service.ts, asset.service.ts, location.service.ts) and utils (requestUtils.ts)

## tRPC Implementation
- All API endpoints are defined using tRPC routers
- Base configuration is in `server/trpc/trpc.ts`
- Router files are in `server/trpc/router/`
- Each domain area has its own router file (incident.router.ts, ai.router.ts, etc.)
- All procedures use the `loggedProcedure` which includes logging middleware

## Service Layer Pattern
- Business logic is organized in service modules in `server/services/`
- Each service module corresponds to a domain entity (incident.service.ts, user.service.ts, etc.)
- Services handle data validation, database operations, and business rules
- Error handling is implemented at the service level
- Services should log important actions and include audit trails when appropriate

## Database Operations
- Database access is through the Drizzle ORM
- Schema definitions are in the shared directory (`shared/schema.ts`)
- The database connection is configured in `server/db/index.ts`
- Use Drizzle query builders for type-safe database operations

## Error Handling
- Use tRPC error types for API errors (`TRPCError`)
- Always include appropriate error codes and descriptive messages
- Log errors with appropriate context information
- Use try/catch blocks for async operations that may fail

## Authentication and Authorization
- Authentication is handled via UpKeep's authentication system
- User information is retrieved in the tRPC context middleware
- Authorization checks should be implemented in service methods

## Logging
- Comprehensive logging is implemented via the logger utility
- All tRPC procedures automatically log start/end and duration
- Include contextual information in logs (user IDs, entity IDs, etc.)
- Log sensitive operations (create, update, delete) for audit purposes

## Audit Trail
- Important operations should write to the audit trail table
- Include entity ID, user ID, action type, and timestamp
- Store a JSON snapshot of the data for historical reference

## Coding Practices
- Follow TypeScript best practices with appropriate typing
- Use async/await for asynchronous operations
- Implement comprehensive error handling
- Keep functions and modules focused on a single responsibility
