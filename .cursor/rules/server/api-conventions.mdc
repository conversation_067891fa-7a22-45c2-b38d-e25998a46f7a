---
description: Content: Strict API payload structure rules (flat objects only), router implementation patterns, validation rules, enforcement checklist, AI instructions for maintaining standards Purpose: Enforces consistent API design patterns and prevents common anti-patterns Key sections: Flat payload requirements, prohibited patterns (no try-catch in routers), TRPCError usage, schema definitions When to use: API endpoint development, code reviews, maintaining API consistency
globs: 
alwaysApply: false
---
# API Conventions

## Payload Structure
- Use flat object structures for all API payloads
- Keep all fields at the root level of the request object
- Avoid nested data structures unless absolutely necessary

### Example - Preferred Flat Structure ✅
```typescript
// Update endpoint payload
{
  "id": "01HQ2X5NMXJ6RJKZ3ZQBW4N8D2",
  "name": "Main Entrance QR Code",
  "description": "QR code for tracking entry/exit",
  "locationId": "1234567890",
  "status": "active"
}
```

### Example - Avoid Nested Structure ❌
```typescript
// Avoid this structure
{
  "id": "01HQ2X5NMXJ6RJKZ3ZQBW4N8D2",
  "data": {
    "name": "Main Entrance QR Code",
    "description": "QR code for tracking entry/exit",
    "locationId": "1234567890",
    "status": "active"
  }
}
```

## Router Implementation Rules

### REQUIRED_PATTERNS
```typescript
// These patterns MUST be followed in all router files

// 1. Flat Payload Structure
✅ DO:
.input(UpdateEntitySchema.and(z.object({ id: z.string() })))
.mutation(async ({ input }) => {
  const { id, ...data } = input;
  return await EntityService.update(id, data);
})

❌ DON'T:
.input(z.object({
  id: z.string(),
  data: UpdateEntitySchema
}))

// 2. Error Handling
✅ DO:
.query(async ({ input, ctx }) => {
  if (!input?.id) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'ID is required'
    });
  }
  return await EntityService.getById(input.id, ctx.user);
})

❌ DON'T:
.query(async ({ input }) => {
  try {
    return await EntityService.getById(input.id);
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to get entity'
    });
  }
})
```

### VALIDATION_RULES
1. NO try-catch blocks in router files
2. ALL error handling through TRPCError
3. ALL payloads must be flat objects
4. NO nested data structures in input schemas
5. ALL business logic must be in services

### ENFORCEMENT_CHECKLIST
When modifying router files, verify:
- [ ] No try-catch blocks present
- [ ] TRPCError used for business logic conditions
- [ ] Input schemas are flat (no nested data objects)
- [ ] Service calls are direct (no wrapping in try-catch)
- [ ] Error messages are specific and descriptive

### AI_INSTRUCTIONS
When assisting with router files:
1. ALWAYS check for and remove try-catch blocks
2. ALWAYS flatten nested input schemas
3. ALWAYS use TRPCError for business logic conditions
4. NEVER wrap service calls in try-catch
5. ALWAYS provide specific error messages

## Schema Definition
- Define base schema using `createUpdateSchema` or `createInsertSchema`
- Add additional fields using `.and()` when needed
- Keep validation rules in the schema definition
- Use descriptive error messages in schema validations
