---
description: Purpose: Complete backend development guide emphasizing strict API conventions and service architecture When to Use: Server development, API implementation, backend architecture, ensuring API convention compliance
globs: 
alwaysApply: false
---
# Server Implementation Guidelines

## Architecture Overview
- Node.js/Express server with tRPC for type-safe API communication
- PostgreSQL database with Drizzle ORM
- Service-oriented architecture with clear separation of concerns
- Integration with UpKeep's authentication system

## Key Directories
- `server/trpc/` - API router definitions and middleware
- `server/services/` - Business logic implementation
- `server/db/` - Database connection and utilities
- `server/utils/` - Shared utilities including logging and request utilities
- UpKeep integration is distributed across services (user.service.ts, asset.service.ts, location.service.ts) and utils (requestUtils.ts)

## tRPC Implementation
- Define procedures in domain-specific router files (incident.router.ts, etc.)
- Use `loggedProcedure` for all endpoints (includes logging middleware)
- STRICTLY follow router patterns in api-conventions.mdc
- NO try-catch blocks in routers (see REQUIRED_PATTERNS in api-conventions.mdc)
- ALL payloads must be flat (see VALIDATION_RULES in api-conventions.mdc)
- Use TRPCError for business logic conditions
- Follow ENFORCEMENT_CHECKLIST in api-conventions.mdc for all router changes

## Service Layer
- Business logic goes in service modules in `server/services/`
- Services handle data validation, business rules, and database operations
- Each domain entity has a dedicated service (incident.service.ts, etc.)
- Include audit trail entries for important data operations
- Handle low-level errors and data transformations

## Database Access Patterns
- Use Drizzle ORM for all database operations
- Always filter by company ID for multi-tenant isolation
- Use soft deletes (deletedAt field) instead of permanent deletion
- Use transactions for operations that modify multiple tables

## Error Handling
- Follow REQUIRED_PATTERNS in api-conventions.mdc
- Use TRPCError for business logic conditions
- Let tRPC handle unexpected errors
- Use appropriate error codes and descriptive messages
- Handle low-level errors in services, not routers
- Log errors with proper context and stack traces

## Logging and Auditing
- Log all significant operations with relevant context
- Include user ID, entity ID, and other relevant data in log entries
- Use appropriate log levels (ERROR, WARN, INFO, DEBUG)
- Write audit trail entries for data modifications

## UpKeep Integration
- Use the UpKeep API for user authentication and data sharing
- User data comes from `/api/v1/users/me` endpoint
- Forward necessary headers from client requests to UpKeep API
- Handle API communication errors gracefully

## Example Patterns

### Router Definition (Following api-conventions.mdc)
```typescript
export const incidentRouter = trpc.router({
  getById: loggedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      if (!input?.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Incident ID is required',
        });
      }
      return await getIncidentById(input.id, ctx.user);
    }),
});
```

### Service Method
```typescript
export const getIncidentById = async (id: string, user: User) => {
  return await db
    .select()
    .from(incidents)
    .where(
      and(
        eq(incidents.id, id),
        eq(incidents.upkeepCompanyId, user.roleId),
        isNull(incidents.deletedAt)
      )
    )
    .then(results => results[0] || undefined);
};
```

### Logging Example
```typescript
logger.info('Creating new incident', {
  userId: user.id,
  companyId: user.roleId,
  incidentTitle: incident.title
});
```
