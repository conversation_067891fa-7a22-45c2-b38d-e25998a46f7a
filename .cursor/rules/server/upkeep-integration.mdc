---
description: Content: UpKeep API integration, authentication system, user management, multi-tenancy, environment configuration, API client setup, security considerations Purpose: Manages integration with the main UpKeep system for authentication and data sharing Key sections: Authentication flow, user management, API calls, multi-tenancy, security When to use: UpKeep integration, authentication, user data, multi-tenant setup
globs: 
alwaysApply: false
---
# UpKeep Integration Guidelines

## Overview
- EHS module integrates with the main UpKeep system
- Integration points include authentication, user management, and data sharing
- APIs are used to communicate between the systems

## Authentication
- EHS uses UpKeep's authentication system
- User sessions are validated via API calls to UpKeep
- Authentication headers are forwarded to UpKeep API for validation
- User information is retrieved from UpKeep's user API

## User Management
- Users are managed in the UpKeep system
- E<PERSON> retrieves user information via the `/api/v1/users/me` endpoint
- User role and company information come from UpKeep
- No separate user management in EHS

## API Integration
- API calls to UpKeep are made through the server
- Headers from client requests are forwarded to UpKeep
- Default headers are built using the `buildDefaultHeaders` utility

## Multi-tenancy
- EHS is multi-tenant, with each tenant corresponding to an UpKeep company
- Company ID from UpKeep (`roleId`) is used for data isolation
- All database queries filter by company ID

## Environment Configuration
- UpKeep API URL is configured in environment variables
- Set `UPKEEP_API_URL` to point to the appropriate UpKeep environment
- Local development typically uses `https://local.onupkeep.com`

## API Client
- Axios is used for making API calls to UpKeep
- Requests include authentication credentials
- The `withCredentials` flag is set to true for cookie-based auth

## Example UpKeep API Call
```typescript
// Retrieving user information from UpKeep
const getMe = async ({ headers }: { headers: Headers }): Promise<User> => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/users/me`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
  });

  const user = response.data.result as User;

  // Process and return user data
  return {
    id: user.id,
    username: user.username,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    roleId: user.roleId,
    groupName: user.groupName,
    groupId: user.groupId,
  };
};
```

## Error Handling
- Handle API communication errors gracefully
- Include timeouts for API calls
- Implement retry logic for transient failures
- Log API errors with appropriate context

## Security Considerations
- Only forward necessary headers to UpKeep API
- Don't expose sensitive UpKeep data or credentials
- Use HTTPS for all API communication
- Validate and sanitize data received from UpKeep

## Development Guidelines
- Use mock data for testing when developing locally
- Document UpKeep API dependencies
- Keep UpKeep API integration code separate from business logic
- Follow UpKeep API versioning and deprecation policies
