---
description: Content: Security practices covering authentication (UpKeep integration), API security (CORS, input validation), web security (CSP headers, XSS protection), data security, audit logging, and secure configuration Purpose: Ensures all security requirements and best practices are followed throughout development Key sections: Role-based access control, Zod validation, environment variables, audit trails, principle of least privilege When to use: Implementing auth features, API endpoints, data handling, compliance requirements
globs: 
alwaysApply: false
---
# Security Practices

The EHS project implements the following security practices to ensure data protection and system integrity:

## Authentication and Authorization
- Leverage UpKeep's authentication system
- Use proper role-based access control for all resources
- Implement middleware for secure access to protected resources
- Validate user permissions for each API endpoint

## API Security
- CORS protection for all API endpoints
- Input validation using Zod schemas for all API requests
- Proper error handling to avoid information leakage
- Rate limiting for public endpoints
- Use HTTPS for all communications

## Web Security
- Content Security Policy (CSP) headers for secure iframe embedding
- Environment-specific UPKEEP_APP_URL for CSP frame-ancestors
- XSS protection via proper escaping and <PERSON><PERSON>'s security features
- CSRF protection for state-changing operations

## Data Security
- Store sensitive information in environment variables
- Follow principle of least privilege for database operations
- Use prepared statements for all database queries
- Implement proper data validation before storage
- Sanitize all user inputs

## Audit and Logging
- Log all security-related events
- Maintain comprehensive audit trails for sensitive operations
- Ensure logs don't contain sensitive information
- Track all user actions for accountability

## Secure Configuration
- Follow the principle of secure defaults
- Use environment variables for configuration
- Store secrets securely and never in code
- Keep dependencies up-to-date with security patches
