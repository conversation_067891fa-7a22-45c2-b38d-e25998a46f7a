---
description: Content: Comprehensive technology inventory including versions - Node.js 20+, React 19, TypeScript 5.8+, PostgreSQL with Drizzle ORM, tRPC 11.1+, TailwindCSS, Radix UI, etc. Purpose: Ensures consistency in technology choices and version compatibility across the project Key sections: Core technologies, frontend stack, backend stack, development tools, AI integrations When to use: Package installations, dependency updates, technology decisions, build configurations
globs: 
alwaysApply: false
---
# Tech Stack

This is a comprehensive list of technologies used in this project:

## Core Technologies
- Runtime: Node.js 20+
- Package Manager: PNPM 10.11+
- Language: TypeScript 5.8+

## Frontend
- Framework: React 19
- Bundling: Vite 6.3+
- Routing: Wouter 3.7+
- Styling: TailwindCSS 4.1+
- UI Components: Radix UI
- Forms: React Hook Form 7.56+ with Zod validation
- State Management: TanStack Query (React Query) 5.76+
- Data Visualization: Recharts 2.15+
- Notifications: Sonner 2.0+
- Icons: Lucide React
- Animation: Motion

## Backend
- Server: Express 5.1+
- API: tRPC 11.1+
- Database: PostgreSQL with <PERSON><PERSON>zle ORM 0.43+
- Schema Management: Drizzle Kit
- Validation: Zod 3.24+
- Logging: Winston 3.17+
- Authentication: Custom implementation

## Development & Infrastructure
- Containerization: Docker
- Environment Variables: dotenv
- Date Handling: date-fns 4.1+
- HTTP Client: Axios 1.9+

## AI & Integration
- AI Integration: AI SDK with OpenAI 1.3+
- Upkeep Integration: Custom implementation

## Email & Communication
- Email Templates: Handlebars 4.7+
- Email Service: Mandrill API 1.0+

## File Storage & Cloud Services
- File Storage: AWS SDK S3 3.812+

## UI Enhancements
- Animations: Canvas Confetti 1.9+

## Utilities
- Data Serialization: SuperJSON
- ID Generation: CUID2
