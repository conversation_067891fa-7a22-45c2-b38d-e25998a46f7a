---
description: Content: Complete directory tree showing file organization from root to specific subdirectories, explaining the purpose of each major folder and file Purpose: Helps developers navigate the codebase and understand where different types of code should be placed Key sections: Client structure, server structure, shared code, configuration files, infrastructure When to use: File organization, creating new modules, understanding codebase layout
globs: 
alwaysApply: false
---
## Project Structure

ehs/
├── .git/                      # Git repository
├── .github/                   # GitHub workflows and configuration
├── .vscode/                   # VS Code configuration
├── client/                    # Frontend application
│   ├── index.html             # Entry HTML file
│   └── src/                   # Frontend source code
│       ├── api/               # API client configuration
│       ├── components/        # Reusable UI components
│       ├── contexts/          # React contexts
│       ├── constants/         # Application constants
│       ├── hooks/             # Custom React hooks
│       ├── lib/               # Utility libraries
│       ├── pages/             # Page components
│       ├── providers/         # Context providers
│       ├── App.tsx            # Main application component
│       ├── main.tsx           # Application entry point
│       ├── index.css          # Global CSS
│       └── styles.css         # Additional styles
├── server/                    # Backend application
│   ├── __tests__/             # Server integration tests
│   ├── db/                    # Database configuration and models
│   ├── services/              # Service layer
│   ├── trpc/                  # tRPC API endpoints
│   ├── upkeep/                # Upkeep integration
│   ├── utils/                 # Server utilities
│   └── index.ts               # Server entry point
├── shared/                    # Shared code between client and server
│   ├── router.types.ts        # tRPC router type definitions
│   ├── schema.ts              # Database schema definitions
│   └── schema.types.ts        # TypeScript types for the schema
├── coverage/                  # Test coverage reports
├── drizzle/                   # Database migrations
├── public/                    # Static assets
├── infra/                     # Infrastructure configuration
├── node_modules/              # Dependencies
├── .cursor/                   # Cursor editor configuration
├── env.ts                     # Environment variable validation
├── package.json               # Project dependencies and scripts
├── pnpm-lock.yaml            # PNPM lock file
├── tsconfig.json              # TypeScript configuration
├── vite.config.ts             # Vite configuration
├── drizzle.config.ts          # Drizzle ORM configuration
├── Dockerfile                 # Production Docker configuration
├── Dockerfile.dev             # Development Docker configuration
├── .gitignore                 # Git ignore file
├── .node-version              # Node.js version file
└── README.md                  # Project documentation