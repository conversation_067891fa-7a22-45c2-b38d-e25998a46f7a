---
description: Content: Comprehensive guide combining architecture patterns, technology stack, server guidelines (service layer, tRPC, database access), shared code patterns, and client guidelines with practical examples Purpose: One-stop reference for full-stack development patterns and examples Key sections: Complete code examples for server routers, schema definitions, client components, error handling patterns When to use: Comprehensive reference, learning patterns, implementing new features
globs: 
alwaysApply: false
---
---
rule_type: "Agent Requested"
description: "Content: Comprehensive guide combining architecture patterns, technology stack, server guidelines (service layer, tRPC, database access), shared code patterns, and client guidelines with practical examples Purpose: One-stop reference for full-stack development patterns and examples Key sections: Complete code examples for server routers, schema definitions, client components, error handling patterns When to use: Comprehensive reference, learning patterns, implementing new features"
when_to_use: "Comprehensive reference, learning patterns, implementing new features, full-stack development guidance, understanding project architecture, new developer onboarding, code reviews, architecture decisions"
---

# EHS Project Guidelines

## Project Overview
- UpKeep EHS is an integrated safety management solution with monorepo architecture
- Provides safety incident management, OSHA compliance, and AI-assisted analysis
- Uses tRPC for type-safe communication between client and server

## Architecture
- `/client/` - React frontend application
- `/server/` - Node.js/Express backend server
- `/shared/` - Common schemas and types shared between client and server

## Technology Stack
- **Frontend**: React, <PERSON><PERSON>er (routing), TailwindCSS, tRPC client
- **Backend**: Node.js, Express, tRPC server
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Integrated with UpKeep's auth system

## Server Guidelines

### Service Layer Pattern
- Business logic in service modules (`server/services/`)
- Each domain has its own service (incident.service.ts, etc.)
- Services handle validation, business rules, and DB operations

### tRPC Implementation
- Define procedures in domain router files (`server/trpc/router/`)
- Use `loggedProcedure` for all endpoints (includes logging middleware)
- Handle errors with `TRPCError` and appropriate status codes

### Database Access
- Use Drizzle ORM for type-safe database operations
- Always filter by company ID for multi-tenant isolation
- Use soft deletes instead of permanent deletion

### Error Handling & Logging
- Use descriptive error messages and appropriate error codes
- Log all significant operations with relevant context
- Include audit trail entries for data modifications

## Shared Code Guidelines

### Schema Design
- Tables defined with Drizzle ORM in `shared/schema.ts`
- Common fields: id (CUID2), upkeepCompanyId, timestamps
- Define indexes for frequently queried fields

### Type Definitions
- Export TypeScript types for use throughout the application
- Define validation schemas using Zod in `shared/schema.types.ts`
- Keep types in sync with database schema

## Client Guidelines

### Component Structure
- Feature-based organization in `/client/src/features/`
- Reusable UI components in `/client/src/components/`
- Custom hooks in `/client/src/hooks/`

### tRPC API Usage
- Configuration in `client/src/providers/trpc.ts`
- Provider wraps the application in `App.tsx`
- Use hooks for data fetching and mutations:
  ```typescript
  // Query data
  const { data, isLoading } = trpc.incident.getById.useQuery({ id });
  
  // Mutate data
  const mutation = trpc.incident.create.useMutation();
  const handleSubmit = (data) => mutation.mutateAsync(data);
  ```
- Handle loading, error, and success states appropriately
- Leverage React Query's caching and invalidation features

### Form Handling
- Use React Hook Form with Zod validation
- Connect form submissions to tRPC mutations
- Implement proper loading and error states

### State Management
- Use React Query for server state
- Use React Context for application state
- Keep local component state for UI-specific concerns

## Example Patterns

### Server Router
```typescript
export const incidentRouter = trpc.router({
  getById: loggedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const incident = await getIncidentById(input.id, ctx.user);
      if (!incident) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Incident with ID ${input.id} not found`,
        });
      }
      return incident;
    }),
});
```

### Schema Definition
```typescript
export const incidents = pgTable(
  "incidents",
  {
    id: varchar("id", { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar("upkeep_company_id", { length: 10 }).notNull(),
    title: varchar("title", { length: 255 }).notNull(),
    status: statusEnum("status").notNull(),
    // other fields...
  }
);
```

### Client Component
```typescript
function IncidentDetails({ id }: { id: string }) {
  // Type-safe API query
  const { data, isLoading, error } = trpc.incident.getById.useQuery({ id });

  // Handle loading/error states
  if (isLoading) return <Spinner />;
  if (error) return <ErrorDisplay error={error} />;

  // Render with type-safe data
  return (
    <div>
      <h1>{data.title}</h1>
      <p>Status: {data.status}</p>
      <p>Reported: {data.reportedAt.toLocaleDateString()}</p>
      {/* More incident details */}
    </div>
  );
}
```