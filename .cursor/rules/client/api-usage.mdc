---
description: Content: tRPC client setup, data fetching hooks, mutation hooks, AI service integration, error handling, context management, best practices with React Query Purpose: Complete guide for client-side API communication using tRPC Key sections: Setup, data fetching, mutations, AI integration, error handling, context management When to use: API integration, data fetching, mutations, error handling, state management
globs: 
alwaysApply: false
---
# Client-Side API Usage Guidelines

## Overview
- The client uses tRPC for type-safe API communication with the server
- tRPC provides automatically generated React hooks for queries and mutations
- Type definitions are shared between client and server for end-to-end type safety

## Setup and Configuration
- tRPC client is configured in `client/src/providers/trpc.ts`
- Provider component wraps the application in `App.tsx`
- SuperJSON transformer handles dates and other complex types

## Provider Setup
```typescript
// trpc.ts
export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    httpLink({
      url: '/ehs/trpc',
      transformer: superjson,
    }),
  ],
});

// App.tsx
<trpc.Provider client={trpcClient} queryClient={queryClient}>
  <AppContextProvider>
    {/* Application components */}
  </AppContextProvider>
</trpc.Provider>
```

## Data Fetching Hooks
- Use `useQuery` hooks for retrieving data
- Each router procedure is available as a strongly-typed hook
- Example:
  ```typescript
  // Fetch current user data
  const { data: user, isLoading } = trpc.user.me.useQuery();
  
  // Fetch incident by ID
  const { data: incident, error } = trpc.incident.getById.useQuery({ id: incidentId });
  ```

## Mutation Hooks
- Use `useMutation` hooks for data mutations (create, update, delete)
- All mutations are fully typed with appropriate input validation
- Example:
  ```typescript
  // Create a new incident
  const { mutateAsync: createIncident, isLoading } = trpc.incident.create.useMutation({
    onSuccess: (data) => {
      // Handle success (e.g., show toast, redirect)
      toast.success('Incident created successfully');
      navigate('/incidents');
    },
    onError: (error) => {
      // Handle error
      toast.error(`Error creating incident: ${error.message}`);
    },
  });
  
  // Usage in a form submission
  const handleSubmit = async (values) => {
    await createIncident(values);
  };
  ```

## AI Service Integration
- Use the AI router for AI-powered features
- Examples include:
  - `trpc.ai.analyze.useMutation()` - Analyze incident descriptions
  - `trpc.ai.transcribe.useMutation()` - Transcribe audio recordings

## Error Handling
- tRPC provides type-safe error handling
- Error responses include proper HTTP status codes and messages
- Handle loading, error, and success states appropriately
- Example:
  ```typescript
  const { data, isLoading, error } = trpc.incident.getById.useQuery({ id });
  
  if (isLoading) return <Spinner />;
  
  if (error) {
    if (error.data?.code === 'NOT_FOUND') {
      return <div>Incident not found</div>;
    }
    return <div>Error: {error.message}</div>;
  }
  
  return <IncidentDetail incident={data} />;
  ```

## Context Management
- Application context can use tRPC queries for global state
- Example in `app-context.tsx`:
  ```typescript
  const { data: user, isLoading } = trpc.user.me.useQuery();
  return (
    <AppContext.Provider value={{ user, isLoading }}>
      {children}
    </AppContext.Provider>
  );
  ```

## Best Practices
- Use query invalidation to refresh data after mutations
- Leverage TanStack Query features (caching, refetching, etc.)
- Keep API-related logic in custom hooks when reused across components
- Handle loading and error states consistently
- Use TypeScript to ensure type safety
- Implement proper error boundaries
