---
description: Content: React architecture, component structure, state management (React Query + hooks), data fetching, routing, error handling, accessibility, performance optimization Purpose: Comprehensive frontend development guide covering all aspects of React application development Key sections: File structure, state management, data fetching, routing, error boundaries, accessibility, performance When to use: Frontend development, React patterns, state management, performance optimization
globs: 
alwaysApply: false
---
# Client Rules

This document describes the client-side architecture and conventions for the EHS project.

## File Structure
- `/client/src/` - Frontend application source code
- `/client/src/components/` - React components
- `/client/src/pages/` - Page components and routes
- `/client/src/hooks/` - Custom React hooks
- `/client/src/utils/` - Frontend utility functions
- `/client/src/providers/` - Provider components (tRPC, etc.)
- `/client/src/contexts/` - React context providers
- `/client/src/lib/` - Utility libraries and configurations

## Component Structure
- [Component Guidelines](mdc:.cursor/rules/client/components.mdc)
- Use functional components with hooks
- Co-locate component styles with component files
- Keep components small and focused
- Implement proper prop validation

## State Management
- Use React Query for server state management
- Use React hooks (useState, useReducer) for local state
- Minimize prop drilling with context where appropriate
- Keep state as close to where it's used as possible

## Data Fetching
- Use tRPC hooks for all API communication
- Implement proper loading and error states
- Use React Query's caching capabilities
- Follow the [API Guidelines](mdc:.cursor/rules/client/api-guidelines.mdc)

## Routing
- Use Wouter for declarative routing
- Implement lazy loading for route components
- Handle authentication and authorization at the route level
- Provide fallback UI for loading states

## Error Handling
- Implement error boundaries for component errors
- Provide user-friendly error messages
- Log errors to the server when appropriate
- Handle network errors gracefully

## Accessibility
- Follow WCAG guidelines
- Implement proper keyboard navigation
- Use semantic HTML elements
- Provide appropriate ARIA attributes
- Ensure sufficient color contrast

## Performance
- Minimize bundle size with code splitting
- Use memoization where appropriate
- Optimize renders with useMemo and useCallback
- Implement virtualization for long lists
