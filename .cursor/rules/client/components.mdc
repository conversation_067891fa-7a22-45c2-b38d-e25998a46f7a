---
description: Content: Atomic design methodology, component organization, functional components with TypeScript, state management, props patterns, composition, styling guidelines, documentation Purpose: Guides the creation and organization of reusable React components Key sections: Atomic design, component implementation, state management, composition, styling, documentation When to use: Component development, UI architecture, design system implementation
globs: 
alwaysApply: false
---
# Component Guidelines

This document outlines the best practices for creating and organizing React components in the EHS project.

## Component Organization

### Atomic Design
Follow the atomic design methodology for component organization:
- **Atoms**: Basic building blocks (buttons, inputs, icons)
- **Molecules**: Groups of atoms (form fields, search bars)
- **Organisms**: Complex components (forms, tables, cards)
- **Templates**: Page layouts without specific content
- **Pages**: Complete pages with specific content

### File Structure
- One component per file
- Name the file after the component (PascalCase)
- Group related components in directories
- Use index files for exporting components

## Component Implementation

### Functional Components
- Use functional components with hooks
- Avoid class components
- Implement proper TypeScript typing for props
- Use destructuring for props and state

```typescript
// Good example
interface ButtonProps {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({ label, onClick, disabled = false }) => {
  return (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  );
};
```

### Component State
- Use `useState` for simple state
- Use `useReducer` for complex state
- Keep state minimal and focused
- Derive values where possible instead of duplicating state

### Props
- Use TypeScript interfaces for prop definitions
- Provide default values for optional props
- Keep required props to a minimum
- Use prop spreading sparingly and explicitly

### Component Composition
- Favor composition over inheritance
- Use the children prop for flexible components
- Implement render props pattern when needed
- Use higher-order components sparingly

## Styling Guidelines

### CSS Approach
- Use CSS modules for component styling
- Keep styles co-located with components
- Use consistent naming conventions
- Implement responsive design with mobile-first approach

### Theme and Design System
- Use theme variables for colors, spacing, etc.
- Follow the design system guidelines
- Maintain consistent visual language
- Support dark mode through theming

## Component Documentation
- Document component usage with comments
- Include prop descriptions and examples
- Document complex logic and edge cases
- Keep documentation up to date with changes
