---
description: Purpose: Complete frontend development reference for React-based EHS application When to Use: Frontend development, React patterns, UI implementation, client-side architecture decisions
globs: 
alwaysApply: false
---
# Client Implementation Guidelines

## Architecture Overview
- React-based frontend application
- tRPC for type-safe API communication with the server
- <PERSON><PERSON><PERSON>CS<PERSON> for styling
- Wouter for navigation
- React Query for data fetching and caching

## Directory Structure
- `/client/src/providers/` - Provider components (tRPC, etc.)
- `/client/src/components/` - Reusable UI components
- `/client/src/contexts/` - React context providers
- `/client/src/hooks/` - Custom React hooks
- `/client/src/pages/` - Page components for each route
- `/client/src/lib/` - Utility libraries and configurations

## tRPC Client Usage
- tRPC provides type-safe API communication with the server
- Configuration in `client/src/providers/trpc.ts`
- Provider wraps the application in `App.tsx`
- Use hooks for queries (`useQuery`) and mutations (`useMutation`)

```typescript
// Query example
const { data, isLoading, error } = trpc.incident.getById.useQuery({ id });

// Mutation example
const { mutateAsync, isLoading } = trpc.incident.create.useMutation({
  onSuccess: (data) => {
    toast.success('Incident created');
    navigate('/incidents');
  },
});
```

## State Management
- Use React Query for server state management
- Use React Context for application state
- Local component state for UI-specific state
- Example context usage:

```typescript
// Context provider
export const AppContextProvider = ({ children }) => {
  const { data: user, isLoading } = trpc.user.me.useQuery();
  return (
    <AppContext.Provider value={{ user, isLoading }}>
      {children}
    </AppContext.Provider>
  );
};

// Context usage in components
const { user, isLoading } = useAppContext();
```

## Component Design
- Follow a component hierarchy:
  - Layout components (page structure)
  - Feature components (business logic)
  - UI components (reusable UI elements)
- Use composition over inheritance
- Keep components focused on a single responsibility

## Styling Approach
- Use TailwindCSS for utility-first styling
- Consistent design system with variables for colors, spacing, etc.
- UI component library provides styled versions of common components
- Use responsive design patterns for different screen sizes

## Form Handling
- Use React Hook Form for form state and validation
- Zod schemas for validation rules
- Example form setup:

```typescript
const form = useForm<CreateIncident>({
  resolver: zodResolver(CreateIncidentSchema),
  defaultValues: {
    title: '',
    description: '',
    // other defaults...
  },
});

// Form submission
const onSubmit = (values: CreateIncident) => {
  mutateAsync(values);
};
```

## Error Handling
- Handle API errors with appropriate user feedback
- Use toast notifications for temporary messages
- Implement error boundaries for catching component errors
- Handle loading, error, and success states consistently

```typescript
if (isLoading) return <Spinner />;
if (error) return <ErrorDisplay error={error} />;
return <IncidentDetail incident={data} />;
```

## Routing
- Use Wouter for lightweight routing
- Define routes in a central location for maintainability
- Implement proper navigation with route parameters

```typescript
<Switch>
  <Route path={ROUTES.INCIDENT_NEW} component={NewIncident} />
  <Route path={ROUTES.INCIDENT_EDIT} component={EditIncident} />
  <Route path={ROUTES.INCIDENT_DETAILS} component={IncidentDetails} />
</Switch>
```

## Performance Considerations
- Use React.memo for expensive components
- Implement virtualization for long lists
- Optimize bundle size with code splitting
- Use proper dependency arrays for hooks

## Accessibility
- Implement proper semantic HTML
- Ensure keyboard navigation
- Provide alternative text for images
- Test with screen readers and keyboard-only navigation
- Follow WCAG guidelines
