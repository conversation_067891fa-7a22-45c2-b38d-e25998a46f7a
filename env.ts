import 'dotenv/config';

import { createEnv } from '@t3-oss/env-core';
import { z } from 'zod';

export const OpenAIModel = {
  GPT_4O_MINI: 'gpt-4o-mini',
  GPT_4O: 'gpt-4o',
  GPT_4_TURBO: 'gpt-4-turbo',
} as const;

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
    DATABASE_URL: z.string().url(),
    OPENAI_API_KEY: z.string().min(1),
    PORT: z.coerce.number().default(8594),
    UPKEEP_API_URL: z.string().url(),
    LOG_LEVEL: z.string().default('info'),
    ENVIRONMENT_PREFIX: z.string().default('local'),
    SHOW_LOGS: z.coerce.boolean().default(true),
    // Rate limiting configuration
    RATE_LIMIT_WINDOW_MS: z.coerce.number().default(60000), // 1 minute window
    RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(500), // requests per minute for public endpoints
    // AWS S3 configuration
    AWS_REGION: z.string().min(1),
    AWS_ACCESS_KEY_ID: z.string().min(1),
    AWS_SECRET_ACCESS_KEY: z.string().min(1),
    S3_BUCKET_NAME: z.string().min(1),
    // Mandrill configuration
    MANDRILL_API_KEY: z.string().min(1),
    MANDRILL_FROM_EMAIL: z.string().email(),
    MANDRILL_FROM_NAME: z.string().min(1),
    // Frontend URL for email links
    EHS_URL: z.string().url().default('http://local-ehs.onupkeep.com'),
    CAPAS_MODEL: z
      .enum([OpenAIModel.GPT_4O_MINI, OpenAIModel.GPT_4O, OpenAIModel.GPT_4_TURBO])
      .default(OpenAIModel.GPT_4_TURBO),
  },

  /**
   * What object holds the environment variables at runtime. This is usually
   * `process.env` or `import.meta.env`.
   */
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    PORT: process.env.PORT,
    UPKEEP_API_URL: process.env.UPKEEP_API_URL,
    ENVIRONMENT_PREFIX: process.env.ENVIRONMENT_PREFIX,
    SHOW_LOGS: process.env.SHOW_LOGS,
    LOG_LEVEL: process.env.LOG_LEVEL,
    // Rate limiting configuration
    RATE_LIMIT_WINDOW_MS: process.env.RATE_LIMIT_WINDOW_MS,
    RATE_LIMIT_MAX_REQUESTS: process.env.RATE_LIMIT_MAX_REQUESTS,
    // AWS S3 configuration
    AWS_REGION: process.env.AWS_REGION,
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    S3_BUCKET_NAME: process.env.S3_BUCKET_NAME,
    // Mandrill configuration
    MANDRILL_API_KEY: process.env.MANDRILL_API_KEY,
    MANDRILL_FROM_EMAIL: process.env.MANDRILL_FROM_EMAIL,
    MANDRILL_FROM_NAME: process.env.MANDRILL_FROM_NAME,
    // Frontend URL for email links
    EHS_URL: process.env.EHS_URL,
    CAPAS_MODEL: process.env.CAPAS_MODEL,
  },

  /**
   * By default, this library will feed the environment variables directly to
   * the Zod validator.
   *
   * This means that if you have an empty string for a value that is supposed
   * to be a number (e.g. `PORT=` in a ".env" file), Zod will incorrectly flag
   * it as a type mismatch violation. Additionally, if you have an empty string
   * for a value that is supposed to be a string with a default value (e.g.
   * `DOMAIN=` in an ".env" file), the default value will never be applied.
   *
   * In order to solve these issues, we recommend that all new projects
   * explicitly specify this option as true.
   */
  emptyStringAsUndefined: true,
});
