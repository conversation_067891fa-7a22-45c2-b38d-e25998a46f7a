locals {
  tags          = {
    "CostCode"  = "ehs"
    "ManagedBy" = "terraform"
  }
}

resource "aws_ecr_repository" "ehs" {
  name                 = "ehs"
  image_tag_mutability = "MUTABLE"
  encryption_configuration {
    encryption_type = "AES256"
  }
  tags = local.tags
}

resource "aws_ecr_lifecycle_policy" "ehs_combined_policy" {
  repository = aws_ecr_repository.ehs.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Expire untagged images over 100"
        selection = {
          tagStatus     = "untagged"
          countType     = "imageCountMoreThan"
          countNumber   = 100
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Expire total images over 110"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = 110
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}
