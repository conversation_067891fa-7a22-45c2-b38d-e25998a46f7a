import { defineConfig } from 'vite';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import path from 'node:path';
import { runtimeEnv } from 'vite-plugin-runtime';

export default defineConfig({
  plugins: [react(), tailwindcss(), runtimeEnv()],
  base: '/ehs/',
  resolve: {
    alias: {
      '@': path.resolve(import.meta.dirname, 'client', 'src'),
      '@shared': path.resolve(import.meta.dirname, 'shared'),
    },
  },
  server: {
    host: '0.0.0.0',
    allowedHosts: ['docker-ehs', '.onupkeep.com', '.upkeep.com', 'ehsupkapp.serveo.net'],
    hmr: { port: 8595, host: '0.0.0.0' },
  },
  root: path.resolve(import.meta.dirname, 'client'),
  build: {
    outDir: path.resolve(import.meta.dirname, 'dist/public'),
    emptyOutDir: true,
  },
});
