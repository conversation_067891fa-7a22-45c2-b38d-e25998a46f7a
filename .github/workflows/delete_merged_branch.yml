name: Delete Merged Branches

on:
  pull_request:
    types: [closed]
    branches:
      - main

jobs:
  delete_branch:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    steps:
      - name: Extract branch name
        id: extract_branch
        run: echo "branch_name=${{ github.event.pull_request.head.ref }}" >> $GITHUB_OUTPUT

      - name: Check if branch should be preserved
        id: check_branch
        run: |
          BRANCH="${{ steps.extract_branch.outputs.branch_name }}"
          if [[ "$BRANCH" == main* || "$BRANCH" == hotfix* || "$BRANCH" == deploy* ]]; then
            echo "should_delete=false" >> $GITHUB_OUTPUT
            echo "Branch $BRANCH has a protected prefix and will not be deleted."
          else
            echo "should_delete=true" >> $GITHUB_OUTPUT
            echo "Branch $BRANCH will be deleted."
          fi

      - name: Delete branch
        if: steps.check_branch.outputs.should_delete == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.git.deleteRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `heads/${context.payload.pull_request.head.ref}`,
            })
