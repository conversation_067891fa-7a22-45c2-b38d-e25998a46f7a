name: 'Terraform Infra'

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.TF_AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.TF_AWS_SECRET_ACCESS_KEY}}

permissions:
  contents: read
  pull-requests: write

on:
  push:
    paths: 
    - 'infra/**/*.tf'
  pull_request:
    types:
    - opened
    branches:
    - develop
    paths: 
    - 'infra/**/*.tf'

jobs:
  get-tf-plan-paths:
    if: ${{ !startsWith(github.ref, 'refs/tags/') && !startsWith(github.ref, 'refs/heads/deploy') }}
    runs-on: ubuntu-latest
    outputs:
      dir_names: ${{ steps.get_folders.outputs.dir_name }}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get changed folders
        id: changed-files
        uses: step-security/changed-files@3dbe17c78367e7d60f00d78ae6781a35be47b4a1 # v45
        with:
          dir_names: true
          files_ignore: |
            .github/**
            infra/local/**
            infra/modules/**
          files: | 
            infra/*/*.tf
      - id: get_folders
        run: |
          folders="${{ steps.changed-files.outputs.all_changed_files }}"
          folders="['${folders// /\',\'}']"
          echo "$folders"
          echo "dir_name=$folders" >> $GITHUB_OUTPUT
  terraform:
    needs: get-tf-plan-paths
    runs-on: ubuntu-latest
    name: terraform
    strategy:
        matrix:
          folder: ${{ fromJson(needs.get-tf-plan-paths.outputs.dir_names) }}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: setup git credentials for cloning terraform repo.
        uses: de-vri-es/setup-git-credentials@5e1f18da68b7039c7f824408d170811aaec93ca8 # v2.1.2
        with:
          credentials: https://upkeeptechops:${{ secrets.TECHOPS_PAT }}@github.com/
      - name: terraform setup
        uses: hashicorp/setup-terraform@b9cd54a3c349d3f38e8881555d616ced269862dd # v3.1.2
        with:
          terraform_wrapper: false
      - name: terraform format
        if: ${{ needs.get-tf-plan-paths.outputs.dir_names != '' }}
        id: format
        run: terraform fmt -check 
        continue-on-error: true
        shell: bash
      - name: terraform init
        if: ${{ needs.get-tf-plan-paths.outputs.dir_names != '' }}
        id: init
        run: terraform init
        working-directory: ${{ matrix.folder }}
        shell: bash
      - name: terraform plan
        if: ${{ needs.get-tf-plan-paths.outputs.dir_names != '' }}
        id: plan
        run: |
          terraform plan -input=false -no-color -out tf.plan
        working-directory: ${{ matrix.folder }}
        shell: bash
      - name: terraform show
        if: ${{ needs.get-tf-plan-paths.outputs.dir_names != '' }}
        id: show
        run: terraform show -no-color tf.plan 2>&1 >/tmp/plan.txt
        working-directory: ${{ matrix.folder }}
        shell: bash
      - uses: actions/github-script@v7.0.1
        if: github.event_name == 'pull_request' && needs.get-tf-plan-paths.outputs.dir_names != ''
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require("fs");
            const plan = fs.readFileSync("/tmp/plan.txt", "utf8");
            const maxGitHubBodyCharacters = 65536;
            function chunkSubstr(str, size) {
              const numChunks = Math.ceil(str.length / size)
              const chunks = new Array(numChunks)
              for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
                chunks[i] = str.substr(o, size)
              }
              return chunks
            }
            var plans = chunkSubstr(plan, maxGitHubBodyCharacters); 
            for (let i = 0; i < plans.length; i++) {
              const output = `### ${{ matrix.folder }} # ${i + 1}
              #### Terraform Format and Style 🖌\`${{ steps.format.outcome }}\`
              #### Terraform Initialization ⚙️\`${{ steps.init.outcome }}\`
              #### Terraform Plan 📖\`${{ steps.plan.outcome }}\`
              <details><summary>Show Plan</summary>
              \`\`\`\n
              ${"```"}\n
              ${plans[i]}\n
              ${"```"}\n
              \`\`\`
              </details>
              
              *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`, Working Directory: \`${{ matrix.folder }}\`, Workflow: \`${{ github.workflow }}\`*`;   
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: output
              })
            }
      - name: terraform apply
        if: github.ref == 'refs/heads/main' && github.event_name == 'push' && needs.get-tf-plan-paths.outputs.dir_names != ''
        run: terraform apply -input=false tf.plan
        working-directory: ${{ matrix.folder }}
        shell: bash
