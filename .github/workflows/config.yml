name: Build

on:
  push:
    branches-ignore:
      - 'deploy-**'
      - 'hotfix-**'
    paths-ignore:
      - 'infra/**/*.tf'
    tags:
      - 'v**'

jobs:
  ecr_login:
    uses: upkeepapp/github-actions/.github/workflows/ecr_token.yml@main
    secrets: inherit
  build:
    needs: ecr_login
    runs-on: ubuntu-latest
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://npm.pkg.github.com'

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ github.event.repository.name }}
          IMAGE_TAG: ${{ github.ref_name }}
        run: |
          docker build -t $REGISTRY/$REPOSITORY:$IMAGE_TAG .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
  staging-prod-deployment:
    needs: [ecr_login, build]
    runs-on: ubuntu-latest
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Trigger Deploy Branch
        if: ${{ github.ref == 'refs/heads/develop' }}
        uses: convictional/trigger-workflow-and-wait@f69fa9eedd3c62a599220f4d5745230e237904be # v1.6.5
        with:
          owner: upkeepapp
          repo: ehs
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: tag_bump_and_changelog.yml
          ref: develop
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false

      - name: Deploy to staging3
        if: startsWith(github.ref, 'refs/tags/') && contains(github.ref_name, 'beta') && contains(github.ref_name, '0-beta')
        uses: convictional/trigger-workflow-and-wait@f69fa9eedd3c62a599220f4d5745230e237904be # v1.6.5
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: common_config.yml
          ref: nonprod/upkeep/staging3
          client_payload: '{"repo_name": "ehs", "image_tag": "${{ github.ref_name }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false

      - name: Deploy to prod
        if: startsWith(github.ref, 'refs/tags/') && !contains(github.ref_name, 'beta')
        uses: convictional/trigger-workflow-and-wait@f69fa9eedd3c62a599220f4d5745230e237904be # v1.6.5
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: update_tag.yml
          ref: prod/upkeep/ehs-release
          client_payload: '{"repo_name": "ehs", "image_tag": "${{ github.ref_name }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false
