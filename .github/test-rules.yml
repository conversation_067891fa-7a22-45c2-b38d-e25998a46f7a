# Test rules for the EHS project
coverage:
  # Coverage thresholds for server code
  server:
    # Current coverage metrics (as of latest update)
    current:
      statements: 91.03
      branches: 95.34
      functions: 95.23
      lines: 91.03
    
    # Target thresholds that should be maintained or improved
    target:
      statements: 90
      branches: 90
      functions: 90
      lines: 90
    
    # Paths to measure coverage
    excludePatterns:
      - "**/*.d.ts"
      - "**/node_modules/**"
      - "**/dist/**"
      - "**/test/**"
      - "**/*.test.ts"
      - "**/*.spec.ts"
    includePatterns:
      - "server/**/*.ts"
  
  # When we add client tests, can add client coverage rules here
  
# Files with 100% coverage (these should stay at 100%)
fullCoverage:
  - "server/db/index.ts"
  - "server/services/ai.service.ts"
  - "server/services/file/file.service.ts"
  - "server/services/incident/incident.service.ts"
  - "server/services/user/permissions.ts"
  - "server/services/user/types.ts"
  - "server/services/user/user.service.ts"
  - "server/upkeep/headers.ts"
  - "server/upkeep/index.ts"
  - "server/upkeep/work-order.router.ts"
  - "server/utils/logger.ts"
  
# Test globs  
tests:
  server:
    - "server/**/*.test.ts"
    - "server/**/*.spec.ts"

# Coverage priority for next improvements
nextCoveragePriorities:
  - "server/index.ts" # Currently at 0%
  - "server/test/setup.ts" # Currently at 0%
  - "server/trpc/trpc.ts" # Branch coverage at 80%
  - "server/trpc/router/ai.router.ts" # Branch coverage at 75%
  - "server/trpc/router/file.router.ts" # Branch coverage at 83.33%
  - "server/trpc/router/user.router.ts" # Line coverage at 83.33%

# Test command for server
command:
  server: "pnpm test:server"
  server-coverage: "pnpm test:server:coverage" 