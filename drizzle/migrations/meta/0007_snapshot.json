{"id": "da57ba01-2797-40b8-8fe2-f92965a6a7e7", "prevId": "9cca0a15-a0bf-4399-a3f2-f63bba8383ae", "version": "7", "dialect": "postgresql", "tables": {"public.access_points": {"name": "access_points", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "access_point_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_trail": {"name": "audit_trail", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "audit_trail_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.capas": {"name": "capas", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "default": "generate_capa_slug()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "capa_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "asset_id": {"name": "asset_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "rca_method": {"name": "rca_method", "type": "rca_method", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_selected'"}, "rca_findings": {"name": "rca_findings", "type": "text", "primaryKey": false, "notNull": true}, "root_cause": {"name": "root_cause", "type": "root_cause", "typeSchema": "public", "primaryKey": false, "notNull": false}, "other_root_cause": {"name": "other_root_cause", "type": "text", "primaryKey": false, "notNull": false}, "ai_suggested_action": {"name": "ai_suggested_action", "type": "text", "primaryKey": false, "notNull": false}, "ai_confidence_score": {"name": "ai_confidence_score", "type": "real", "primaryKey": false, "notNull": false}, "actions_to_address": {"name": "actions_to_address", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "capa_priority", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'medium'"}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text[]", "primaryKey": false, "notNull": false, "default": "ARRAY[]::text[]"}, "private_to_admins": {"name": "private_to_admins", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "team_members_to_notify": {"name": "team_members_to_notify", "type": "text[]", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "incident_id": {"name": "incident_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "trigger_work_order": {"name": "trigger_work_order", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "actions_implemented": {"name": "actions_implemented", "type": "text", "primaryKey": false, "notNull": false}, "implementation_date": {"name": "implementation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "implemented_by": {"name": "implemented_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "voe_due_date": {"name": "voe_due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_findings": {"name": "verification_findings", "type": "text", "primaryKey": false, "notNull": false}, "voe_performed_by": {"name": "voe_performed_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "voe_date": {"name": "voe_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "effectiveness_status": {"name": "effectiveness_status", "type": "capa_effectiveness_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_effective'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"capa_slug_index": {"name": "capa_slug_index", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "capa_created_by_index": {"name": "capa_created_by_index", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "capa_archived_index": {"name": "capa_archived_index", "columns": [{"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"capas_incident_id_incidents_id_fk": {"name": "capas_incident_id_incidents_id_fk", "tableFrom": "capas", "tableTo": "incidents", "columnsFrom": ["incident_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comment_mentions": {"name": "comment_mentions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "comment_id": {"name": "comment_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": false}, "mention_text": {"name": "mention_text", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"comment_mention_comment_id_index": {"name": "comment_mention_comment_id_index", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "comment_mention_user_id_index": {"name": "comment_mention_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comment_mentions_comment_id_comments_id_fk": {"name": "comment_mentions_comment_id_comments_id_fk", "tableFrom": "comment_mentions", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comments": {"name": "comments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "incident_id": {"name": "incident_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "capa_id": {"name": "capa_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {"comment_created_at_index": {"name": "comment_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comments_incident_id_incidents_id_fk": {"name": "comments_incident_id_incidents_id_fk", "tableFrom": "comments", "tableTo": "incidents", "columnsFrom": ["incident_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "comments_capa_id_capas_id_fk": {"name": "comments_capa_id_capas_id_fk", "tableFrom": "comments", "tableTo": "capas", "columnsFrom": ["capa_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(127)", "primaryKey": false, "notNull": true}, "presigned_url": {"name": "presigned_url", "type": "text", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "text", "primaryKey": false, "notNull": true}, "s3_bucket": {"name": "s3_bucket", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "file_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"file_status_index": {"name": "file_status_index", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_entity_index": {"name": "file_entity_index", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploaded_by_index": {"name": "file_uploaded_by_index", "columns": [{"expression": "uploaded_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.incidents": {"name": "incidents", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "default": "generate_incident_slug()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "reported_at": {"name": "reported_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reported_by": {"name": "reported_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "reported_by_name": {"name": "reported_by_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reported_by_email": {"name": "reported_by_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "report_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "asset_ids": {"name": "asset_ids", "type": "<PERSON><PERSON><PERSON>(10)[]", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "incident_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "severity_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "media": {"name": "media", "type": "text[]", "primaryKey": false, "notNull": false}, "immediate_actions": {"name": "immediate_actions", "type": "text", "primaryKey": false, "notNull": false}, "root_cause": {"name": "root_cause", "type": "root_cause", "typeSchema": "public", "primaryKey": false, "notNull": false}, "other_root_cause": {"name": "other_root_cause", "type": "text", "primaryKey": false, "notNull": false}, "osha_reportable": {"name": "osha_reportable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "ai_confidence_score": {"name": "ai_confidence_score", "type": "real", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"incident_title_index": {"name": "incident_title_index", "columns": [{"expression": "title", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "incident_slug_index": {"name": "incident_slug_index", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "incident_reported_by_index": {"name": "incident_reported_by_index", "columns": [{"expression": "reported_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "incident_type_index": {"name": "incident_type_index", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "incident_archived_index": {"name": "incident_archived_index", "columns": [{"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "incident_deleted_at_index": {"name": "incident_deleted_at_index", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.incidents_osha": {"name": "incidents_osha", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "incident_id": {"name": "incident_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "osha_log_id": {"name": "osha_log_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "submitted_by": {"name": "submitted_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "submitted": {"name": "submitted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"incidents_osha_incident_id_incidents_id_fk": {"name": "incidents_osha_incident_id_incidents_id_fk", "tableFrom": "incidents_osha", "tableTo": "incidents", "columnsFrom": ["incident_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "incidents_osha_osha_log_id_osha_logs_id_fk": {"name": "incidents_osha_osha_log_id_osha_logs_id_fk", "tableFrom": "incidents_osha", "tableTo": "osha_logs", "columnsFrom": ["osha_log_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.incidents_users": {"name": "incidents_users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "incident_id": {"name": "incident_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"incidents_users_incident_id_incidents_id_fk": {"name": "incidents_users_incident_id_incidents_id_fk", "tableFrom": "incidents_users", "tableTo": "incidents", "columnsFrom": ["incident_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_logs": {"name": "osha_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "assessment_year": {"name": "assessment_year", "type": "integer", "primaryKey": false, "notNull": true}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "generated_by": {"name": "generated_by", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"generated_by_index": {"name": "generated_by_index", "columns": [{"expression": "generated_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.access_point_status": {"name": "access_point_status", "schema": "public", "values": ["active", "inactive"]}, "public.audit_trail_action": {"name": "audit_trail_action", "schema": "public", "values": ["created", "updated", "deleted", "commented", "submitted", "closed", "in_review", "reopened", "archived", "unarchived"]}, "public.capa_effectiveness_status": {"name": "capa_effectiveness_status", "schema": "public", "values": ["effective", "partial", "not_effective", "not_evaluated"]}, "public.capa_priority": {"name": "capa_priority", "schema": "public", "values": ["low", "medium", "high"]}, "public.capa_tags": {"name": "capa_tags", "schema": "public", "values": ["training", "policy", "hazard", "equipment", "procedure", "personnel"]}, "public.capa_type": {"name": "capa_type", "schema": "public", "values": ["corrective", "preventive", "both"]}, "public.file_status": {"name": "file_status", "schema": "public", "values": ["pending", "completed", "failed", "expired"]}, "public.incident_category": {"name": "incident_category", "schema": "public", "values": ["chemical", "electrical", "ergonomic", "fall", "fire", "mechanical", "radiation", "spill", "transportation", "violence", "other"]}, "public.rca_method": {"name": "rca_method", "schema": "public", "values": ["5_whys", "fishbone", "fault_tree", "other", "not_selected"]}, "public.report_type": {"name": "report_type", "schema": "public", "values": ["incident", "near_miss"]}, "public.role": {"name": "role", "schema": "public", "values": ["admin", "technician"]}, "public.root_cause": {"name": "root_cause", "schema": "public", "values": ["human_error", "equipment_failure", "environmental", "procedural", "other"]}, "public.severity_level": {"name": "severity_level", "schema": "public", "values": ["low", "medium", "high", "critical"]}, "public.status": {"name": "status", "schema": "public", "values": ["open", "in_review", "closed"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}