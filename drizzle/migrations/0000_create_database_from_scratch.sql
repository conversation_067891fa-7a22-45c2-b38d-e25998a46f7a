-- Migration: Create complete database schema from scratch
-- Created: Complete database setup for EHS system
-- This migration includes all tables, enums, indexes, sequences, and functions

-- ============================================================================
-- ENUMS
-- ============================================================================

-- Enum for incident report classification
CREATE TYPE report_type AS ENUM ('incident', 'near_miss');

-- Enum for classifying severity levels of incidents and CAPAs  
CREATE TYPE severity_level AS ENUM ('low', 'medium', 'high', 'critical');

-- Enum for categorizing the root cause of incidents
CREATE TYPE root_cause AS ENUM (
  'human_error',        -- Errors made by staff
  'equipment_failure',  -- Failures of machinery or tools
  'environmental',      -- Environmental factors
  'procedural',         -- Issues with procedures or protocols
  'other'               -- Other uncategorized causes
);

-- Enum for tracking the status of incidents and CAPAs
CREATE TYPE status AS ENUM ('open', 'in_review', 'closed');

-- Enum for user role types
CREATE TYPE role AS ENUM ('admin', 'technician');

-- Enum for categorizing incident types
CREATE TYPE incident_category AS ENUM (
  'chemical',          -- Chemical spills or exposures
  'electrical',        -- Electrical hazards
  'ergonomic',         -- Ergonomic issues
  'fall',              -- Falls or slips
  'fire',              -- Fire incidents
  'mechanical',        -- Mechanical hazards
  'radiation',         -- Radiation exposure
  'spill',             -- Spills of non-chemical materials
  'transportation',    -- Vehicle accidents
  'violence'           -- Workplace violence
);

-- Enum for file status
CREATE TYPE file_status AS ENUM (
  'pending',           -- URL generated but upload not complete
  'completed',         -- Upload finished successfully
  'failed',            -- Upload failed
  'expired'            -- Presigned URL has expired
);

-- Enum for CAPA types
CREATE TYPE capa_type AS ENUM ('corrective', 'preventive', 'both');

-- Enum for CAPA priority levels
CREATE TYPE capa_priority AS ENUM ('low', 'medium', 'high');

-- Enum for RCA methods
CREATE TYPE rca_method AS ENUM (
  '5_whys',
  'fishbone', 
  'fault_tree',
  'other',
  'not_selected'
);

-- Enum for audit trail action types
CREATE TYPE audit_trail_action AS ENUM (
  'created',           -- New entity created
  'updated',           -- Entity updated
  'deleted',           -- Entity deleted
  'commented',         -- Comment added
  'submitted',         -- Entity submitted (e.g., to OSHA)
  'closed',            -- Entity marked as closed
  'in_review',         -- Entity marked as in review
  'reopened',          -- Entity reopened
  'archived',          -- Entity archived
  'unarchived'         -- Entity unarchived
);

-- ============================================================================
-- SEQUENCES AND FUNCTIONS
-- ============================================================================

-- Create sequence for CAPA slugs
CREATE SEQUENCE IF NOT EXISTS capa_slug_seq START 1;

-- Function to generate CAPA slug
CREATE OR REPLACE FUNCTION generate_capa_slug() 
RETURNS TEXT AS $$
BEGIN
    RETURN 'CAPA-' || LPAD(nextval('capa_slug_seq')::text, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- Create sequence for Incident slugs
CREATE SEQUENCE IF NOT EXISTS incident_slug_seq START 1;

-- Function to generate Incident slug
CREATE OR REPLACE FUNCTION generate_incident_slug() 
RETURNS TEXT AS $$
BEGIN
    RETURN 'INC-' || LPAD(nextval('incident_slug_seq')::text, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- TABLES
-- ============================================================================

-- OSHA Logs for compliance reporting
CREATE TABLE osha_logs (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    assessment_year INTEGER NOT NULL,
    generated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    generated_by TEXT NOT NULL,
    file_url TEXT NOT NULL
);

-- Create index for generated_by
CREATE INDEX generated_by_index ON osha_logs(generated_by);

-- Incident records for safety reporting
CREATE TABLE incidents (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    slug TEXT NOT NULL DEFAULT generate_incident_slug(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reported_at TIMESTAMP NOT NULL DEFAULT NOW(),
    reported_by VARCHAR(10) NOT NULL,
    type report_type NOT NULL,
    location_id VARCHAR(10),
    asset_ids VARCHAR(10)[],
    category incident_category NOT NULL,
    severity severity_level NOT NULL,
    status status NOT NULL,
    archived BOOLEAN DEFAULT FALSE,
    media TEXT[],
    immediate_actions TEXT,
    root_cause root_cause,
    other_root_cause TEXT,
    osha_reportable BOOLEAN DEFAULT FALSE,
    ai_confidence_score REAL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create indexes for incidents
CREATE INDEX incident_title_index ON incidents(title);
CREATE INDEX incident_slug_index ON incidents(slug);
CREATE INDEX incident_reported_by_index ON incidents(reported_by);
CREATE INDEX incident_type_index ON incidents(type);
CREATE INDEX incident_archived_index ON incidents(archived);
CREATE INDEX incident_deleted_at_index ON incidents(deleted_at);

-- Junction table linking incidents to OSHA logs
CREATE TABLE incidents_osha (
    id VARCHAR(24) PRIMARY KEY,
    incident_id VARCHAR(24) NOT NULL REFERENCES incidents(id),
    osha_log_id VARCHAR(24) NOT NULL REFERENCES osha_logs(id),
    submitted_by VARCHAR(10) NOT NULL,
    submitted_at TIMESTAMP,
    submitted BOOLEAN DEFAULT FALSE,
    data JSONB
);

-- Junction table linking incidents to users involved
CREATE TABLE incidents_users (
    id VARCHAR(24) PRIMARY KEY,
    incident_id VARCHAR(24) NOT NULL REFERENCES incidents(id),
    user_id VARCHAR(10) NOT NULL
);

-- Corrective and Preventive Actions for addressing incidents
CREATE TABLE capas (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    slug TEXT NOT NULL DEFAULT generate_capa_slug(),
    title VARCHAR(255) NOT NULL,
    type capa_type NOT NULL,
    rca_method rca_method DEFAULT 'not_selected',
    rca_findings TEXT NOT NULL,
    root_cause root_cause,
    other_root_cause TEXT,
    ai_suggested_action TEXT,
    ai_confidence_score REAL,
    actions_to_address TEXT,
    owner_id VARCHAR(10) NOT NULL,
    due_date TIMESTAMP,
    priority capa_priority NOT NULL DEFAULT 'medium',
    tags TEXT[],
    attachments TEXT[] DEFAULT ARRAY[]::text[],
    private_to_admins BOOLEAN DEFAULT FALSE,
    status status NOT NULL DEFAULT 'open',
    team_members_to_notify TEXT[],
    archived BOOLEAN DEFAULT FALSE,
    incident_id VARCHAR(24) REFERENCES incidents(id),
    created_by VARCHAR(10) NOT NULL,
    trigger_work_order BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Create indexes for capas
CREATE INDEX capa_slug_index ON capas(slug);
CREATE INDEX capa_created_by_index ON capas(created_by);
CREATE INDEX capa_archived_index ON capas(archived);

-- Comments for incidents and CAPAs
CREATE TABLE comments (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    incident_id VARCHAR(24) REFERENCES incidents(id),
    capa_id VARCHAR(24) REFERENCES capas(id),
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP,
    user_id VARCHAR(10) NOT NULL
);

-- Create index for comments
CREATE INDEX comment_created_at_index ON comments(created_at);

-- User mentions in comments
CREATE TABLE comment_mentions (
    id VARCHAR(24) PRIMARY KEY,
    comment_id VARCHAR(24) NOT NULL REFERENCES comments(id),
    user_id VARCHAR(10) NOT NULL,
    position INTEGER,
    mention_text VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for comment mentions
CREATE INDEX comment_mention_comment_id_index ON comment_mentions(comment_id);
CREATE INDEX comment_mention_user_id_index ON comment_mentions(user_id);

-- Template for generating work orders from incidents
CREATE TABLE work_order_template (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    incident_id TEXT REFERENCES incidents(id),
    title TEXT NOT NULL,
    description TEXT,
    due_date TEXT,
    priority TEXT,
    category TEXT,
    media TEXT[],
    asset_id VARCHAR(10),
    location_id VARCHAR(10),
    work_order_id VARCHAR(10),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP
);

-- Audit trail for tracking all system actions
CREATE TABLE audit_trail (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    entity_type VARCHAR(255) NOT NULL,
    entity_id VARCHAR(24) NOT NULL,
    action audit_trail_action NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    details TEXT,
    user_id VARCHAR(10)
);

-- Files for S3 presigned URLs
CREATE TABLE files (
    id VARCHAR(24) PRIMARY KEY,
    upkeep_company_id VARCHAR(10) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(127) NOT NULL,
    presigned_url TEXT NOT NULL,
    s3_key TEXT NOT NULL,
    s3_bucket VARCHAR(255) NOT NULL,
    status file_status NOT NULL DEFAULT 'pending',
    entity_type VARCHAR(255),
    entity_id VARCHAR(24),
    uploaded_by VARCHAR(10) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP
);

-- Create indexes for files
CREATE INDEX file_status_index ON files(status);
CREATE INDEX file_entity_index ON files(entity_type, entity_id);
CREATE INDEX file_uploaded_by_index ON files(uploaded_by);

-- ============================================================================
-- ADDITIONAL SETUP
-- ============================================================================

-- Note: This migration creates the complete database schema from scratch.
-- If you need to add additional indexes, constraints, or modifications,
-- create separate migration files for those changes.

-- Migration completed successfully 