import { createId, getConstants } from '@paralleldrive/cuid2';
import { sql } from 'drizzle-orm';
import { boolean, index, integer, jsonb, pgEnum, pgTable, real, text, timestamp, varchar } from 'drizzle-orm/pg-core';

// Enum for incident report classification
export const reportTypeEnum = pgEnum('report_type', ['incident', 'near_miss']);

// Enum for classifying severity levels of incidents and CAPAs
export const severityEnum = pgEnum('severity_level', ['low', 'medium', 'high', 'critical']);

// Enum for categorizing the root cause of incidents
export const rootCauseEnum = pgEnum('root_cause', [
  'human_error', // Errors made by staff
  'equipment_failure', // Failures of machinery or tools
  'environmental', // Environmental factors
  'procedural', // Issues with procedures or protocols
  'other', // Other uncategorized causes
]);

// Enum for tracking the status of incidents and CAPAs
export const statusEnum = pgEnum('status', ['open', 'in_review', 'closed']);

// Enum for user role types
export const roleEnum = pgEnum('role', ['admin', 'technician']);

// Enum for categorizing incident types
export const incidentCategoryEnum = pgEnum('incident_category', [
  'chemical', // Chemical spills or exposures
  'electrical', // Electrical hazards
  'ergonomic', // Ergonomic issues
  'fall', // Falls or slips
  'fire', // Fire incidents
  'mechanical', // Mechanical hazards
  'radiation', // Radiation exposure
  'spill', // Spills of non-chemical materials
  'transportation', // Vehicle accidents
  'violence', // Workplace violence
  'other', // Other uncategorized causes
]);

// Enum for file status
export const fileStatusEnum = pgEnum('file_status', [
  'pending', // URL generated but upload not complete
  'completed', // Upload finished successfully
  'failed', // Upload failed
  'expired', // Presigned URL has expired
]);

// Enum for QR access point status
export const accessPointStatusEnum = pgEnum('access_point_status', [
  'active', // QR code is active and can be scanned
  'inactive', // QR code is inactive and cannot be scanned
]);

// OSHA Logs for compliance reporting
export const oshaLogs = pgTable(
  'osha_logs',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
      .notNull(),
    assessmentYear: integer('assessment_year').notNull(), // Year for OSHA reporting
    generatedAt: timestamp('generated_at').notNull().defaultNow(), // Log generation timestamp
    generatedBy: text('generated_by').notNull(), // User who generated the log
    fileUrl: text('file_url').notNull(), // URL to the generated OSHA report file
  },
  (oshaLogs) => [index('generated_by_index').on(oshaLogs.generatedBy)], // Index for user-based queries
);

// Incident records for safety reporting
export const incidents = pgTable(
  'incidents',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
      .notNull(),
    slug: text('slug')
      .notNull()
      .default(sql`generate_incident_slug()`), // Human-readable identifier (INC-0001)
    title: varchar('title', { length: 255 }).notNull(), // Incident title/summary
    description: text('description'), // Detailed incident description
    reportedAt: timestamp('reported_at').notNull().defaultNow(), // When incident was reported
    reportedBy: varchar('reported_by', { length: 10 }), // upkeep user id Who reported the incident
    reportedByName: varchar('reported_by_name', { length: 255 }), // Name of the user who reported the incident
    reportedByEmail: varchar('reported_by_email', { length: 255 }), // Email of the user who reported the incident
    type: reportTypeEnum('type').notNull(), // Incident or near miss
    locationId: varchar('location_id', { length: 10 }), // Where incident occurred (ref to UpKeep location)
    assetIds: varchar('asset_ids', { length: 10 }).array(), // Equipment involved (ref to UpKeep assets)
    category: incidentCategoryEnum('category').notNull(), // Type of hazard
    severity: severityEnum('severity').notNull(), // Incident severity rating
    status: statusEnum('status').notNull(), // Current status of incident
    archived: boolean('archived').default(false), // Whether incident is archived
    immediateActions: text('immediate_actions'), // Actions taken immediately after incident
    rootCause: rootCauseEnum('root_cause'), // Determined root cause
    otherRootCause: text('other_root_cause'), // Description if rootCause is "other"
    oshaReportable: boolean('osha_reportable').default(false), // Whether incident is OSHA reportable
    aiConfidenceScore: real('ai_confidence_score'), // AI analysis confidence level
    updatedAt: timestamp('updated_at'), // Last update timestamp
    deletedAt: timestamp('deleted_at'), // Soft deletion timestamp
  },
  (incidents) => [
    index('incident_title_index').on(incidents.title), // Index for title searches
    index('incident_slug_index').on(incidents.slug), // Index for slug lookups
    index('incident_reported_by_index').on(incidents.reportedBy), // Index for user-based queries
    index('incident_type_index').on(incidents.type), // Index for type-based queries
    index('incident_archived_index').on(incidents.archived), // Index for filtering archived incidents
    index('incident_deleted_at_index').on(incidents.deletedAt), // Index for soft deletion queries
  ],
);

// Junction table linking incidents to OSHA logs
export const incidentsOsha = pgTable('incidents_osha', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  incidentId: varchar('incident_id', { length: getConstants().defaultLength }) // Referenced incident
    .notNull()
    .references(() => incidents.id),
  oshaLogId: varchar('osha_log_id', { length: getConstants().defaultLength }) // Referenced OSHA log
    .notNull()
    .references(() => oshaLogs.id),
  submittedBy: varchar('submitted_by', {
    // User who submitted the OSHA report
    length: 10,
  }).notNull(),

  submittedAt: timestamp('submitted_at'), // When the incident was submitted to OSHA
  submitted: boolean('submitted').default(false), // Whether the incident was submitted to OSHA
  data: jsonb('data'), // OSHA-specific form data
});

// Junction table linking incidents to users involved
export const incidentsUsers = pgTable('incidents_users', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  incidentId: varchar('incident_id', { length: getConstants().defaultLength }) // Referenced incident
    .notNull()
    .references(() => incidents.id),
  userId: varchar('user_id', { length: 10 }) // User involved in incident
    .notNull(),
});

// Enum for CAPA types
export const capaTypeEnum = pgEnum('capa_type', ['corrective', 'preventive', 'both']);
// Enum for CAPA priority levels
export const capaPriorityEnum = pgEnum('capa_priority', ['low', 'medium', 'high']);

// Enum for RCA methods
export const rcaMethodEnum = pgEnum('rca_method', ['5_whys', 'fishbone', 'fault_tree', 'other', 'not_selected']);

// Enum for CAPA tags
export const capaTagsEnum = pgEnum('capa_tags', [
  'training',
  'policy',
  'hazard',
  'equipment',
  'procedure',
  'personnel',
]);

export const capaEffectivenessStatusEnum = pgEnum('capa_effectiveness_status', [
  'effective',
  'partial',
  'not_effective',
  'not_evaluated',
]);

// Corrective and Preventive Actions for addressing incidents
export const capas = pgTable(
  'capas',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    slug: text('slug')
      .notNull()
      .default(sql`generate_capa_slug()`),
    title: varchar('title', { length: 255 }).notNull(),
    type: capaTypeEnum('type').notNull(),
    locationId: varchar('location_id', { length: 10 }),
    assetId: varchar('asset_id', { length: 10 }),
    rcaMethod: rcaMethodEnum('rca_method').default('not_selected'),
    rcaFindings: text('rca_findings').notNull(),
    rootCause: rootCauseEnum('root_cause'),
    otherRootCause: text('other_root_cause'),
    aiSuggestedAction: text('ai_suggested_action'), // AI-generated action suggestion
    aiConfidenceScore: real('ai_confidence_score'), // AI analysis confidence level
    actionsToAddress: text('actions_to_address'),
    ownerId: varchar('owner_id', { length: 10 }) // User responsible for the CAPA
      .notNull(),
    dueDate: timestamp('due_date'),
    priority: capaPriorityEnum('priority').notNull().default('medium'),
    tags: text('tags').array(),
    privateToAdmins: boolean('private_to_admins').default(false),
    status: statusEnum('status').notNull().default('open'),
    teamMembersToNotify: text('team_members_to_notify').array(),
    archived: boolean('archived').default(false),
    incidentId: varchar('incident_id', { length: getConstants().defaultLength }).references(() => incidents.id),
    createdBy: varchar('created_by', { length: 10 }) // User who created the CAPA
      .notNull(),
    triggerWorkOrder: boolean('trigger_work_order').default(false),
    actionsImplemented: text('actions_implemented'),
    implementationDate: timestamp('implementation_date'),
    implementedBy: varchar('implemented_by', { length: 10 }),
    voeDueDate: timestamp('voe_due_date'),
    verificationFindings: text('verification_findings'),
    voePerformedBy: varchar('voe_performed_by', { length: 10 }),
    voeDate: timestamp('voe_date'),
    effectivenessStatus: capaEffectivenessStatusEnum('effectiveness_status').default('not_evaluated'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
    deletedAt: timestamp('deleted_at'),
  },
  (capas) => [
    index('capa_slug_index').on(capas.slug),
    index('capa_created_by_index').on(capas.createdBy),
    index('capa_archived_index').on(capas.archived),
  ],
);

// Comments for incidents and CAPAs
export const comments = pgTable(
  'comments',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
      .notNull(),
    incidentId: varchar('incident_id', {
      // Referenced incident (if comment is on incident)
      length: getConstants().defaultLength,
    }).references(() => incidents.id),
    capaId: varchar('capa_id', {
      // Referenced CAPA (if comment is on CAPA)
      length: getConstants().defaultLength,
    }).references(() => capas.id),
    content: text('content').notNull(), // Comment text content
    createdAt: timestamp('created_at').notNull().defaultNow(), // Comment creation timestamp
    updatedAt: timestamp('updated_at'), // Last edit timestamp
    userId: varchar('user_id', {
      // User who made the comment
      length: 10,
    }).notNull(),
  },
  (comments) => [index('comment_created_at_index').on(comments.createdAt)], // Index for chronological ordering
);

// User mentions in comments
export const commentMentions = pgTable(
  'comment_mentions',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    commentId: varchar('comment_id', { length: getConstants().defaultLength }) // Referenced comment
      .notNull()
      .references(() => comments.id),
    userId: varchar('user_id', { length: 10 }) // User being mentioned
      .notNull(),
    position: integer('position'), // Position of mention in comment text
    mentionText: varchar('mention_text', { length: 100 }), // Text of the mention (e.g., @username)
    createdAt: timestamp('created_at').notNull().defaultNow(), // Record creation timestamp
  },
  (commentMentions) => [
    index('comment_mention_comment_id_index').on(commentMentions.commentId), // Index for comment-based queries
    index('comment_mention_user_id_index').on(commentMentions.userId), // Index for user-based queries
  ],
);

// Enum for audit trail action types
export const auditTrailActionEnum = pgEnum('audit_trail_action', [
  'created', // New entity created
  'updated', // Entity updated
  'deleted', // Entity deleted
  'commented', // Comment added
  'submitted', // Entity submitted (e.g., to OSHA)
  'closed', // Entity marked as closed
  'in_review', // Entity marked as in review
  'reopened', // Entity reopened
  'archived', // Entity archived
  'unarchived', // Entity unarchived
]);

// Audit trail for tracking all system actions
export const auditTrail = pgTable('audit_trail', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
    .notNull(),
  entityType: varchar('entity_type', { length: 255 }).notNull(), // Type of entity (incident, CAPA, comment etc.)
  entityId: varchar('entity_id', {
    // ID of the affected entity
    length: getConstants().defaultLength,
  }).notNull(),
  action: auditTrailActionEnum('action').notNull(), // Type of action performed
  timestamp: timestamp('timestamp').notNull().defaultNow(), // When action occurred
  details: text('details'), // Additional action details
  userId: varchar('user_id', {
    // User who performed the action
    length: 10,
  }),
});

// Files for managing S3 presigned URLs and file uploads
export const files = pgTable(
  'files',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    fileName: varchar('file_name', { length: 255 }).notNull(),
    fileSize: integer('file_size').notNull(),
    mimeType: varchar('mime_type', { length: 127 }).notNull(),
    presignedUrl: text('presigned_url').notNull(),
    s3Key: text('s3_key').notNull(),
    s3Bucket: varchar('s3_bucket', { length: 255 }).notNull(),
    status: fileStatusEnum('status').notNull().default('pending'),
    entityType: varchar('entity_type', { length: 255 }), // Type of entity this file belongs to
    entityId: varchar('entity_id', { length: getConstants().defaultLength }), // ID of the related entity
    uploadedBy: varchar('uploaded_by', { length: 10 }),
    expiresAt: timestamp('expires_at').notNull(), // When presigned URL expires
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
  },
  (files) => [
    index('file_status_index').on(files.status),
    index('file_entity_index').on(files.entityType, files.entityId),
    index('file_uploaded_by_index').on(files.uploadedBy),
  ],
);

// QR code access points for location-based scanning
export const accessPoints = pgTable('access_points', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(), // Unique identifier for the access point
  upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(), // Associated upkeep company Id for multi-tenancy
  locationId: varchar('location_id', { length: 10 }).notNull(), // Associated location where access point is placed
  name: varchar('name', { length: 255 }).notNull(), // Display name of the access point
  description: text('description'), // Optional detailed description
  status: accessPointStatusEnum('status').notNull().default('active'), // Current operational status
  createdBy: varchar('created_by', { length: 10 }).notNull(), // User who created the access point
  createdAt: timestamp('created_at').notNull().defaultNow(), // Creation timestamp
  archived: boolean('archived').default(false), // Whether access point is archived
  archivedAt: timestamp('archived_at'), // When access point was archived
  updatedAt: timestamp('updated_at'), // Last update timestamp
  deletedAt: timestamp('deleted_at'), // Soft deletion timestamp
});
