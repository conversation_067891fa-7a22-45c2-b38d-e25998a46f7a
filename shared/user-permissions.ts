import { z } from 'zod';

// User account constants
export const USER_TYPES = {
  ADMIN: 'Admin',
  TECHNICIAN: 'Technician',
  VIEW_ONLY: 'View Only',
  REQUESTOR: 'Requestor',
  LIMITED_TECHNICIAN: 'Limited Technician',
  VENDOR: 'Vendor',
} as const;

export const USER_ACCOUNTS = {
  ADMIN: '1',
  TECHNICIAN: '2',
  VIEW_ONLY: '3',
  REQUESTOR: '4',
  LIMITED_TECHNICIAN: '5',
  VENDOR: '6',
} as const;

export const USER_ACCOUNT_TYPE_MAP = {
  [USER_ACCOUNTS.ADMIN]: USER_TYPES.ADMIN,
  [USER_ACCOUNTS.TECHNICIAN]: USER_TYPES.TECHNICIAN,
  [USER_ACCOUNTS.VIEW_ONLY]: USER_TYPES.VIEW_ONLY,
  [USER_ACCOUNTS.REQUESTOR]: USER_TYPES.REQUESTOR,
  [USER_ACCOUNTS.LIMITED_TECHNICIAN]: USER_TYPES.LIMITED_TECHNICIAN,
  [USER_ACCOUNTS.VENDOR]: USER_TYPES.VENDOR,
} as const;

export const MODULES = {
  EHS_INCIDENT: 'ehs-incident',
  EHS_CAPA: 'ehs-capa',
  EHS_ACCESS_POINT: 'ehs-access-point',
} as const;

export const ALLOWED_ACTIONS = {
  CREATE: 'create',
  USE_AI_VOICE: 'use-ai-voice',
  ATTACH_MEDIA: 'attach-media',
  TAG_TEAMMATES: 'tag-teammates',
  VIEW: 'view',
  VIEW_AUDIT_TRAIL: 'view-audit-trail',
  VIEW_TRACKER: 'view-tracker',
  EDIT: 'edit',
  COMMENT: 'comment',
  EXPORT: 'export',
  MARK_REVIEWED: 'mark-reviewed',
  MARK_COMPLETED: 'mark-completed',
  CHANGE_STATUS: 'change-status',
  CREATE_CAPA: 'create-capa',
  ARCHIVE: 'archive',
  DELETE: 'delete',
} as const;

export const PERMISSION_LEVELS = {
  FULL: 'full',
  PARTIAL: 'partial',
  NONE: 'none',
} as const;

/**
 * Permission matrix - cleaner, more maintainable structure
 * Easy to see differences between user types at a glance
 */
export const PERMISSION_MATRIX = {
  [MODULES.EHS_ACCESS_POINT]: {
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_INCIDENT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.USE_AI_VOICE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.ATTACH_MEDIA]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.TAG_TEAMMATES]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.VIEW_AUDIT_TRAIL]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.COMMENT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.MARK_REVIEWED]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.CREATE_CAPA]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.ARCHIVE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_CAPA]: {
    [ALLOWED_ACTIONS.VIEW_TRACKER]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.USE_AI_VOICE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.ATTACH_MEDIA]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.MARK_COMPLETED]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.CHANGE_STATUS]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.ARCHIVE]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW_AUDIT_TRAIL]: {
      [USER_ACCOUNTS.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNTS.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
  },
} as const;

export const UserTypesSchema = z.enum(Object.values(USER_TYPES) as [string, ...string[]]);
export const AllowedActionsSchema = z.enum(Object.values(ALLOWED_ACTIONS) as [string, ...string[]]);
export const PermissionLevelsSchema = z.enum(Object.values(PERMISSION_LEVELS) as [string, ...string[]]);
export const ModulesSchema = z.enum(Object.values(MODULES) as [string, ...string[]]);
export const UserPermissionSchema = z.object({
  dataObject: ModulesSchema,
  action: AllowedActionsSchema,
  permissionLevel: PermissionLevelsSchema,
});

export type AllowedActions = z.infer<typeof AllowedActionsSchema>;
export type UserPermission = z.infer<typeof UserPermissionSchema>;

export type AllowedUserType = typeof USER_ACCOUNTS.ADMIN | typeof USER_ACCOUNTS.TECHNICIAN;
export type PermissionLevel = z.infer<typeof PermissionLevelsSchema>;

/**
 * Generate permission arrays from matrix
 */
export const generatePermissions = (userType: AllowedUserType): UserPermission[] => {
  const permissions: UserPermission[] = [];

  Object.entries(PERMISSION_MATRIX).forEach(([dataObject, actions]) => {
    Object.entries(actions).forEach(([action, levels]) => {
      permissions.push({
        dataObject: dataObject as UserPermission['dataObject'],
        action: action as AllowedActions,
        permissionLevel: levels[userType] as PermissionLevel,
      });
    });
  });

  return permissions;
};

export const permissions = {
  [USER_ACCOUNTS.ADMIN]: generatePermissions(USER_ACCOUNTS.ADMIN),
  [USER_ACCOUNTS.TECHNICIAN]: generatePermissions(USER_ACCOUNTS.TECHNICIAN),
};
