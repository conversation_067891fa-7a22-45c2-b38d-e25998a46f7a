import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import {
  accessPoints,
  accessPointStatusEnum,
  auditTrail,
  auditTrailActionEnum,
  capaEffectivenessStatusEnum,
  capaPriorityEnum,
  capas,
  capaTagsEnum,
  capaTypeEnum,
  comments,
  files,
  fileStatusEnum,
  incidentCategoryEnum,
  incidents,
  incidentsOsha,
  oshaLogs,
  rcaMethodEnum,
  reportTypeEnum,
  roleEnum,
  rootCauseEnum,
  severityEnum,
  statusEnum,
} from './schema';

import { UserPermissionSchema, UserTypesSchema } from './user-permissions';

export const featureFlags = ['webEHS'] as const;

export const UserSchema = z.object({
  id: z.string(),
  username: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  fullName: z.string(),
  roleId: z.string(),
  groupName: z.string(),
  groupId: z.string(),
  userAccountType: UserTypesSchema,
  permissions: z.array(UserPermissionSchema),
  featureFlags: z.record(z.enum(featureFlags), z.boolean()),
});

export const UserPublicSchema = UserSchema.pick({
  id: true,
  firstName: true,
  lastName: true,
  username: true,
}).extend({
  lastName: z.string().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
});

export type FeatureFlag = (typeof featureFlags)[number];
export type FeatureFlags = Record<FeatureFlag, boolean>;

export type User = z.infer<typeof UserSchema>;
export type UserPublic = z.infer<typeof UserPublicSchema>;

export const ReportTypeSchema = z.enum(reportTypeEnum.enumValues);
export const SeveritySchema = z.enum(severityEnum.enumValues);
export const RootCauseSchema = z.enum(rootCauseEnum.enumValues);
export const StatusSchema = z.enum(statusEnum.enumValues);

export const STATUS_MAP: Record<(typeof statusEnum.enumValues)[number], string> = {
  [statusEnum.enumValues[0]]: 'Open',
  [statusEnum.enumValues[1]]: 'In Review',
  [statusEnum.enumValues[2]]: 'Closed',
};

export const SEVERITY_MAP: Record<(typeof severityEnum.enumValues)[number], string> = {
  [severityEnum.enumValues[0]]: 'Low',
  [severityEnum.enumValues[1]]: 'Medium',
  [severityEnum.enumValues[2]]: 'High',
  [severityEnum.enumValues[3]]: 'Critical',
};

export const CATEGORY_MAP: Record<(typeof incidentCategoryEnum.enumValues)[number], string> = {
  [incidentCategoryEnum.enumValues[0]]: 'Chemical',
  [incidentCategoryEnum.enumValues[1]]: 'Electrical',
  [incidentCategoryEnum.enumValues[2]]: 'Ergonomic',
  [incidentCategoryEnum.enumValues[3]]: 'Fall',
  [incidentCategoryEnum.enumValues[4]]: 'Fire',
  [incidentCategoryEnum.enumValues[5]]: 'Mechanical',
  [incidentCategoryEnum.enumValues[6]]: 'Radiation',
  [incidentCategoryEnum.enumValues[7]]: 'Spill',
  [incidentCategoryEnum.enumValues[8]]: 'Transportation',
  [incidentCategoryEnum.enumValues[9]]: 'Violence',
  [incidentCategoryEnum.enumValues[10]]: 'Other',
};

export const RCA_METHOD_MAP: Record<(typeof rcaMethodEnum.enumValues)[number], string> = {
  [rcaMethodEnum.enumValues[0]]: '5 Whys',
  [rcaMethodEnum.enumValues[1]]: 'Fishbone Diagram',
  [rcaMethodEnum.enumValues[2]]: 'Fault Tree Analysis',
  [rcaMethodEnum.enumValues[3]]: 'Other Method',
  [rcaMethodEnum.enumValues[4]]: 'Not Selected',
};

export const ROOT_CAUSE_MAP: Record<(typeof rootCauseEnum.enumValues)[number], string> = {
  [rootCauseEnum.enumValues[0]]: 'Human Error',
  [rootCauseEnum.enumValues[1]]: 'Equipment Failure',
  [rootCauseEnum.enumValues[2]]: 'Environmental',
  [rootCauseEnum.enumValues[3]]: 'Procedural',
  [rootCauseEnum.enumValues[4]]: 'Other',
};

export const CAPA_PRIORITY_MAP: Record<(typeof capaPriorityEnum.enumValues)[number], string> = {
  [capaPriorityEnum.enumValues[0]]: 'High',
  [capaPriorityEnum.enumValues[1]]: 'Medium',
  [capaPriorityEnum.enumValues[2]]: 'Low',
};

export const CAPA_TYPE_MAP: Record<(typeof capaTypeEnum.enumValues)[number], string> = {
  [capaTypeEnum.enumValues[0]]: 'Corrective',
  [capaTypeEnum.enumValues[1]]: 'Preventive',
  [capaTypeEnum.enumValues[2]]: 'Both',
};

export const CAPA_TAGS_MAP: Record<(typeof capaTagsEnum.enumValues)[number], string> = {
  [capaTagsEnum.enumValues[0]]: 'Training',
  [capaTagsEnum.enumValues[1]]: 'Policy',
  [capaTagsEnum.enumValues[2]]: 'Hazard',
  [capaTagsEnum.enumValues[3]]: 'Equipment',
  [capaTagsEnum.enumValues[4]]: 'Procedure',
  [capaTagsEnum.enumValues[5]]: 'Personnel',
};

export const CAPA_EFFECTIVENESS_STATUS_MAP: Record<(typeof capaEffectivenessStatusEnum.enumValues)[number], string> = {
  [capaEffectivenessStatusEnum.enumValues[0]]: 'Effective',
  [capaEffectivenessStatusEnum.enumValues[1]]: 'Partial',
  [capaEffectivenessStatusEnum.enumValues[2]]: 'Not Effective',
  [capaEffectivenessStatusEnum.enumValues[3]]: 'Not Evaluated',
};

// Interface for status configuration
export interface StatusConfig {
  label: string;
  color: string;
  backgroundColor: string;
}

// Status to style configuration mapping (colors)
export const STATUS_STYLES: Record<(typeof statusEnum.enumValues)[number], StatusConfig> = {
  [statusEnum.enumValues[0]]: {
    label: 'Open',
    backgroundColor: '#e0e7ff',
    color: '#2563eb',
  },
  [statusEnum.enumValues[1]]: {
    label: 'In Review',
    backgroundColor: '#fef9c3',
    color: '#b45309',
  },
  [statusEnum.enumValues[2]]: {
    label: 'Closed',
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
};

// Severity to style configuration mapping (colors)
export const SEVERITY_STYLES: Record<(typeof severityEnum.enumValues)[number], StatusConfig> = {
  [severityEnum.enumValues[0]]: {
    label: 'Low',
    backgroundColor: '#dcfce7',
    color: '#15803d',
  },
  [severityEnum.enumValues[1]]: {
    label: 'Medium',
    backgroundColor: '#fef3c7',
    color: '#d97706',
  },
  [severityEnum.enumValues[2]]: {
    label: 'High',
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  [severityEnum.enumValues[3]]: {
    label: 'Critical',
    backgroundColor: '#7f1d1d',
    color: '#fecaca',
  },
};

export const RoleSchema = z.enum(roleEnum.enumValues);
export const IncidentCategorySchema = z.enum(incidentCategoryEnum.enumValues);
export const FileStatusSchema = z.enum(fileStatusEnum.enumValues);
export const AccessPointStatusSchema = z.enum(accessPointStatusEnum.enumValues);

// Specific schema for file status updates
export const FileUpdateStatusSchema = z.enum(['completed', 'failed'] as const);

export const CreateIncidentSchema = createInsertSchema(incidents);

export const IdSchema = z.object({
  id: z.string().cuid2(),
});

export const IdArraySchema = z.array(z.string().cuid2());

export const RoleIdSchema = z.object({
  roleId: z.string().min(1),
});

export const CreateCapasSchema = createInsertSchema(capas);
export const UpdateIncidentSchema = createUpdateSchema(incidents);

export const SelectIncidentSchema = createSelectSchema(incidents);

export type CreateIncident = InferInsertModel<typeof incidents>;
export type Incident = InferSelectModel<typeof incidents>;

export const UpdateCapasSchema = createUpdateSchema(capas);

export const SelectCapasSchema = createSelectSchema(capas);

export type CreateCapa = z.infer<typeof CreateCapasSchema>;
export type Capa = InferSelectModel<typeof capas>;

export const CreateCommentsSchema = createInsertSchema(comments);

export const CreateCommentFormSchema = CreateCommentsSchema.omit({
  id: true,
  createdAt: true,
  capaId: true,
  incidentId: true,
  updatedAt: true,
  upkeepCompanyId: true,
  userId: true,
}).and(
  z.object({
    entityId: z.string(),
    entityType: z.enum(['incident', 'capa']),
  }),
);

export const ListCommentsSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['incident', 'capa']),
  options: z
    .object({
      limit: z.number().optional(),
      offset: z.number().optional(),
    })
    .optional(),
});

export const UpdateCommentsSchema = createUpdateSchema(comments);
export const SelectCommentsSchema = createSelectSchema(comments);

export type CreateComments = InferInsertModel<typeof comments>;
export type Comments = InferSelectModel<typeof comments>;

export const CreateAuditTrailSchema = createInsertSchema(auditTrail);
export const UpdateAuditTrailSchema = createUpdateSchema(auditTrail);
export const SelectAuditTrailSchema = createSelectSchema(auditTrail);

export const GetAuditTrailSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['incident', 'capa', 'access_point']),
});

export type CreateAuditTrail = InferInsertModel<typeof auditTrail>;
export type AuditTrail = InferSelectModel<typeof auditTrail>;

export const CreateOshaLogsSchema = createInsertSchema(oshaLogs);
export const UpdateOshaLogsSchema = createUpdateSchema(oshaLogs);
export const SelectOshaLogsSchema = createSelectSchema(oshaLogs);

export type CreateOshaLogs = InferInsertModel<typeof oshaLogs>;
export type OshaLogs = InferSelectModel<typeof oshaLogs>;

export const CreateIncidentsOshaSchema = createInsertSchema(incidentsOsha);
export const UpdateIncidentsOshaSchema = createUpdateSchema(incidentsOsha);
export const SelectIncidentsOshaSchema = createSelectSchema(incidentsOsha);

export type CreateIncidentsOsha = InferInsertModel<typeof incidentsOsha>;
export type IncidentsOsha = InferSelectModel<typeof incidentsOsha>;

export const CreateFileSchema = createInsertSchema(files);
export const UpdateFileSchema = createUpdateSchema(files);
export const SelectFileSchema = createSelectSchema(files);

export const UpdateFilePublicSchema = UpdateFileSchema.extend({
  roleId: z.string().min(1),
});

export const TransientFileSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  url: z.string(),
  type: z.string(),
  size: z.number(),
});

export type CreateFile = InferInsertModel<typeof files>;
export type File = InferSelectModel<typeof files>;

// Asset Schema and Type
export const AssetSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
});

export type Asset = z.infer<typeof AssetSchema>;

// Generic Pagination Schemas and Types
export const PaginationInputSchema = z.object({
  page: z.number().min(1).default(1).optional(),
  limit: z.number().min(1).max(100).default(50).optional(),
  search: z.string().optional(),
});
// Generic Sorting Schemas and Types
export const SortInputSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

export type SortInput = z.infer<typeof SortInputSchema>;

export const AssetSearchInputSchema = PaginationInputSchema.extend({
  locationId: z.string().optional(),
});

// Location Schema and Type
export const LocationSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type Location = z.infer<typeof LocationSchema>;

export const LocationSearchInputSchema = PaginationInputSchema.extend({
  restrictionsLevel: z.number().optional(),
});

export type LocationSearchInput = z.infer<typeof LocationSearchInputSchema>;

export type PaginationInput = z.infer<typeof PaginationInputSchema>;
export type AssetSearchInput = z.infer<typeof AssetSearchInputSchema>;

// Generic pagination response type
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const ListIncidentSchema = PaginationInputSchema.and(SortInputSchema).and(
  z.object({
    status: z.array(z.enum(statusEnum.enumValues)).optional(),
    type: z.array(z.enum(reportTypeEnum.enumValues)).optional(),
    severity: z.array(z.enum(severityEnum.enumValues)).optional(),
    oshaReportable: z.boolean().optional(),
    archived: z.boolean().optional(),
    locationIds: z.array(z.string()).optional(),
  }),
);

export const ListCapasSchema = PaginationInputSchema.and(SortInputSchema).and(
  z.object({
    status: z.array(z.enum(statusEnum.enumValues)).optional(),
    type: z.array(z.enum(capaTypeEnum.enumValues)).optional(),
    priority: z.array(z.enum(capaPriorityEnum.enumValues)).optional(),
    owner: z.array(z.string()).optional(),
    dueDateStart: z.coerce.date().optional(),
    dueDateEnd: z.coerce.date().optional(),
    includeArchived: z.boolean().optional(),
    tags: z.array(z.enum(capaTagsEnum.enumValues)).optional(),
  }),
);

export const GetPresignedUrlInputSchema = CreateFileSchema.pick({
  fileName: true,
  fileSize: true,
  mimeType: true,
  entityType: true,
  entityId: true,
}).extend({
  entityType: z.string().min(1),
  fileSize: z.number().positive('File size must be positive'),
});

export const GetPresignedUrlInputPublicSchema = GetPresignedUrlInputSchema.extend({
  roleId: z.string().min(1),
});

export const ListFilesSchema = z.object({
  entityType: z.string().min(1),
  entityId: z.string().min(1),
  status: FileStatusSchema.optional(),
});

export const PublicSearchSchema = z.object({
  roleId: z.string().min(1, 'Role ID is required'),
  search: z.string().optional().default(''),
  limit: z.number().min(1).max(100).optional(),
  locationId: z.string().optional(),
  objectId: z.array(z.string()).optional(),
});

export type PublicSearchInput = z.infer<typeof PublicSearchSchema>;

export const CreateWorkOrderFromCapaSchema = CreateCapasSchema.pick({
  title: true,
  actionsToAddress: true,
  priority: true,
  dueDate: true,
  locationId: true,
  assetId: true,
})
  .extend({
    id: z.string().min(1, 'CAPA ID is required'),
    slug: z.string().min(1, 'Slug is required'),
    userAssignedTo: z.string().optional(),
  })
  .extend({ dueDate: z.string().min(1, 'Due date is required') }); // avoid conflicts with the dueDate type from the schema

export type CreateWorkOrderFromCapaInput = z.infer<typeof CreateWorkOrderFromCapaSchema>;

// Work Order Search Schema extending PaginationInputSchema for consistency with other entities
export const WorkOrderSearchInputSchema = PaginationInputSchema.extend({
  capaId: z.array(z.string()),
  sort: z.string().optional().default('createdAt DESC'),
});

export const CountWorkOrdersByCapaIdSchema = z.object({
  capaId: z.array(z.string()),
});

export type WorkOrderSearchInput = z.infer<typeof WorkOrderSearchInputSchema>;
export type CountWorkOrdersByCapaIdInput = z.infer<typeof CountWorkOrdersByCapaIdSchema>;

type RawParseObject = {
  __type?: string;
  className?: string;
  objectId: string;
};

type UpkeepAsset = {
  id: string;
  Name: string;
};

// Raw UpKeep API Work Order response structure
export interface RawWorkOrderFromApi {
  id: string;
  workOrderNumber: string;
  mainDescription: string;
  currentStatus: string;
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder: RawParseObject;
  objectAsset: UpkeepAsset;
  userAssignedTo: RawParseObject;
}

// Work Order Types for UpKeep API responses
export type WorkOrder = {
  id: string;
  workOrderNumber: string;
  title: string;
  currentStatus: string;
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  assignedTo: string;
  locationId: string;
  assetId: string;
  assetName: string;
};

export const CreateAccessPointSchema = createInsertSchema(accessPoints);
export const UpdateAccessPointSchema = createUpdateSchema(accessPoints);
export const SelectAccessPointSchema = createSelectSchema(accessPoints);

export const CreateAccessPointFormSchema = CreateAccessPointSchema.pick({
  name: true,
  locationId: true,
});

export type CreateAccessPoint = InferInsertModel<typeof accessPoints>;
export type AccessPoint = InferSelectModel<typeof accessPoints>;

export const ListAccessPointsSchema = PaginationInputSchema.and(SortInputSchema).and(
  z.object({
    includeArchived: z.boolean().optional(),
    locationId: z.array(z.string()).optional(),
    status: z.enum(accessPointStatusEnum.enumValues).optional(),
    createdBy: z.array(z.string()).optional(),
    createdDateStart: z.coerce.date().optional(),
    createdDateEnd: z.coerce.date().optional(),
  }),
);

export type CreateWorkOrderParams = {
  mainDescription: string; // Title
  note: string; // Description
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder?: string;
  objectAsset?: string;
  userAssignedTo?: string;
  capaId: string;
};

type BaseAssetSearchParams = {
  search?: string;
  objectLocation?: string[];
};

export type GetAssetsCountParams = BaseAssetSearchParams;

export type GetAssetsParams = BaseAssetSearchParams & {
  id?: string[] | string;
  limit: number;
  offset: number;
  sort?: string;
};

export const IncidentNotificationSchema = SelectIncidentSchema.omit({
  type: true,
  category: true,
  archived: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
  rootCause: true,
  otherRootCause: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
});

export const CreateIncidentFormSchema = CreateIncidentSchema.omit({
  id: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  archived: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
  status: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
  mediaFiles: z.array(z.instanceof(File)).optional(),
});

export const EditIncidentFormSchema = UpdateIncidentSchema.omit({
  aiConfidenceScore: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
  mediaFiles: z.array(z.instanceof(File)).optional(),
});

export const CreateIncidentFormPublicSchema = CreateIncidentFormSchema.extend({
  name: z.string().min(1),
  email: z.string().email(),
  roleId: z.string().min(1),
});

export const CreateCapasFormSchema = CreateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiConfidenceScore: true,
  archived: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  attachmentsFiles: z.array(z.instanceof(File)).optional(),
});

export const EditCapasFormSchema = UpdateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiSuggestedAction: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  attachmentsFiles: z.array(z.instanceof(File)).optional(),
});
