import { router as trpcRouter } from '@server/trpc/router';
import { createTrpcContext } from '@server/trpc/trpc';
import { logger } from '@server/utils/logger';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import express, { Express } from 'express';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import ViteExpress from 'vite-express';
import { env } from '../env';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app: Express = express();

app.get('/health', (_, res) => {
  res.status(200).json({
    status: 'ok',
  });
});

app.use(
  '/ehs/trpc',
  createExpressMiddleware({
    router: trpcRouter,
    createContext: createTrpcContext,
    maxBodySize: 10 * 1024 * 1024, // 10MB
  }),
);

// Development mode: ViteExpress will handle serving the client
// Production mode: Serve static files from the dist directory
app.use('/*splat', (_, res, next) => {
  res.set('Content-Security-Policy', `frame-ancestors 'self' ${process.env.UPKEEP_APP_URL};`);
  next();
});

if (env.NODE_ENV === 'production' || env.NODE_ENV === 'staging') {
  app.use(express.static(join(__dirname, '../dist')));

  // Handle all other routes by serving index.html
  app.get('/*splat', (_, res) => {
    res.sendFile(join(__dirname, '../dist/index.html'));
  });
}

ViteExpress.listen(app, env.PORT, () => {
  logger.info('Server started', { port: env.PORT });
});
