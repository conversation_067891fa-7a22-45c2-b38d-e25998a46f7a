import { getUserPublic, getUsers } from '@server/services/user.service';
import { loggedProcedure, trpc } from '@server/trpc/trpc';
import { z } from 'zod';

export const userRouter = trpc.router({
  me: loggedProcedure.query(async ({ ctx }) => {
    return ctx.user;
  }),

  getUsers: loggedProcedure
    .input(
      z.object({
        search: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return await getUsers({ headers: ctx.req.headers, search: input.search });
    }),

  searchUsers: loggedProcedure
    .input(
      z.object({
        search: z.string().optional(),
        objectId: z.array(z.string()).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return await getUserPublic({
        roleId: ctx.user.roleId,
        search: input?.search,
        objectId: input?.objectId,
        headers: ctx.req.headers,
      });
    }),
});
