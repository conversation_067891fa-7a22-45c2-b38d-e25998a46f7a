import { getFiles, getPresignedReadUrl, getPresignedUrl, removeFiles, updateFile } from '@server/services/file.service';
import {
  GetPresignedUrlInputPublicSchema,
  GetPresignedUrlInputSchema,
  IdArraySchema,
  ListFilesSchema,
  UpdateFilePublicSchema,
  UpdateFileSchema
} from '@shared/schema.types';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';
import { loggedProcedure, publicProcedure, trpc } from '../trpc';

export const fileRouter = trpc.router({
  getPresignedUrlPublic: publicProcedure.input(GetPresignedUrlInputPublicSchema).mutation(async ({ input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      { roleId: input.roleId },
    );
  }),

  getPresignedUrl: loggedProcedure.input(GetPresignedUrlInputSchema).mutation(async ({ ctx, input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      ctx.user,
    );
  }),

  getPresignedReadUrl: publicProcedure.input(z.object({ s3Key: z.string() })).query(async ({ input }) => {
    return await getPresignedReadUrl(input.s3Key);
  }),

  updatePublic: publicProcedure.input(UpdateFilePublicSchema).mutation(async ({ input }) => {
    const { id, roleId, ...rest } = input;

    if (!id || !roleId || !rest.s3Key) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'File ID, role ID & s3 key is required',
      });
    }

    return await updateFile(id, rest, { roleId });
  }),

  update: loggedProcedure.input(UpdateFileSchema).mutation(async ({ ctx, input }) => {
    const { id, ...rest } = input;

    if (!id || !rest.s3Key) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'File ID & s3 key is required',
      });
    }

    return await updateFile(id, rest, ctx.user);
  }),

  getFiles: loggedProcedure.input(ListFilesSchema).query(async ({ ctx, input }) => {
    return await getFiles(input, ctx.user);
  }),

  removeFiles: loggedProcedure.input(IdArraySchema).mutation(async ({ input }) => {
    return await removeFiles(input);
  }),
});
