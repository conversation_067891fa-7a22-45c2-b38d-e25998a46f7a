import { getAssets } from '@server/services/asset.service';
import {
  handleIncidentNotifications,
  handleIncidentPublicNotifications,
  sendIncidentSubmittedNotification,
} from '@server/services/email/incident-notification.service';
import {
  createIncident,
  createIncidentPublic,
  getIncidentById,
  listIncidents,
  updateIncident,
} from '@server/services/incident.service';
import { getLocationById, getLocations } from '@server/services/location.service';
import { getUserById, getUserPublic } from '@server/services/user.service';
import { loggedProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import { statusEnum } from '@shared/schema';
import {
  Asset,
  CreateIncidentFormPublicSchema,
  CreateIncidentFormSchema,
  EditIncidentFormSchema,
  IdSchema,
  ListIncidentSchema,
  Location,
  User
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const incidentRouter = trpc.router({
  createPublic: publicProcedure.input(CreateIncidentFormPublicSchema).mutation(async ({ input }) => {
    const users = await getUserPublic({ roleId: input.roleId, search: input.email });

    const user = users.at(0);

    const createdIncident = await createIncidentPublic(
      input,
      {
        email: input.email,
        name: input.name,
      },
      user,
    );

    if (createdIncident) {
      await handleIncidentPublicNotifications(createdIncident, { ...user, email: input.email });
    }

    return createdIncident;
  }),
  create: loggedProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE)
    .input(CreateIncidentFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdIncident = await createIncident(input, ctx.user);

      if (createdIncident) {
        let location: Location | undefined;
        if (createdIncident.locationId) {
          location = await getLocationById(createdIncident.locationId, ctx.req.headers);
        }
        await sendIncidentSubmittedNotification({ ...createdIncident, location }, ctx.user);
      }

      return createdIncident;
    }),

  getById: loggedProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      if (!input?.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Incident ID is required',
        });
      }

      const incident = await getIncidentById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!incident) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Incident with ID ${input.id} not found`,
        });
      }

      let user: User | undefined;

      if (incident?.reportedBy) {
        user = await getUserById(incident?.reportedBy, ctx.req.headers);
      }

      let assets: Asset[] = [];

      let location: Location | undefined;

      if (incident?.assetIds && incident?.assetIds.length > 0) {
        assets = await getAssets(
          {
            id: incident?.assetIds,
            limit: 100,
            offset: 0,
          },
          ctx.req.headers,
        );
      }

      if (incident?.locationId) {
        location = await getLocationById(incident?.locationId, ctx.req.headers);
      }

      return {
        ...incident,
        reportedByUser: user,
        assets,
        location,
      };
    }),

  list: loggedProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
    .input(ListIncidentSchema)
    .query(async ({ ctx, input }) => {
      const paginatedIncidents = await listIncidents(input, ctx.user, ctx.needPartialCheck || false);

      const locationIdsInIncidents = paginatedIncidents.data
        .map((incident) => incident.locationId)
        .filter(Boolean) as string[];

      const locations = await getLocations(
        {
          id: locationIdsInIncidents,
          limit: 100,
          offset: 0,
        },
        ctx.req.headers,
      );

      const mappedLocations = locations.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      const incidentsWithLocation = paginatedIncidents.data.map((incident) => {
        const location = mappedLocations[incident.locationId || ''];

        if (!location) {
          return {
            ...incident,
            location: undefined,
          };
        }

        return {
          ...incident,
          location,
        };
      });

      return {
        ...paginatedIncidents,
        data: incidentsWithLocation,
      };
    }),

  update: loggedProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT)
    .input(EditIncidentFormSchema)
    .mutation(async ({ input, ctx }) => {
      if (!input?.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Incident ID is required',
        });
      }

      const updatedIncident = await updateIncident(input.id, input, ctx.user, ctx.needPartialCheck || false);

      if (statusEnum.enumValues.includes(updatedIncident.action as (typeof statusEnum.enumValues)[number])) {
        await handleIncidentNotifications(updatedIncident, ctx.user);
      }

      return updatedIncident;
    }),
});
