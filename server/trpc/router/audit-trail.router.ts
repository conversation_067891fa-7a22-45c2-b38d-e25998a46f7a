import { getAuditTrail } from '@server/services/audit-trail.service';
import { getUserById } from '@server/services/user.service';
import { loggedProcedure, trpc } from '@server/trpc/trpc';
import { GetAuditTrailSchema, User } from '@shared/schema.types';

export const auditTrailRouter = trpc.router({
  get: loggedProcedure.input(GetAuditTrailSchema).query(async ({ input, ctx }) => {
    const auditTrail = await getAuditTrail(input.entityId, ctx.user);

    const auditTrailWithUser = auditTrail.map(async (audit) => {
      let user: User | undefined;
      if (audit.userId) {
        user = await getUserById(audit.userId, ctx.req.headers);
      }
      return {
        ...audit,
        user,
      };
    });

    return Promise.all(auditTrailWithUser);
  }),
});
