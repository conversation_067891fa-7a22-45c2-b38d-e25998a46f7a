import {
  createAccessPoint,
  getAccessPointById,
  getAccessPoints,
  updateAccessPoint,
} from '@server/services/access-point.service';
import { loggedProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import {
  CreateAccessPointFormSchema,
  IdSchema,
  ListAccessPointsSchema,
  RoleIdSchema,
  UpdateAccessPointSchema,
} from '@shared/schema.types';
import { TRPCError } from '@trpc/server';
import { MODULES, ALLOWED_ACTIONS } from '@shared/user-permissions';

export const accessPointRouter = trpc.router({
  create: loggedProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE)
    .input(CreateAccessPointFormSchema)
    .mutation(async ({ input, ctx }) => {
      return await createAccessPoint(
        {
          ...input,
        },
        ctx.user,
      );
    }),

  update: loggedProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EDIT)
    .input(UpdateAccessPointSchema.partial().extend({ id: IdSchema.shape.id }))
    .mutation(async ({ input: { id, ...data }, ctx }) => {
      if (!id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Access point ID is required',
        });
      }
      if (!data) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Access point data is required',
        });
      }

      return await updateAccessPoint(id, data, ctx.user);
    }),

  list: loggedProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(ListAccessPointsSchema.default({}))
    .query(async ({ input, ctx }) => {
      return await getAccessPoints(input, ctx.user);
    }),

  getById: loggedProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const accessPoint = await getAccessPointById(input.id, ctx.user);
      if (!accessPoint) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Access point not found',
        });
      }
      return accessPoint;
    }),

  getByIdPublic: publicProcedure.input(IdSchema.and(RoleIdSchema)).query(async ({ input }) => {
    const accessPoint = await getAccessPointById(input.id, { roleId: input.roleId });
    if (!accessPoint) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Access point not found',
      });
    }
    return accessPoint;
  }),
});
