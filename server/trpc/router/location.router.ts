import { getLocations, getLocationsCount, searchLocationsPublic } from '@server/services/location.service';
import { loggedProcedure, trpc, publicProcedure } from '@server/trpc/trpc';
import { calculateOffset, createPaginatedResponse } from '@server/utils/pagination';
import { Location, LocationSearchInputSchema, PaginatedResponse, PublicSearchSchema } from '@shared/schema.types';

/**
 * Location Router with Pagination
 *
 * This router follows the same pagination pattern as the asset router.
 * It supports search, pagination, and restrictions level filtering.
 */

export const locationRouter = trpc.router({
  search: loggedProcedure
    .input(LocationSearchInputSchema.default({ page: 1, limit: 10, search: '' }))
    .query(async ({ input, ctx }): Promise<PaginatedResponse<Location>> => {
      const { page = 1, limit = 10, search } = input;

      // Calculate offset for pagination
      const offset = calculateOffset(page, limit);

      // Get total count and data in parallel
      const [total, locations] = await Promise.all([
        getLocationsCount({ search }, ctx.req.headers),
        getLocations(
          {
            search,
            limit,
            offset,
            sort: 'createdAt DESC',
          },
          ctx.req.headers,
        ),
      ]);

      // Return paginated response
      return createPaginatedResponse(locations, { page, limit, total });
    }),

  // Public location search endpoint - requires roleId and authentication headers
  searchPublic: publicProcedure.input(PublicSearchSchema).query(async ({ input }): Promise<Location[]> => {
    return await searchLocationsPublic({
      roleId: input.roleId,
      search: input.search,
      limit: input.limit,
    });
  }),
});
