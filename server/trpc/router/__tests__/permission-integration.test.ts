import { describe, it, expect, vi, beforeEach } from 'vitest';
import { incidentRouter } from '../incident.router';
import { createIncident, getIncidentById } from '../../../services/incident.service';
import { TRPCError } from '@trpc/server';
import { createId } from '@paralleldrive/cuid2';
import {
  getUserById,
  hasPermission,
  userHasIncidentPermission,
  canEditIncident,
  canExportIncident,
} from '../../../services/user.service';
import {
  createAdminUser,
  createTechnicianUser,
  createUnauthorizedUser,
  createUserWithCustomPermissions,
  createMockContext,
  type User,
} from './test-utils';
import { MODULES, ALLOWED_ACTIONS, PERMISSION_LEVELS } from '../../../../shared/user-permissions';

// Mock services
vi.mock('../../../services/incident.service', () => ({
  createIncident: vi.fn(),
  getIncidentById: vi.fn(),
  isResourceOwner: vi.fn(),
}));

vi.mock('../../../services/user.service', () => ({
  getUserById: vi.fn(),
  hasPermission: vi.fn(),
  userHasIncidentPermission: vi.fn(),
  canEditIncident: vi.fn(),
  canExportIncident: vi.fn(),
}));

describe('Permission Integration Tests', () => {
  const mockIncident = {
    id: createId(),
    slug: 'INC-001',
    title: 'Test Incident',
    description: 'Test Description',
    reportedBy: 'user-123',
    type: 'incident',
    category: 'chemical',
    severity: 'high',
    status: 'open',
    reportedAt: new Date(),
    upkeepCompanyId: 'company-1',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(createAdminUser());
    vi.mocked(getIncidentById).mockResolvedValue(mockIncident);
  });

  describe('Admin User Permissions', () => {
    const adminUser = createAdminUser();
    const adminContext = createMockContext(adminUser);

    beforeEach(() => {
      // Admin should have full permissions
      vi.mocked(hasPermission).mockReturnValue(true);
      vi.mocked(userHasIncidentPermission).mockReturnValue(true);
      vi.mocked(canEditIncident).mockReturnValue(true);
      vi.mocked(canExportIncident).mockReturnValue(true);
    });

    it('should allow admin to create incidents', async () => {
      vi.mocked(createIncident).mockResolvedValue(undefined);

      await expect(
        incidentRouter.createCaller(adminContext).create({
          title: 'Admin Incident',
          description: 'Created by admin',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: new Date(),
        }),
      ).resolves.not.toThrow();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE, false);
    });

    it('should allow admin to view any incident', async () => {
      await expect(incidentRouter.createCaller(adminContext).getById({ id: mockIncident.id })).resolves.toBeDefined();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });

  describe('Technician User Permissions', () => {
    const technicianUser = createTechnicianUser();
    const technicianContext = createMockContext(technicianUser);

    beforeEach(() => {
      // Technician has limited permissions
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        if (module === MODULES.EHS_INCIDENT) {
          return [ALLOWED_ACTIONS.CREATE, ALLOWED_ACTIONS.VIEW, ALLOWED_ACTIONS.EDIT].includes(action);
        }
        return false;
      });
      vi.mocked(userHasIncidentPermission).mockReturnValue(true);
      vi.mocked(canEditIncident).mockReturnValue(false); // Partial permissions need contextual check
      vi.mocked(canExportIncident).mockReturnValue(false);
    });

    it('should allow technician to create incidents', async () => {
      vi.mocked(createIncident).mockResolvedValue(undefined);

      await expect(
        incidentRouter.createCaller(technicianContext).create({
          title: 'Technician Incident',
          description: 'Created by technician',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: new Date(),
        }),
      ).resolves.not.toThrow();
    });

    it('should allow technician to view incidents (partial permission)', async () => {
      await expect(
        incidentRouter.createCaller(technicianContext).getById({ id: mockIncident.id }),
      ).resolves.toBeDefined();
    });
  });

  describe('Unauthorized User', () => {
    const unauthorizedUser = createUnauthorizedUser();
    const unauthorizedContext = createMockContext(unauthorizedUser);

    beforeEach(() => {
      // No permissions
      vi.mocked(hasPermission).mockReturnValue(false);
      vi.mocked(userHasIncidentPermission).mockReturnValue(false);
      vi.mocked(canEditIncident).mockReturnValue(false);
      vi.mocked(canExportIncident).mockReturnValue(false);
    });

    it('should deny unauthorized user from creating incidents', async () => {
      await expect(
        incidentRouter.createCaller(unauthorizedContext).create({
          title: 'Unauthorized Incident',
          description: 'Should fail',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: new Date(),
        }),
      ).rejects.toThrow(TRPCError);
    });

    it('should deny unauthorized user from viewing incidents', async () => {
      await expect(incidentRouter.createCaller(unauthorizedContext).getById({ id: mockIncident.id })).rejects.toThrow(
        TRPCError,
      );
    });
  });

  describe('Partial Permissions with Resource Ownership', () => {
    const partialUser = createUserWithCustomPermissions([
      {
        dataObject: MODULES.EHS_INCIDENT,
        action: ALLOWED_ACTIONS.VIEW,
        permissionLevel: PERMISSION_LEVELS.PARTIAL,
      },
      {
        dataObject: MODULES.EHS_INCIDENT,
        action: ALLOWED_ACTIONS.EDIT,
        permissionLevel: PERMISSION_LEVELS.PARTIAL,
      },
    ]);
    const partialContext = createMockContext(partialUser);

    beforeEach(() => {
      // Partial permissions - need contextual checks
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        const permission = user.permissions.find((p) => p.dataObject === module && p.action === action);
        return !!permission;
      });
    });

    it('should allow partial user to view their own incidents', async () => {
      // Mock that user owns this incident
      const ownedIncident = { ...mockIncident, reportedBy: partialUser.id };
      vi.mocked(getIncidentById).mockResolvedValue(ownedIncident);
      vi.mocked(canEditIncident).mockReturnValue(true); // They own it

      await expect(
        incidentRouter.createCaller(partialContext).getById({ id: ownedIncident.id }),
      ).resolves.toBeDefined();
    });

    it('should demonstrate permission middleware is called', async () => {
      vi.mocked(getIncidentById).mockResolvedValue(mockIncident);

      await incidentRouter.createCaller(partialContext).getById({ id: mockIncident.id });

      // Verify the permission system was called
      expect(hasPermission).toHaveBeenCalledWith(partialUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });

  describe('Null User Context', () => {
    const nullContext = createMockContext(null);

    it('should deny access when user is null', async () => {
      await expect(
        incidentRouter.createCaller(nullContext).create({
          title: 'Should Fail',
          description: 'No user',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: new Date(),
        }),
      ).rejects.toThrow(TRPCError);
    });

    it('should deny getById when user is null', async () => {
      await expect(incidentRouter.createCaller(nullContext).getById({ id: mockIncident.id })).rejects.toThrow(
        TRPCError,
      );
    });
  });
});
