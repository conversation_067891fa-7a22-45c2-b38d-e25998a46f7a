import { describe, it, expect, vi, beforeEach } from 'vitest';
import { userRouter } from '../user.router';
import { createAdminUser, createMockContext } from './test-utils';

describe('User Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('me procedure', () => {
    it('should return the user context', async () => {
      const result = await userRouter.createCaller(mockContext).me();

      expect(result).toEqual(mockUser);
    });

    it('should return null if no user in context', async () => {
      await expect(userRouter.createCaller(nullUserContext).me()).rejects.toThrow();
    });
  });
});
