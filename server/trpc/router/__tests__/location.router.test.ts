import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TRPCError } from '@trpc/server';
import { locationRouter } from '../location.router';
import { getLocations, getLocationsCount, searchLocationsPublic } from '../../../services/location.service';
import { createAdminUser, createMockContext } from './test-utils';
import type { Location } from '@shared/schema.types';

// Mock the rate limiter to prevent rate limiting during tests
vi.mock('@trpc-limiter/memory', () => ({
  createTRPCStoreLimiter: vi.fn(
    () =>
      async ({ next }: any) =>
        await next(),
  ),
  defaultFingerPrint: vi.fn(() => 'test-fingerprint'),
}));

// Mock location service
vi.mock('../../../services/location.service', () => ({
  getLocations: vi.fn(),
  getLocationsCount: vi.fn(),
  searchLocationsPublic: vi.fn(),
}));

// Mock pagination utilities
vi.mock('../../../utils/pagination', () => ({
  calculateOffset: vi.fn((page, limit) => (page - 1) * limit),
  createPaginatedResponse: vi.fn((data, { page, limit, total }) => ({
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNextPage: page < Math.ceil(total / limit),
      hasPreviousPage: page > 1,
    },
  })),
}));

describe('Location Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('search procedure', () => {
    it('should return paginated locations with default parameters', async () => {
      const mockLocations: Location[] = [
        { id: 'location-1', name: 'Inventory Control Room' },
        { id: 'location-2', name: 'Tool Maintenance Room' },
      ];

      vi.mocked(getLocationsCount).mockResolvedValue(2);
      vi.mocked(getLocations).mockResolvedValue(mockLocations);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({});

      expect(getLocationsCount).toHaveBeenCalledWith({ search: undefined }, mockContext.req.headers);
      expect(getLocations).toHaveBeenCalledWith(
        {
          search: undefined,
          limit: 10,
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual({
        data: mockLocations,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    });

    it('should handle search with custom parameters', async () => {
      const mockLocations: Location[] = [{ id: 'location-1', name: 'Room 101' }];

      vi.mocked(getLocationsCount).mockResolvedValue(15);
      vi.mocked(getLocations).mockResolvedValue(mockLocations);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({
        page: 2,
        limit: 5,
        search: 'room',
      });

      expect(getLocationsCount).toHaveBeenCalledWith({ search: 'room' }, mockContext.req.headers);
      expect(getLocations).toHaveBeenCalledWith(
        {
          search: 'room',
          limit: 5,
          offset: 5,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual({
        data: mockLocations,
        pagination: {
          page: 2,
          limit: 5,
          total: 15,
          totalPages: 3,
          hasNextPage: true,
          hasPreviousPage: true,
        },
      });
    });

    it('should handle empty search results', async () => {
      vi.mocked(getLocationsCount).mockResolvedValue(0);
      vi.mocked(getLocations).mockResolvedValue([]);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'nonexistent' });

      expect(result).toEqual({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    });

    it('should validate input parameters', async () => {
      const caller = locationRouter.createCaller(mockContext);

      await expect(caller.search({ page: 0 })).rejects.toThrow();
      await expect(caller.search({ limit: 101 })).rejects.toThrow();
      await expect(caller.search({ limit: 0 })).rejects.toThrow();
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(locationRouter.createCaller(nullUserContext).search({})).rejects.toThrow(TRPCError);
    });
  });

  describe('searchPublic procedure', () => {
    it('should return locations without authentication', async () => {
      const mockLocations: Location[] = [
        { id: 'location-1', name: 'Public Room 1' },
        { id: 'location-2', name: 'Public Room 2' },
      ];

      vi.mocked(searchLocationsPublic).mockResolvedValue(mockLocations);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.1' } } };
      const caller = locationRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({ roleId: 'test-role-id', search: 'public' });

      expect(result).toEqual(mockLocations);
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        roleId: 'test-role-id',
        search: 'public',
        limit: undefined,
      });
    });

    it('should handle empty public search results', async () => {
      vi.mocked(searchLocationsPublic).mockResolvedValue([]);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.2' } } };
      const caller = locationRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({ roleId: 'test-role-id', search: 'nonexistent' });

      expect(result).toEqual([]);
    });
  });
});
