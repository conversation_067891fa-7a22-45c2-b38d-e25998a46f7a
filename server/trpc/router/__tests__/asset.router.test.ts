import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TRPCError } from '@trpc/server';
import { assetRouter } from '../asset.router';
import { createAdminUser, createMockContext } from './test-utils';
import type { Asset } from '@shared/schema.types';

// Mock the asset services
vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn(),
  getAssetsCount: vi.fn(),
  searchAssetsPublic: vi.fn(),
}));

// Mock pagination utilities
vi.mock('@server/utils/pagination', () => ({
  calculateOffset: vi.fn((page, limit) => (page - 1) * limit),
  createPaginatedResponse: vi.fn((data, { page, limit, total }) => ({
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNextPage: page < Math.ceil(total / limit),
      hasPreviousPage: page > 1,
    },
  })),
}));

// Import mocked services for type checking
import { getAssets, getAssetsCount, searchAssetsPublic } from '@server/services/asset.service';

describe('Asset Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('search procedure', () => {
    it('should return paginated assets with default parameters', async () => {
      const mockAssets: Asset[] = [
        { id: 'asset-1', name: 'Test Asset 1', description: 'Description 1' },
        { id: 'asset-2', name: 'Test Asset 2', description: 'Description 2' },
      ];

      vi.mocked(getAssetsCount).mockResolvedValue(2);
      vi.mocked(getAssets).mockResolvedValue(mockAssets);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({});

      expect(getAssetsCount).toHaveBeenCalledWith({}, mockContext.req.headers);
      expect(getAssets).toHaveBeenCalledWith(
        {
          limit: 10,
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual({
        data: mockAssets,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    });

    it('should handle search with custom parameters', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Tool Box', description: 'A tool box' }];

      vi.mocked(getAssetsCount).mockResolvedValue(15);
      vi.mocked(getAssets).mockResolvedValue(mockAssets);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({
        page: 2,
        limit: 5,
        search: 'tool',
      });

      expect(getAssetsCount).toHaveBeenCalledWith({ search: 'tool' }, mockContext.req.headers);
      expect(getAssets).toHaveBeenCalledWith(
        {
          search: 'tool',
          limit: 5,
          offset: 5, // (page 2 - 1) * 5
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual({
        data: mockAssets,
        pagination: {
          page: 2,
          limit: 5,
          total: 15,
          totalPages: 3,
          hasNextPage: true,
          hasPreviousPage: true,
        },
      });
    });

    it('should handle locationId parameter and convert to objectLocation array', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Warehouse Pump', description: 'Industrial pump' }];

      vi.mocked(getAssetsCount).mockResolvedValue(1);
      vi.mocked(getAssets).mockResolvedValue(mockAssets);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({
        locationId: 'warehouse-a',
        search: 'pump',
      });

      expect(getAssetsCount).toHaveBeenCalledWith(
        { search: 'pump', objectLocation: ['warehouse-a'] },
        mockContext.req.headers,
      );
      expect(getAssets).toHaveBeenCalledWith(
        {
          search: 'pump',
          objectLocation: ['warehouse-a'],
          limit: 10,
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result.data).toEqual(mockAssets);
    });

    it('should handle empty search results', async () => {
      vi.mocked(getAssetsCount).mockResolvedValue(0);
      vi.mocked(getAssets).mockResolvedValue([]);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'nonexistent' });

      expect(result).toEqual({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    });

    it('should make parallel service calls for performance', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Asset 1', description: 'Description 1' }];

      // Add delays to verify parallel execution
      vi.mocked(getAssetsCount).mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve(1), 50)));
      vi.mocked(getAssets).mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(mockAssets), 50)),
      );

      const caller = assetRouter.createCaller(mockContext);
      const startTime = Date.now();

      await caller.search({ search: 'test' });

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete in ~50ms (parallel) rather than ~100ms (sequential)
      expect(duration).toBeLessThan(80);
      expect(getAssetsCount).toHaveBeenCalledTimes(1);
      expect(getAssets).toHaveBeenCalledTimes(1);
    });

    it('should validate input parameters', async () => {
      const caller = assetRouter.createCaller(mockContext);

      // Test invalid page (less than 1)
      await expect(caller.search({ page: 0 })).rejects.toThrow();

      // Test invalid limit (greater than 100)
      await expect(caller.search({ limit: 101 })).rejects.toThrow();

      // Test invalid limit (less than 1)
      await expect(caller.search({ limit: 0 })).rejects.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      vi.mocked(getAssetsCount).mockRejectedValue(new Error('Service error'));

      const caller = assetRouter.createCaller(mockContext);

      await expect(caller.search({ search: 'test' })).rejects.toThrow('Service error');
    });

    it('should handle getAssets service error', async () => {
      vi.mocked(getAssetsCount).mockResolvedValue(10);
      vi.mocked(getAssets).mockRejectedValue(new Error('Asset service error'));

      const caller = assetRouter.createCaller(mockContext);

      await expect(caller.search({ search: 'test' })).rejects.toThrow('Asset service error');
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(assetRouter.createCaller(nullUserContext).search({})).rejects.toThrow(TRPCError);
    });

    it('should work without locationId parameter', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Any Asset', description: 'Asset from any location' }];

      vi.mocked(getAssetsCount).mockResolvedValue(1);
      vi.mocked(getAssets).mockResolvedValue(mockAssets);

      const caller = assetRouter.createCaller(mockContext);
      await caller.search({ search: 'asset' });

      // Verify objectLocation is not included when locationId is not provided
      expect(getAssetsCount).toHaveBeenCalledWith({ search: 'asset' }, mockContext.req.headers);
      expect(getAssets).toHaveBeenCalledWith(
        {
          search: 'asset',
          limit: 10,
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );
    });

    it('should handle large page numbers correctly', async () => {
      vi.mocked(getAssetsCount).mockResolvedValue(1000);
      vi.mocked(getAssets).mockResolvedValue([]);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({ page: 50, limit: 20 });

      expect(getAssets).toHaveBeenCalledWith(
        expect.objectContaining({
          offset: 980, // (50 - 1) * 20
        }),
        mockContext.req.headers,
      );

      expect(result.pagination).toEqual({
        page: 50,
        limit: 20,
        total: 1000,
        totalPages: 50,
        hasNextPage: false,
        hasPreviousPage: true,
      });
    });
  });

  describe('searchPublic procedure', () => {
    it('should search assets with valid roleId', async () => {
      const mockAssets: Asset[] = [
        {
          id: 'RN9QcEi9O1',
          name: 'Fan Assembly-TRANE HVAC Suite B',
          description: 'Suite B',
        },
        {
          id: 'UJnih6dxAM',
          name: 'TRANE HVAC Suite B',
          description: 'Suite B',
        },
      ];

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockAssets);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.1' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        roleId: 'jTRpZXLexa',
        search: '',
        limit: 100,
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        roleId: 'jTRpZXLexa',
        search: '',
        limit: 100,
      });

      expect(result).toEqual(mockAssets);
    });

    it('should handle locationId parameter in public search', async () => {
      const mockAssets: Asset[] = [
        {
          id: 'asset-1',
          name: 'Warehouse Asset',
          description: 'Warehouse A',
        },
      ];

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockAssets);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '*********' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        roleId: 'role-123',
        search: 'warehouse',
        limit: 50,
        locationId: 'warehouse-a',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        roleId: 'role-123',
        search: 'warehouse',
        limit: 50,
        locationId: 'warehouse-a',
      });

      expect(result).toEqual(mockAssets);
    });

    it('should use default values when optional parameters are not provided', async () => {
      vi.mocked(searchAssetsPublic).mockResolvedValue([]);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '*********' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        roleId: 'jTRpZXLexa',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        roleId: 'jTRpZXLexa',
        search: '',
      });

      expect(result).toEqual([]);
    });

    it('should throw BAD_REQUEST when roleId is missing', async () => {
      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.4' } } };
      const caller = assetRouter.createCaller(publicContext as any);

      await expect(caller.searchPublic({} as any)).rejects.toThrow('Required');
    });

    it('should validate limit parameter bounds', async () => {
      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.5' } } };
      const caller = assetRouter.createCaller(publicContext as any);

      // Test limit too low
      await expect(
        caller.searchPublic({
          roleId: 'jTRpZXLexa',
          limit: 0,
        }),
      ).rejects.toThrow();

      // Test limit too high
      await expect(
        caller.searchPublic({
          roleId: 'jTRpZXLexa',
          limit: 101,
        }),
      ).rejects.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      const mockError = new Error('Service connection error');
      vi.mocked(searchAssetsPublic).mockRejectedValue(mockError);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.6' } } };
      const caller = assetRouter.createCaller(publicContext as any);

      await expect(
        caller.searchPublic({
          roleId: 'jTRpZXLexa',
        }),
      ).rejects.toThrow('Service connection error');
    });

    it('should return empty array when no assets found', async () => {
      vi.mocked(searchAssetsPublic).mockResolvedValue([]);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.7' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        roleId: 'jTRpZXLexa',
        search: 'nonexistent',
      });

      expect(result).toEqual([]);
    });

    it('should work without user authentication', async () => {
      // This test demonstrates that the public procedure doesn't require user authentication
      const contextWithoutUser = {
        req: {
          headers: { 'x-forwarded-for': '*********' },
        },
        // No user property - this would fail for loggedProcedure
      };

      const mockAssets: Asset[] = [
        {
          id: 'public-asset',
          name: 'Public Asset',
          description: 'Accessible without auth',
        },
      ];

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockAssets);

      const caller = assetRouter.createCaller(contextWithoutUser as any);
      const result = await caller.searchPublic({
        roleId: 'public-company-123',
      });

      expect(result).toEqual(mockAssets);
      expect(searchAssetsPublic).toHaveBeenCalledWith({
        roleId: 'public-company-123',
        search: '',
      });
    });

    it('should handle search with all parameters', async () => {
      const mockAssets: Asset[] = [
        {
          id: 'asset-1',
          name: 'Filtered Asset',
          description: 'Matches search criteria',
        },
      ];

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockAssets);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '*********' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        roleId: 'role-123',
        search: 'filtered',
        limit: 100,
        locationId: 'building-1',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        roleId: 'role-123',
        search: 'filtered',
        limit: 100,
        locationId: 'building-1',
      });

      expect(result).toEqual(mockAssets);
    });
  });
});
