import { createId } from '@paralleldrive/cuid2';
import { User } from '@shared/schema.types';
import {
  USER_ACCOUNTS,
  generatePermissions,
  UserPermission,
  MODULES,
  ALLOWED_ACTIONS,
  PERMISSION_LEVELS,
  AllowedUserType,
} from '@shared/user-permissions';

// Re-export types for convenience
export type { User };

/**
 * Create a mock user with proper permissions for testing
 */
export const createMockUser = (userType: 'ADMIN' | 'TECHNICIAN' = 'ADMIN', overrides?: Partial<User>): User => {
  const userAccountType = USER_ACCOUNTS[userType] as AllowedUserType;

  const baseUser: User = {
    id: createId(),
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    roleId: 'test-role-id',
    groupName: 'Test Group',
    groupId: 'test-group-id',
    userAccountType,
    permissions: generatePermissions(userAccountType),
    featureFlags: { webEHS: true },
    ...overrides,
  };

  return baseUser;
};

/**
 * Create admin user for testing
 */
export const createAdminUser = (overrides?: Partial<User>) => createMockUser('ADMIN', overrides);

/**
 * Create technician user for testing
 */
export const createTechnicianUser = (overrides?: Partial<User>) => createMockUser('TECHNICIAN', overrides);

/**
 * Create user with custom permissions for testing edge cases
 */
export const createUserWithCustomPermissions = (permissions: UserPermission[], overrides?: Partial<User>): User => {
  return createMockUser('TECHNICIAN', {
    permissions,
    ...overrides,
  });
};

/**
 * Create user with no permissions for testing unauthorized scenarios
 */
export const createUnauthorizedUser = (overrides?: Partial<User>) => {
  return createUserWithCustomPermissions([], overrides);
};

/**
 * Create mock TRPC context for testing
 */
export const createMockContext = (user: User | null = null) => ({
  user,
  req: {
    headers: {
      'auth-token': 'test-token',
      'x-user-token': 'test-user-token',
    },
  },
});

/**
 * Permission test scenarios for common use cases
 */
export const permissionTestScenarios = {
  adminUser: createAdminUser(),
  technicianUser: createTechnicianUser(),
  unauthorizedUser: createUnauthorizedUser(),
  partialEditUser: createUserWithCustomPermissions([
    {
      dataObject: MODULES.EHS_INCIDENT,
      action: ALLOWED_ACTIONS.VIEW,
      permissionLevel: PERMISSION_LEVELS.FULL,
    },
    {
      dataObject: MODULES.EHS_INCIDENT,
      action: ALLOWED_ACTIONS.EDIT,
      permissionLevel: PERMISSION_LEVELS.PARTIAL,
    },
  ]),
};
