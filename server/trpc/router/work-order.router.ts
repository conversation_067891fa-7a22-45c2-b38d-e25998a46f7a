import {
  createWorkOrderFromCapa,
  searchWorkOrdersByCapaId,
  getWorkOrdersCountByCapa,
} from '@server/services/work-order.service';
import { loggedProcedure, trpc } from '@server/trpc/trpc';
import { calculateOffset, createPaginatedResponse } from '@server/utils/pagination';
import {
  CreateWorkOrderFromCapaSchema,
  WorkOrderSearchInputSchema,
  CountWorkOrdersByCapaIdSchema,
  WorkOrder,
  PaginatedResponse,
} from '@shared/schema.types';

export const workOrderRouter = trpc.router({
  createFromCapa: loggedProcedure.input(CreateWorkOrderFromCapaSchema).mutation(async ({ input, ctx }) => {
    return await createWorkOrderFromCapa(input, ctx.req.headers);
  }),

  getByCapa: loggedProcedure
    .input(WorkOrderSearchInputSchema)
    .query(async ({ input, ctx }): Promise<PaginatedResponse<WorkOrder>> => {
      const { page = 1, limit = 10, capaId, sort } = input;

      // Calculate offset for pagination
      const offset = calculateOffset(page, limit);

      // Get total count and data in parallel
      const [total, workOrders] = await Promise.all([
        getWorkOrdersCountByCapa({ capaId }, ctx.req.headers),
        searchWorkOrdersByCapaId(
          {
            capaId,
            limit,
            offset,
            sort,
          },
          ctx.req.headers,
        ),
      ]);

      // Return paginated response
      return createPaginatedResponse(workOrders, { page, limit, total });
    }),

  getCountByCapa: loggedProcedure
    .input(CountWorkOrdersByCapaIdSchema)
    .query(async ({ input, ctx }): Promise<number> => {
      return await getWorkOrdersCountByCapa(input, ctx.req.headers);
    }),
});
