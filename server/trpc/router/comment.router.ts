import {
  createComment,
  fetchCommentById,
  fetchComments,
  deleteComment,
} from '@server/services/comment.service';
import { loggedProcedure, trpc } from '@server/trpc/trpc';
import { CreateCommentFormSchema, IdSchema, ListCommentsSchema } from '@shared/schema.types';

export const commentRouter = trpc.router({
  create: loggedProcedure.input(CreateCommentFormSchema).mutation(async ({ input, ctx }) => {
    return await createComment(input, ctx.user);
  }),
  list: loggedProcedure.input(ListCommentsSchema).query(async ({ input, ctx }) => {
    return await fetchComments(
      { entityId: input.entityId, entityType: input.entityType },
      ctx.user,
    );
  }),
  getById: loggedProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    return await fetchCommentById({ id: input.id }, ctx.user);
  }),
  delete: loggedProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    return await deleteComment({ id: input.id }, ctx.user);
  }),
});
