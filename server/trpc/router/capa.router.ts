import { getAssetById } from '@server/services/asset.service';
import { createCapa, getCapaById, listCapas, updateCapa } from '@server/services/capa.service';
import { handleCAPANotifications } from '@server/services/email/capa-notification.service';
import { getIncidentById } from '@server/services/incident.service';
import { getLocationById } from '@server/services/location.service';
import { getUsers } from '@server/services/user.service';
import { loggedProcedure, trpc } from '@server/trpc/trpc';
import {
  Asset,
  CreateCapasFormSchema,
  EditCapasFormSchema,
  IdSchema,
  ListCapasSchema,
  Location,
  User,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const capaRouter = trpc.router({
  // Create CAPA procedure - requires CAPA create permission
  create: loggedProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)
    .input(CreateCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdCapa = await createCapa(input, ctx.user!);

      let incident;
      let location;
      if (createdCapa.incidentId) {
        incident = await getIncidentById(createdCapa.incidentId, ctx.user!, ctx.needPartialCheck || false);

        if (incident?.locationId) {
          location = await getLocationById(incident.locationId, ctx.req.headers);
        }
      }

      await handleCAPANotifications(
        {
          ...createdCapa,
          incidentTitle: incident?.title,
          incidentSlug: incident?.slug,
          location: location?.name,
        },
        ctx.user!,
      );

      return createdCapa;
    }),

  // Get CAPA by ID procedure - requires CAPA view permission
  getById: loggedProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user!);

      // If CAPA not found, throw a NOT_FOUND error
      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      let asset: Asset | undefined;
      let location: Location | undefined;
      let owner: User | undefined;

      let implementedBy: User | undefined;
      let voePerformedBy: User | undefined;

      if (capa.assetId) {
        asset = await getAssetById(capa.assetId, ctx.req.headers);
      }

      if (capa.locationId) {
        location = await getLocationById(capa.locationId, ctx.req.headers);
      }

      const usersToGet = [capa.ownerId, capa.implementedBy, capa.voePerformedBy].filter(Boolean);

      const users = await getUsers({ headers: ctx.req.headers, objectId: usersToGet as string[] });

      const usersMap = users.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, User>,
      );

      owner = usersMap[capa.ownerId];

      if (capa.implementedBy) {
        implementedBy = usersMap[capa.implementedBy];
      }

      if (capa.voePerformedBy) {
        voePerformedBy = usersMap[capa.voePerformedBy];
      }

      return {
        ...capa,
        owner,
        asset,
        location,
        implementedBy,
        voePerformedBy,
      };
    }),

  getByIdForEdit: loggedProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user!);

      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      return capa;
    }),

  // List CAPAs procedure - requires CAPA view permission
  list: loggedProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(ListCapasSchema.default({ page: 1, limit: 10, search: '' }))
    .query(async ({ ctx, input }) => {
      const capas = await listCapas(input, ctx.user!);

      const userIds = Array.from(new Set(capas.data.map((capa) => capa.ownerId).filter(Boolean))) as string[];

      const users = await getUsers({ headers: ctx.req.headers, objectId: userIds });

      const usersMap = users.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, User>,
      );

      const capasWithUsers = capas.data.map((capa) => {
        const user = usersMap[capa.ownerId];
        return {
          ...capa,
          owner: user,
        };
      });

      return {
        ...capas,
        data: capasWithUsers,
      };
    }),

  // Update CAPA procedure
  update: loggedProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(EditCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      if (!input?.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'CAPA ID is required',
        });
      }

      const currentCapa = await getCapaById(input.id, ctx.user!);

      if (!currentCapa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      const updatedCapa = await updateCapa(input.id, input, ctx.user!);

      let incident;
      let location;
      if (input.status && updatedCapa.incidentId) {
        incident = await getIncidentById(updatedCapa.incidentId, ctx.user!, ctx.needPartialCheck || false);

        if (incident?.locationId) {
          location = await getLocationById(incident.locationId, ctx.req.headers);
        }

        await handleCAPANotifications(
          {
            ...updatedCapa,
            incidentTitle: incident?.title,
            incidentSlug: incident?.slug,
            location: location?.name,
          },
          ctx.user!,
          input.status,
        );
      }

      return updatedCapa;
    }),
});
