import { getAssets, getAssetsCount, searchAssetsPublic } from '@server/services/asset.service';
import { loggedProcedure, trpc, publicProcedure } from '@server/trpc/trpc';
import { calculateOffset, createPaginatedResponse } from '@server/utils/pagination';
import { Asset, AssetSearchInputSchema, PaginatedResponse, PublicSearchSchema } from '@shared/schema.types';

/**
 * Asset Router with Pagination
 *
 * This router demonstrates the generic pagination pattern that can be extended for other entities.
 *
 * To create pagination for other entities:
 * 1. Create a search input schema extending PaginationInputSchema in shared/schema.types.ts
 * 2. Create count and list service functions for your entity
 * 3. Use calculateOffset and createPaginatedResponse utilities
 *
 * Example for users:
 * const UserSearchInputSchema = PaginationInputSchema.extend({
 *   role: z.string().optional(),
 * });
 *
 * search: loggedProcedure
 *   .input(UserSearchInputSchema)
 *   .query(async ({ input, ctx }): Promise<PaginatedResponse<User>> => {
 *     const { page, limit, search, role } = input;
 *     const offset = calculateOffset(page, limit);
 *     const [total, users] = await Promise.all([
 *       getUsersCount({ search, role }, ctx.req.headers),
 *       getUsers({ search, role, limit, offset }, ctx.req.headers)
 *     ]);
 *     return createPaginatedResponse(users, { page, limit, total });
 *   }),
 */

export const assetRouter = trpc.router({
  search: loggedProcedure
    .input(AssetSearchInputSchema.default({ page: 1, limit: 10, search: '' }))
    .query(async ({ input, ctx }): Promise<PaginatedResponse<Asset>> => {
      const { page = 1, limit = 10, search, locationId } = input;

      // Calculate offset for pagination
      const offset = calculateOffset(page, limit);

      // Prepare search parameters - map locationId to objectLocation for UpKeep API
      const searchParams = {
        search,
        ...(locationId && { objectLocation: [locationId] }),
      };

      // Get total count and data in parallel
      const [total, assets] = await Promise.all([
        getAssetsCount(searchParams, ctx.req.headers),
        getAssets(
          {
            ...searchParams,
            limit,
            offset,
            sort: 'createdAt DESC',
          },
          ctx.req.headers,
        ),
      ]);

      // Return paginated response
      return createPaginatedResponse(assets, { page, limit, total });
    }),

  // Public asset search endpoint - requires only roleId
  searchPublic: publicProcedure.input(PublicSearchSchema).query(async ({ input }): Promise<Asset[]> => {
    return await searchAssetsPublic(input);
  }),
});
