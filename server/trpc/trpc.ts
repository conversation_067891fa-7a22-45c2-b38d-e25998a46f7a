import { enrichTrpcContext, createRateLimiter } from '@server/trpc/middleware';
import { logger } from '@server/utils/logger';
import { initTRPC, TRPCError } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import superjson from 'superjson';
import { ZodError } from 'zod';
import { USER_ACCOUNTS, PermissionLevel, UserPermission } from '@shared/user-permissions';
import { hasPermission } from '@server/services/user.service';

export const createTrpcContext = async ({ req }: CreateExpressContextOptions) => ({
  req,
  ...(await enrichTrpcContext(req)),
});

// Enhanced context type to include permission information
type EnhancedContext = Awaited<ReturnType<typeof createTrpcContext>> & {
  permissionLevel?: PermissionLevel;
  needPartialCheck?: boolean;
};

export const trpc = initTRPC.context<EnhancedContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

const timingMiddleware = trpc.middleware(async ({ next, path, type }) => {
  logger.info(`[TRPC] Call Started`, { path, type });
  const start = Date.now();
  const result = await next();
  const end = Date.now();
  logger.info(`[TRPC] Call Completed`, { path, type, duration: end - start });
  return result;
});

const rateLimiter = createRateLimiter();

// Base logged procedure
const baseLoggedProcedure = trpc.procedure.use(timingMiddleware).use(({ ctx, next, input }) => {
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in to access this resource',
    });
  }

  return next({
    input,
    ctx: { user: { ...ctx.user } },
  });
});

/**
 * Permission middleware factory
 */
const createPermissionMiddleware = (dataObject: UserPermission['dataObject'], action: UserPermission['action']) => {
  return trpc.middleware(async ({ ctx, next }) => {
    const user = ctx.user!;

    const hasAnyPermission = hasPermission(user, dataObject, action, false);

    if (!hasAnyPermission) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Insufficient permissions for ${action} on ${dataObject}`,
      });
    }

    const userPermission = user.permissions.find((p) => p.dataObject === dataObject && p.action === action);

    const permissionLevel = userPermission?.permissionLevel || 'none';
    const needPartialCheck = permissionLevel === 'partial';

    return next({
      ctx: {
        ...ctx,
        permissionLevel,
        needPartialCheck,
      },
    });
  });
};

/**
 * Extended logged procedure with permission checking capability
 */
const createExtendedLoggedProcedure = () => {
  type LoggedProcedure = typeof baseLoggedProcedure;

  const procedure = baseLoggedProcedure;

  const extendedProcedure = procedure as LoggedProcedure & {
    hasPermission: (dataObject: UserPermission['dataObject'], action: UserPermission['action']) => LoggedProcedure;
    adminOnly: () => LoggedProcedure;
  };

  extendedProcedure.hasPermission = (dataObject, action) => {
    return procedure.use(createPermissionMiddleware(dataObject, action)) as LoggedProcedure;
  };

  extendedProcedure.adminOnly = () => {
    return procedure.use(
      trpc.middleware(({ ctx, next }) => {
        if (!ctx.user || ctx.user.userAccountType !== USER_ACCOUNTS.ADMIN) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Admin access required',
          });
        }
        return next();
      }),
    );
  };

  return extendedProcedure;
};

export const loggedProcedure = createExtendedLoggedProcedure();
export const publicProcedure = trpc.procedure.use(timingMiddleware).use(rateLimiter);

export type TRPCServerType = typeof trpc;
