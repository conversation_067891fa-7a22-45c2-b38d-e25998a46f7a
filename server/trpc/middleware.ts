import { IncomingMessage } from 'http';
import { getMe } from '@server/services/user.service';
import { logger } from 'server/utils/logger';
import { createTRPCStoreLimiter, defaultFingerPrint } from '@trpc-limiter/memory';
import { TRPCError } from '@trpc/server';
import { env } from '../../env';
import { TRPCServerType } from './trpc';

export const enrichTrpcContext = async (req: IncomingMessage) => {
  try {
    const user = await getMe(req);
    return {
      user,
    };
  } catch (error) {
    logger.error('Error enriching TRPC context:', error);
    return {
      user: null,
    };
  }
};

// Rate limiter configuration factory
export const createRateLimiter = () => {
  // Get values with fallbacks since env might return undefined
  const windowMs = env.RATE_LIMIT_WINDOW_MS || 60000; // 1 minute default
  const maxRequests = env.RATE_LIMIT_MAX_REQUESTS || 100; // 500 requests default

  return createTRPCStoreLimiter<TRPCServerType>({
    fingerprint: (ctx) => defaultFingerPrint(ctx.req),
    windowMs, // use the fallback value
    max: maxRequests, // use the fallback value
    message: (retryAfter) =>
      `Too many requests from this IP. Please try again in ${Math.ceil(retryAfter / 1000)} seconds.`,
    onLimit: (retryAfter, ctx, fingerprint) => {
      logger.warn('Rate limit exceeded for public endpoint', {
        fingerprint,
        retryAfter,
        windowMs,
        maxRequests,
        ip: ctx.req.headers['x-forwarded-for'] || ctx.req.headers['x-real-ip'] || ctx.req.socket?.remoteAddress,
        path: ctx.req.url,
      });
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: `Too many requests. Please try again in ${Math.ceil(retryAfter / 1000)} seconds.`,
      });
    },
  });
};
