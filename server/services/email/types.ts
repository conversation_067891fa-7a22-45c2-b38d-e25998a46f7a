import { SelectCapasSchema } from '@shared/schema.types';
import { z } from 'zod';

export interface EmailRecipient {
  email: string;
  name?: string;
  type: 'to' | 'cc' | 'bcc';
}

export interface EmailAttachment {
  type: string;
  name: string;
  content: string;
}

export interface EmailOptions {
  to: EmailRecipient[];
  subject: string;
  template: string;
  templateData: Record<string, unknown>;
  attachments?: EmailAttachment[];
  tags?: string[];
}

export interface EmailService {
  sendEmail(options: EmailOptions): Promise<void>;
}

// Incident Notification Types
export interface IncidentNotificationData {
  incidentId: string;
  title: string;
  status: string;
  severity: string;
  timestamp: string;
  location: string;
  asset?: string;
  reporter: {
    name: string;
    email: string;
    role: 'admin' | 'technician' | 'public';
  };
  description?: string;
  hasViewPermission: boolean;
  incidentUrl?: string;
  isAdmin: boolean;
  isTechnicianOwner: boolean;
  previousStatus?: string;
  newStatus?: string;
  reporterName?: string;
  reporterEmail?: string;
  closedBy?: string;
  closureDate?: string;
}

// CAPA Notification Types
export interface CAPANotificationData {
  capaId: string;
  title: string;
  status: 'open' | 'in_review' | 'closed';
  dueDate: string;
  owner: {
    name: string;
    email: string;
    role: 'admin' | 'technician';
  };
  type: 'corrective' | 'preventive';
  location?: string;
  description?: string;
  isPrivate: boolean;
  linkedIncidentId?: string;
  linkedWorkOrderId?: string;
  incidentUrl?: string;
  isAdmin: boolean;
  isTechnicianOwner: boolean;
}

export type CAPANotificationType = 'capa_assigned' | 'capa_marked_complete' | 'capa_closed' | 'capa_overdue';

export const CapaNotificationSchema = SelectCapasSchema.pick({
  id: true,
  slug: true,
  title: true,
  type: true,
  priority: true,
  status: true,
  dueDate: true,
  ownerId: true,
  incidentId: true,
  createdBy: true,
  upkeepCompanyId: true,
  rcaFindings: true,
}).extend({
  location: z.string().nullable().optional(),
  incidentTitle: z.string().nullable().optional(),
  incidentSlug: z.string().nullable().optional(),
});
