import { sendEmail } from './email.service';
import { getStatusLabel } from './helpers';
import { userHasIncidentPermission } from '../user.service';
import { env } from '../../../env';
import { formatNotificationDate } from '../../utils/dates';
import { logger } from '@server/utils/logger';
import { CapaNotificationSchema } from './types';
import { z } from 'zod';
import { User, UserPublic } from '@shared/schema.types';

export const sendCAPAOverdueNotification = async (data: {
  capa: z.infer<typeof CapaNotificationSchema>;
  owner?: UserPublic;
  toNotified: { email: string; name: string; type: 'to' }[];
  daysOverdue: number;
  location?: string;
  isAdmin: boolean;
  isTechnicianOwner: boolean;
}): Promise<void> => {
  const subject = `CAPA Overdue: ${data.capa.title}`;
  const capaUrl = `${env.EHS_URL}/capas/${data.capa.id}`;

  const linkedIncidentUrl = data.capa.incidentId ? `${env.EHS_URL}/incidents/${data.capa.incidentId}` : null;

  await sendEmail({
    to: data.toNotified,
    subject,
    template: 'capa-notifications/capa-overdue.jsx',
    templateData: {
      capaId: data.capa.slug,
      title: data.capa.title,
      type: data.capa.type,
      priority: data.capa.priority,
      status: getStatusLabel(data.capa.status),
      dueDate: data.capa.dueDate ? formatNotificationDate(data.capa.dueDate) : 'Not specified',
      daysOverdue: data.daysOverdue,
      assignedTo: data.owner?.firstName ? `${data.owner.firstName} ${data.owner?.lastName}` : 'Not specified',
      location: data.location || 'Not specified',
      description: data.capa.rcaFindings || '',
      linkedIncidentUrl,
      linkedIncidentTitle: data.capa.incidentTitle,
      linkedIncidentSlug: data.capa.incidentSlug,
      linkedIncidentId: data.capa.incidentId,
      capaUrl,
      isAdmin: data.isAdmin,
      isTechnicianOwner: data.isTechnicianOwner,
    },
  });
};

const sendCAPAAssignedNotification = async (data: {
  capa: z.infer<typeof CapaNotificationSchema>;
  owner: User;
  reporter: User;
  hasViewPermission: boolean;
  isAdmin: boolean;
  isTechnicianOwner: boolean;
}): Promise<void> => {
  const subject = `CAPA Assigned: ${data.capa.title}`;
  const capaUrl = `${env.EHS_URL}/capas/${data.capa.id}`;
  const linkedIncidentUrl = data.capa.incidentId ? `${env.EHS_URL}/incidents/${data.capa.incidentId}` : null;

  await sendEmail({
    to: [
      {
        email: data.owner.email,
        name: data.owner.fullName,
        type: 'to',
      },
    ],
    subject,
    template: 'capa-notifications/capa-assigned.jsx',
    templateData: {
      capaId: data.capa.slug,
      title: data.capa.title,
      type: data.capa.type,
      priority: data.capa.priority,
      status: getStatusLabel(data.capa.status),
      dueDate: data.capa.dueDate ? formatNotificationDate(data.capa.dueDate) : 'Not specified',
      assignedTo: data.owner.fullName,
      location: data.capa.location || 'Not specified',
      description: data.capa.rcaFindings || '',
      linkedIncidentUrl,
      linkedIncidentTitle: data.capa.incidentTitle,
      linkedIncidentSlug: data.capa.incidentSlug,
      capaUrl,
      isAdmin: data.isAdmin,
      isTechnicianOwner: data.isTechnicianOwner,
    },
    tags: ['capa', 'assigned'],
  });
};

const sendCAPAClosedNotification = async (data: {
  capa: z.infer<typeof CapaNotificationSchema>;
  user: User;
  hasViewPermission: boolean;
  isAdmin: boolean;
  isTechnicianOwner: boolean;
}): Promise<void> => {
  const subject = `CAPA Closed: ${data.capa.title}`;
  const capaUrl = `${env.EHS_URL}/capas/${data.capa.id}`;
  const linkedIncidentUrl = data.capa.incidentId ? `${env.EHS_URL}/incidents/${data.capa.incidentId}` : null;

  await sendEmail({
    to: [
      {
        email: data.user.email,
        name: data.user.fullName,
        type: 'to',
      },
    ],
    subject,
    template: 'capa-notifications/capa-closed.jsx',
    templateData: {
      capaId: data.capa.slug,
      title: data.capa.title,
      type: data.capa.type,
      priority: data.capa.priority,
      status: getStatusLabel(data.capa.status),
      dueDate: data.capa.dueDate ? formatNotificationDate(data.capa.dueDate) : 'Not specified',
      assignedTo: data.user.fullName,
      closedBy: data.user.fullName,
      location: data.capa.location || 'Not specified',
      description: data.capa.rcaFindings || '',
      linkedIncidentUrl,
      linkedIncidentTitle: data.capa.incidentTitle,
      linkedIncidentSlug: data.capa.incidentSlug,
      capaUrl,
      isAdmin: data.isAdmin,
      isTechnicianOwner: data.isTechnicianOwner,
    },
    tags: ['capa', 'closed'],
  });
};

export const handleCAPANotifications = async (
  capa: z.infer<typeof CapaNotificationSchema>,
  user: User,
  previousStatus?: string,
) => {
  const hasViewPermission = userHasIncidentPermission(user, 'view');
  const isAdmin = user.userAccountType === 'Admin';
  const isTechnicianOwner = user.userAccountType === 'Technician' && capa.ownerId === user.id;

  try {
    if (previousStatus && previousStatus !== capa.status && capa.status === 'closed') {
      await sendCAPAClosedNotification({
        capa,
        user,
        hasViewPermission,
        isAdmin,
        isTechnicianOwner,
      });
    } else {
      await sendCAPAAssignedNotification({
        capa,
        owner: user,
        reporter: user,
        hasViewPermission,
        isAdmin,
        isTechnicianOwner,
      });
    }
  } catch (error) {
    logger.error('Error handling CAPA notifications:', {
      error,
      capaId: capa.id,
      userId: user.id,
    });
  }
};
