import { env } from '../../../env';
import { EmailOptions } from './types';
import mailchimp from '@mailchimp/mailchimp_transactional';
import { renderTemplate } from './renderTemplate';

const mailchimpClient = mailchimp(env.MANDRILL_API_KEY);

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  const html = await renderTemplate(options.template, options.templateData);

  const message = {
    from_email: env.MANDRILL_FROM_EMAIL,
    from_name: env.MANDRILL_FROM_NAME,
    to: options.to.map(recipient => ({
      email: recipient.email,
      name: recipient.name,
      type: recipient.type,
    })),
    subject: options.subject,
    html,
    text: '',
    attachments: options.attachments?.map(attachment => ({
      type: attachment.type,
      name: attachment.name,
      content: attachment.content,
    })),
    tags: options.tags,
  };

  await mailchimpClient.messages.send({ message });
};
