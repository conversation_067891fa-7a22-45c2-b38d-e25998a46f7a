import React from 'react';

const CapaMarkedCompleteTemplate = ({ 
  capaId, 
  title, 
  type,
  priority,
  status,
  dueDate,
  assignedTo,
  markedCompleteBy,
  location,
  description,
  linkedIncidentId,
  linkedIncidentUrl,
  linkedIncidentTitle,
  capaUrl, 
  isAdmin, 
  isTechnicianOwner 
}) => {
  const styles = {
    body: { 
      fontFamily: 'Arial, sans-serif', 
      background: '#fff', 
      color: '#222', 
      margin: 0, 
      padding: 0 
    },
    container: { 
      maxWidth: '700px', 
      margin: '32px auto', 
      background: '#fff', 
      borderRadius: '4px', 
      border: '1px solid #eee' 
    },
    content: { 
      padding: '0 32px 20px' 
    },
    header: { 
      fontSize: '18px', 
      fontWeight: 600, 
      marginBottom: '20px', 
      background: '#f3f4f6', 
      padding: '10px', 
      borderBottom: '1px solid #e5e7eb', 
      borderRadius: '4px 4px 0 0' 
    },
    subject: { 
      color: '#555', 
      fontSize: '15px', 
      marginBottom: '24px' 
    },
    sectionTitle: { 
      fontSize: '18px', 
      fontWeight: 600, 
      marginTop: '32px', 
      marginBottom: '20px' 
    },
    infoTable: { 
      width: '100%', 
      borderCollapse: 'collapse', 
      marginBottom: '24px' 
    },
    fieldBlock: { 
      marginBottom: '18px' 
    },
    label: { 
      color: '#222', 
      display: 'block', 
      fontWeight: 400, 
      textAlign: 'left' 
    },
    value: { 
      display: 'block', 
      marginTop: '10px', 
      fontWeight: 600 
    },
    badge: { 
      display: 'inline-block', 
      padding: '2px 10px', 
      borderRadius: '8px', 
      fontSize: '13px', 
      fontWeight: 600, 
      marginRight: '6px', 
      marginTop: '10px' 
    },
    badgeCorrective: { 
      background: '#2563eb', 
      color: '#fff' 
    },
    badgeHigh: { 
      background: '#fee2e2', 
      color: '#b91c1c' 
    },
    badgeStatusInreview: { 
      background: '#fef9c3', 
      color: '#b45309' 
    },
    descBlock: { 
      marginTop: '36px' 
    },
    descLabel: { 
      color: '#222', 
      display: 'block', 
      marginBottom: '8px', 
      fontWeight: 400 
    },
    descValue: { 
      display: 'block', 
      color: '#222', 
      fontWeight: 600 
    },
    linkBadge: { 
      background: '#e0e7ff', 
      color: '#2563eb', 
      borderRadius: '6px', 
      padding: '2px 8px', 
      fontSize: '13px', 
      fontWeight: 600, 
      textDecoration: 'none', 
      marginRight: '6px' 
    },
    linkIncident: { 
      color: '#2563eb', 
      textDecoration: 'underline', 
      fontSize: '14px' 
    },
    ctaBtn: { 
      display: 'inline-block', 
      background: '#ef4444', 
      color: '#fff', 
      padding: '10px 28px', 
      borderRadius: '6px', 
      fontWeight: 600, 
      textDecoration: 'none', 
      margin: '16px 0 0' 
    },
    footer: { 
      color: '#888', 
      fontSize: '13px', 
      marginTop: '32px', 
      textAlign: 'left' 
    },
    card: { 
      border: '1px solid #eee', 
      borderRadius: '10px', 
      padding: '24px', 
      marginBottom: '24px', 
      background: '#fff' 
    },
    viewSection: { 
      marginBottom: '18px', 
      border: '1px solid #eee', 
      borderRadius: '10px', 
      padding: '18px 24px', 
      background: '#fff', 
      textAlign: 'left' 
    },
    ctaCenter: { 
      textAlign: 'center' 
    },
    muted: { 
      color: '#888', 
      fontSize: '15px', 
      marginTop: '25px' 
    },
    divider: { 
      border: 'none', 
      borderTop: '1px solid #e5e7eb', 
      margin: '24px 0 28px 0' 
    }
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>CAPA Marked Complete Notification</title>
      </head>
      <body style={styles.body}>
        <div style={styles.container}>
          <div style={styles.header}>CAPA Marked Complete Notification</div>
          <div style={styles.content}>
            <div style={styles.subject}>
              <div><b>Subject:</b></div>
              <div>CAPA Marked Complete: {title}</div>
            </div>
            <hr style={styles.divider} />
            <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
            <div style={{ marginBottom: '24px' }}>A CAPA has been marked complete. The assigned user has completed their corrective or preventive action. Please review it for verification.</div>
            <div style={styles.card}>
              <table style={styles.infoTable} width="100%">
                <tbody>
                  <tr>
                    <td style={{ width: '50%', verticalAlign: 'top' }}>
                      <div style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}>CAPA Information</div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>CAPA ID</span>
                        <span style={styles.value}>{capaId}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Title</span>
                        <span style={styles.value}>{title}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Type</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeCorrective }}>{type}</span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Priority</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeHigh }}>{priority}</span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Due Date</span>
                        <span style={styles.value}>{dueDate}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Linked Incident</span>
                        <div style={styles.value}>
                          <span style={styles.linkBadge}>{linkedIncidentId}</span> 
                          <a href={linkedIncidentUrl} style={styles.linkIncident}>{linkedIncidentTitle}</a>
                        </div>
                      </div>
                    </td>
                    <td style={{ width: '50%', verticalAlign: 'top' }}>
                      <div style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}>Completion Details</div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Status</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeStatusInreview }}>{status}</span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Assigned To</span>
                        <span style={styles.value}>{assignedTo}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Marked Complete By</span>
                        <span style={styles.value}>{markedCompleteBy}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Location</span>
                        <span style={styles.value}>{location}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style={styles.descBlock}>
                <span style={styles.descLabel}>Description</span>
                <span style={styles.descValue}>{description}</span>
              </div>
            </div>
            {isAdmin && (
              <div style={styles.viewSection}>
                <div style={styles.label}>Admin View:</div>
                <div style={styles.ctaCenter}>
                  <a href={capaUrl} style={styles.ctaBtn}>Review CAPA &rarr;</a>
                </div>
              </div>
            )}
            {isTechnicianOwner && (
              <div style={styles.viewSection}>
                <div style={styles.label}>Technician View:</div>
                <div style={styles.ctaCenter}>
                  <em>Technicians do not receive this notification email.</em>
                </div>
              </div>
            )}
            <div style={styles.footer}>
              This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
    </html>
  );
};

export default CapaMarkedCompleteTemplate; 