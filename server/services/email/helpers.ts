import { statusEnum, severityEnum } from '@shared/schema';
import { STATUS_MAP, STATUS_STYLES, SEVERITY_MAP, SEVERITY_STYLES, StatusConfig } from '@shared/schema.types';

// Utility functions for status and severity
export const getStatusLabel = (status: string): string => {
  const normalizedStatus = status?.toLowerCase()?.replace(/\s+/g, '_') as (typeof statusEnum.enumValues)[number];
  return STATUS_MAP[normalizedStatus] || status;
};

export const getStatusStyle = (status: string): StatusConfig => {
  const normalizedStatus = status?.toLowerCase()?.replace(/\s+/g, '_') as (typeof statusEnum.enumValues)[number];
  return STATUS_STYLES[normalizedStatus] || STATUS_STYLES[statusEnum.enumValues[0]];
};

export const getStatusColors = (status: string): { background: string; color: string } => {
  const config = getStatusStyle(status);
  return {
    background: config.backgroundColor,
    color: config.color
  };
};

export const getSeverityLabel = (severity: string): string => {
  const normalizedSeverity = severity?.toLowerCase() as (typeof severityEnum.enumValues)[number];
  return SEVERITY_MAP[normalizedSeverity] || severity;
};

export const getSeverityStyle = (severity: string): StatusConfig => {
  const normalizedSeverity = severity?.toLowerCase() as (typeof severityEnum.enumValues)[number];
  return SEVERITY_STYLES[normalizedSeverity] || SEVERITY_STYLES[severityEnum.enumValues[2]];
};

export const getSeverityColors = (severity: string): { background: string; color: string } => {
  const config = getSeverityStyle(severity);
  return {
    background: config.backgroundColor,
    color: config.color
  };
};
