import { logger } from '@server/utils/logger';
import { IncidentNotificationSchema, User, UserPublic } from '@shared/schema.types';
import { statusEnum } from '@shared/schema';
import { z } from 'zod';
import { env } from '../../../env';
import { formatNotificationDate } from '../../utils/dates';
import { userHasIncidentPermission } from '../user.service';
import { sendEmail } from './email.service';
import { getStatusLabel } from './helpers';
import { IncidentNotificationData } from './types';
import { ALLOWED_ACTIONS, USER_ACCOUNTS } from '@shared/user-permissions';

const buildNotificationData = (incident: z.infer<typeof IncidentNotificationSchema>, user: User) => {
  const hasViewPermission = userHasIncidentPermission(user, ALLOWED_ACTIONS.VIEW);
  const isAdmin = user.userAccountType === USER_ACCOUNTS.ADMIN;
  const isTechnicianOwner = user.userAccountType === USER_ACCOUNTS.TECHNICIAN && incident.reportedBy === user.id;
  const incidentUrl = `${env.EHS_URL}/incidents/${incident.id}`;
  
  return {
    incidentId: incident.slug,
    title: incident.title,
    status: incident.status,
    severity: incident.severity,
    timestamp: formatNotificationDate(incident.reportedAt ?? new Date()),
    location: incident.location?.name ?? 'Not specified',
    asset: incident.assetIds?.[0] ?? 'Not specified',
    reporter: {
      name: user.fullName,
      email: user.email,
      role: user.userAccountType.toLowerCase() as 'admin' | 'technician',
    },
    description: incident.description ?? '',
    hasViewPermission,
    incidentUrl,
    isAdmin,
    isTechnicianOwner,
  }
};

export const sendIncidentSubmittedNotification = async (incident: z.infer<typeof IncidentNotificationSchema>, user: User): Promise<void> => {
  const notificationData = buildNotificationData(incident, user);

  const subject = `New Incident Submitted: ${notificationData.title}`;

  await sendEmail({
    to: [
      {
        email: notificationData.reporter.email,
        name: notificationData.reporter.name,
        type: 'to',
      },
    ],
    subject,
    template: 'incident-notifications/incident-submitted.jsx',
    templateData: {
      ...notificationData,
      hasViewButton: notificationData.hasViewPermission,
    },
    tags: ['incident', 'submitted'],
  });
};

const sendIncidentStatusChangedNotification = async (data: IncidentNotificationData): Promise<void> => {
  const statusForSubject = data.newStatus || data.status;
  const statusLabel = getStatusLabel(statusForSubject);
  const subject = `Incident Status Updated: Now ${statusLabel}`;

  await sendEmail({
    to: [
      {
        email: data.reporter.email,
        name: data.reporter.name,
        type: 'to',
      },
    ],
    subject,
    template: 'incident-notifications/incident-status-changed.jsx',
    templateData: {
      ...data,
      hasViewButton: data.hasViewPermission,
    },
    tags: ['incident', 'status-changed'],
  });
};

const sendIncidentClosedNotification = async (data: IncidentNotificationData): Promise<void> => {
  const subject = `Incident Closed: ${data.title}`;

  await sendEmail({
    to: [
      {
        email: data.reporter.email,
        name: data.reporter.name,
        type: 'to',
      },
    ],
    subject,
    template: 'incident-notifications/incident-closed.jsx',
    templateData: {
      ...data,
      hasViewButton: data.hasViewPermission,
    },
    tags: ['incident', 'closed'],
  });
};

export const handleIncidentNotifications = async (
  incident: z.infer<typeof IncidentNotificationSchema>,
  user: User,
) => {
  try {
    const notificationData = buildNotificationData(incident, user);

    if (incident.action === statusEnum.enumValues[2]) {
      await sendIncidentClosedNotification({
        ...notificationData,
        reporterName: user.fullName,
        reporterEmail: user.email,
        closedBy: user.fullName,
        closureDate: formatNotificationDate(new Date()),
      });
    } else {
      await sendIncidentStatusChangedNotification(notificationData);
    }
  } catch (error) {
    logger.error('Error handling incident notifications:', {
      error,
      incidentId: incident.id,
      userId: user.id,
    });
  }
};

export const handleIncidentPublicNotifications = async (
  incident: z.infer<typeof IncidentNotificationSchema>,
  user: Partial<UserPublic>,
) => {
  try {
    const incidentUrl = `${env.EHS_URL}/incidents/${incident.id}`;

    if (!user?.email) {
      logger.error('User email is required for public incident notifications', {
        incidentId: incident.id,
        userId: user?.id,
      });
      return;
    }

    const notificationData = {
      incidentId: incident.slug,
      title: incident.title,
      status: incident.status,
      severity: incident.severity,
      timestamp: formatNotificationDate(incident.reportedAt || new Date()),
      location: incident.location?.name ?? 'Not specified',
      asset: incident.assetIds?.[0] ?? 'Not specified',
      reporter: {
        name: `${user?.firstName} ${user?.lastName ?? ''}`.trim(),
        email: user.email,
        role: 'public' as const,
      },
      description: incident.description ?? '',
      incidentUrl,
      isAdmin: false,
      isTechnicianOwner: false,
      hasViewPermission: true,
    };

    const subject = `New Incident Submitted: ${notificationData.title}`;

    await sendEmail({
      to: [
        {
          email: notificationData.reporter.email,
          name: notificationData.reporter.name,
          type: 'to',
        },
      ],
      subject,
      template: 'incident-notifications/incident-submitted.jsx',
      templateData: {
        ...notificationData,
        hasViewButton: notificationData.hasViewPermission,
      },
      tags: ['incident', 'submitted'],
    });
  } catch (error) {
    logger.error('Error handling incident public notifications:', {
      error,
      incidentId: incident.id,
      userId: user?.id,
    });
  }
};
