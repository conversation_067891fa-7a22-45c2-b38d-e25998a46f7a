import fs from 'fs';
import path from 'path';
import Handlebars from 'handlebars';
import { renderToStaticMarkup } from 'react-dom/server';
import React from 'react';

export async function renderTemplate(templateRelativePath: string, data: Record<string, unknown>): Promise<string> {
  const templatePath = path.join(process.cwd(), 'server', 'services', 'email', 'templates', templateRelativePath);
  
  if (templateRelativePath.endsWith('.jsx')) {
    const templateModule = await import(`file://${templatePath}`);
    const TemplateComponent = templateModule.default || templateModule;

    return renderToStaticMarkup(React.createElement(TemplateComponent, data));
  }

  const templatePathWithExtension = templateRelativePath.endsWith('.hbs') 
    ? templateRelativePath 
    : `${templateRelativePath}.hbs`;
    
  const hbsTemplatePath = path.join(process.cwd(), 'server', 'services', 'email', 'templates', templatePathWithExtension);
  
  const templateSource = fs.readFileSync(hbsTemplatePath, 'utf-8');
  const template = Handlebars.compile(templateSource);
  return template(data);
}
