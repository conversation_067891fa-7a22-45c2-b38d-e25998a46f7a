import { format, addDays } from 'date-fns';
import { capaTypeEnum, capaPriorityEnum, rcaMethodEnum, capaTagsEnum } from '@shared/schema';
import { RootCauseSchema } from '@shared/schema.types';

/**
 * CAPA analysis prompts configuration
 */
export const getCapaPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd');

  // Example future dates for the prompt
  const nextWeekDate = format(addDays(currentDate, 7), 'yyyy-MM-dd');
  const nextMonthDate = format(addDays(currentDate, 30), 'yyyy-MM-dd');
  const tomorrow = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  // System prompt for CAPA analysis
  const systemPrompt =
    'You are an AI assistant specialized in extracting detailed and accurate structured information about Corrective and Preventive Actions (CAPAs) from transcripts. ' +
    'You are also a certified expert in occupational safety and workplace compliance, with deep knowledge of OSHA standards, labor regulations, and risk mitigation strategies. ' +
    'You understand how to assess both immediate and systemic safety issues, and provide corrective and preventive actions that align with industry best practices. ' +
    'When generating suggested actions, you will think like a safety officer conducting a root cause analysis. Describe countermeasures as clearly, specifically, and thoroughly as possible — including both short-term fixes and long-term systemic improvements. ' +
    'Your task is to precisely identify key CAPA details while strictly adhering to the provided categorization systems. ' +
    'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
    'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing. ' +
    'Use the exact allowed values from the schema for: "type", "rootCause", "status", "priority", "rcaMethod". ' +
    'Suggested mapping: ' +
    '- **High**: critical issues, injury risk, urgent language, only mark priority: high if there was actual harm, a safety shutdown, or critical system failure. ' +
    '- **Medium**: recurring problems or moderate safety impact, only mark priority: medium if there was a safety shutdown or critical system failure. ' +
    '- **Low**: audit findings, future tasks, documentation-only needs use low for near misses or other non-harmful incidents. ' +
    'For date fields, interpret relative time references (like "next week", "tomorrow", "in two days") correctly relative to the current date. ' +
    `Today's date is ${formattedCurrentDate} for reference. When you see "next week", it refers to a date like ${nextWeekDate}, not a date in the past. ` +
    'You need always to return a due date in the future. If the due date is not provided in the transcript, you need to calculate it based on the root cause and severity.' +
    'When returning the field rcaFindings, the format should depend on the selected rcaMethod: ' +
    '- For "5_whys": return a sequence of five "Why?" questions and answers that lead logically to the root cause. Those questions should be numbered and the answers should be in the same order as the questions. after a question and answer break the line.' +
    '- For "fishbone": summarize the findings in paragraph form, citing categories like People, Process, Equipment, Environment. Mention how each contributed. ' +
    '- For "fault_tree": describe a logic chain that led to the top event, referencing contributing events and conditions. ' +
    '- For "other" or "not_selected": provide a short paragraph summarizing the root cause in plain language. ' +
    'The aiSuggestedAction field should include a **numbered list** of clear corrective and preventive steps. ' +
    'If a method like fishbone is used, and a monitoring plan is mentioned (e.g., "track incident rates for 3 months"), include that as the final step. ' +
    'The response should always be in english, despite the transcript being in another language.';

  // Function to generate the user prompt with the transcript
  const generateUserPrompt = (
    transcript: string,
    timezone?: string,
  ) => `Extract comprehensive details from this CAPA transcript. Return ONLY a JSON object with these precise keys:

    title (a clear, concise title summarizing the CAPA)
    type (must be one of: ${capaTypeEnum.enumValues.join(', ')})
    rcaMethod (must be one of: ${rcaMethodEnum.enumValues.join(', ')})
    rcaFindings (based on rcaMethod: for "5_whys" provide five "Why?" questions and answers that lead to the root cause; for fault_tree, return rcaFindings as a paragraph describing the top event and the contributing failure chains. Start the paragraph with: "Fault Tree Analysis of '...' identified...".
    rootCause (if determined: ${RootCauseSchema.options.join(', ')})
    otherRootCause (if rootCause is not selected, provide a short paragraph summarizing the root cause in plain language)
    dueDate (when CAPA should be completed, in ISO format if possible) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone
    tags (select tags that apply from ${capaTagsEnum.enumValues.join(', ')})
    priority (level of priority: ${capaPriorityEnum.enumValues.join(', ')})
    actionsToAddress (AI-generated action suggestions. Provide the response as a numbered list of clear, concise action steps)
    aiConfidenceScore (AI analysis confidence level) should be a number between 0 and 1 as percentage where 0.85 is 85%
    

    IMPORTANT DATE HANDLING INSTRUCTIONS:
    - Today's date is ${formattedCurrentDate}
    - If the transcript mentions "next week", use a date around ${nextWeekDate}
    - If the transcript mentions "next month", use a date around ${nextMonthDate}
    - If the transcript mentions "tomorrow", use ${tomorrow}
    - All due dates should be in the future, not the past
    - For relative time references, calculate the proper future date based on today
    - Format all dates as ISO strings (YYYY-MM-DDTHH:MM:SS.SSSZ)
    
    Only include fields where information is explicitly provided in the transcript.
    Use exact type, rootCause, severity, status, and priority values as specified.
    
    Transcript: "${transcript}"`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};
