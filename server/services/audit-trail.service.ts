import { auditTrail } from '@shared/schema';
import { db } from '@server/db';
import { and, eq, asc } from 'drizzle-orm';
import { User } from '@shared/schema.types';

export const getAuditTrail = async (entityId: string, user: User) => {
  return await db
    .select()
    .from(auditTrail)
    .where(and(eq(auditTrail.entityId, entityId), eq(auditTrail.upkeepCompanyId, user.roleId)))
    .orderBy(asc(auditTrail.timestamp));
};
