import { buildDefaultHeaders, type Headers } from '@server/utils/requestUtils';
import { Asset, GetAssetsCountParams, GetAssetsParams, PublicSearchInput } from '@shared/schema.types';
import axios from 'axios';
import { env } from 'env';

// Map upstream response to our Asset type
const mapAssetResponse = (upstreamAsset: { id: string; Name: string; Description: string }): Asset => ({
  id: upstreamAsset.id,
  name: upstreamAsset.Name,
  description: upstreamAsset.Description,
});

// Map public API response to our Asset type
const mapPublicAssetResponse = (upstreamAsset: {
  id: string;
  Name: string;
  objectLocation?: { stringName: string };
}): Asset => ({
  id: upstreamAsset.id,
  name: upstreamAsset.Name,
  description: upstreamAsset.objectLocation?.stringName || '', // Use location as description if available
});

export const getAssets = async (params: GetAssetsParams, headers: Headers): Promise<Asset[]> => {
  const requestBody = {
    limit: params.limit,
    offset: params.offset,
    sort: params.sort || 'createdAt DESC',
    ...(params.search && { search: params.search }),
    ...(params.objectLocation && { objectLocation: params.objectLocation }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/assets/search`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
    params: {
      ...(params.id && { id: params.id }),
    },
  });

  // Map the response to only return needed properties
  return response.data.results.map(mapAssetResponse);
};

export const getAssetById = async (id: string, headers: Headers) => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/assets/${id}`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
  });

  // Map the response to only return needed properties
  return mapAssetResponse(response.data.result);
};

export const getAssetsCount = async (params: GetAssetsCountParams, headers: Headers): Promise<number> => {
  const requestBody = {
    ...(params.search && { search: params.search }),
    ...(params.objectLocation && { objectLocation: params.objectLocation }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/assets/search/count`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });
  return response.data.result as number;
};

/**
 * Public asset search - calls the UpKeep API without authentication requirements
 * Uses the search endpoint that requires only roleId
 */
export const searchAssetsPublic = async (params: PublicSearchInput): Promise<Asset[]> => {
  const requestBody = {
    roleId: params.roleId,
    ...(params.search && { search: params.search }),
    ...(params.limit && { limit: params.limit }),
    ...(params.locationId && { objectLocation: params.locationId }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/app/search/assets`, requestBody, {
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  // Check if response has expected structure
  if (!response.data.success || !response.data.results) {
    return [];
  }

  // Map the response to be consistent with our Asset type
  return response.data.results.map(mapPublicAssetResponse);
};
