import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, auditTrailActionEnum, files, incidents } from '@shared/schema';
import {
  CreateIncidentFormPublicSchema,
  CreateIncidentFormSchema,
  Incident,
  ListIncidentSchema,
  PaginatedResponse,
  TransientFileSchema,
  UpdateIncidentSchema,
  User,
  UserPublic,
} from '@shared/schema.types';
import { and, asc, desc, eq, getTableColumns, ilike, inArray, isNull, or, sql, SQL } from 'drizzle-orm';
import { z } from 'zod';

export const createIncident = async (incident: z.infer<typeof CreateIncidentFormSchema>, user: User) => {
  const inserted = await db
    .insert(incidents)
    .values({
      ...incident,
      reportedBy: user.id,
      upkeepCompanyId: user.roleId,
      status: 'open',
      reportedByName: user.fullName,
      reportedByEmail: user.email,
    })
    .returning({
      id: incidents.id,
      upkeepCompanyId: incidents.upkeepCompanyId,
      slug: incidents.slug,
      title: incidents.title,
      description: incidents.description,
      status: incidents.status,
      severity: incidents.severity,
      reportedAt: incidents.reportedAt,
      reportedBy: incidents.reportedBy,
      locationId: incidents.locationId,
      assetIds: incidents.assetIds,
      reportedByName: incidents.reportedByName,
      reportedByEmail: incidents.reportedByEmail,
    });

  await db.insert(auditTrail).values({
    entityId: inserted[0].id,
    entityType: 'incident',
    details: JSON.stringify(inserted),
    userId: inserted[0].reportedBy,
    upkeepCompanyId: inserted[0].upkeepCompanyId,
    timestamp: new Date(),
    action: auditTrailActionEnum.enumValues[0],
  });

  return inserted.at(0);
};

export const createIncidentPublic = async (
  incident: z.infer<typeof CreateIncidentFormPublicSchema>,
  reporter: { email: string; name: string },
  user?: UserPublic,
) => {
  const inserted = await db
    .insert(incidents)
    .values({
      ...incident,
      reportedBy: user?.id,
      upkeepCompanyId: incident.roleId,
      status: 'open',
      reportedByName: user?.name ?? reporter.name,
      reportedByEmail: user?.username ?? reporter.email,
    })
    .returning({
      id: incidents.id,
      upkeepCompanyId: incidents.upkeepCompanyId,
      slug: incidents.slug,
      title: incidents.title,
      description: incidents.description,
      status: incidents.status,
      severity: incidents.severity,
      reportedAt: incidents.reportedAt,
      reportedBy: incidents.reportedBy,
      locationId: incidents.locationId,
      assetIds: incidents.assetIds,
      reportedByName: incidents.reportedByName,
      reportedByEmail: incidents.reportedByEmail,
    });

  await db.insert(auditTrail).values({
    entityId: inserted[0].id,
    entityType: 'incident',
    details: JSON.stringify({ inserted, user, public: true }),
    userId: inserted[0].reportedBy,
    upkeepCompanyId: inserted[0].upkeepCompanyId,
    timestamp: new Date(),
    action: auditTrailActionEnum.enumValues[0],
  });

  return inserted.at(0);
};

export const getIncidentById = async (id: string, user: User, needPartialCheck: boolean) => {
  // Fetch the incident that matches the ID and company ID, and is not deleted
  const result = await db
    .select({
      ...getTableColumns(incidents),
      media: sql<z.infer<typeof TransientFileSchema>[]>`
      CASE
        WHEN COUNT(${files.id}) = 0 THEN NULL
        ELSE json_agg(
          json_build_object(
            'id', ${files.id},
            'name', ${files.fileName},
            'url', ${files.presignedUrl},
            'type', ${files.mimeType},
            'size', ${files.fileSize}
          )
        )
      END
    `,
    })
    .from(incidents)
    .leftJoin(
      files,
      and(eq(files.entityId, incidents.id), eq(files.status, 'completed'), eq(files.entityType, 'incident')),
    )
    .where(
      and(
        eq(incidents.id, id),
        eq(incidents.upkeepCompanyId, user.roleId),
        isNull(incidents.deletedAt),
        ...(needPartialCheck ? [eq(incidents.reportedBy, user.id)] : []),
      ),
    )
    .groupBy(incidents.id);

  return result.at(0);
};

export const listIncidents = async (
  input: z.infer<typeof ListIncidentSchema>,
  user: User,
  needPartialCheck: boolean,
): Promise<PaginatedResponse<Incident>> => {
  const {
    page = 1,
    limit = 10,
    archived,
    search,
    sortBy = 'reportedAt',
    sortOrder = 'desc',
    oshaReportable = false,
    severity = [],
    status = [],
    type = [],
    locationIds = [],
  } = input;

  const conditions = [eq(incidents.upkeepCompanyId, user.roleId)];

  if (needPartialCheck) {
    conditions.push(eq(incidents.reportedBy, user.id));
  }

  if (!archived) {
    conditions.push(eq(incidents.archived, false));
  }

  if (search) {
    const searchFilter = or(
      ilike(incidents.title, `%${search}%`),
      ilike(incidents.description, `%${search}%`),
      ilike(incidents.slug, `%${search}%`),
    );
    conditions.push(searchFilter as SQL<unknown>);
  }

  if (oshaReportable) {
    conditions.push(eq(incidents.oshaReportable, oshaReportable));
  }

  if (severity.length > 0) {
    conditions.push(inArray(incidents.severity, severity));
  }

  if (status.length > 0) {
    conditions.push(inArray(incidents.status, status));
  }

  if (type.length > 0) {
    conditions.push(inArray(incidents.type, type));
  }

  if (locationIds.length > 0) {
    conditions.push(inArray(incidents.locationId, locationIds));
  }

  let query = db
    .select()
    .from(incidents)
    .where(and(...conditions))
    .$dynamic();

  if (sortBy) {
    query = query.orderBy(sortOrder === 'desc' ? desc(incidents.reportedAt) : asc(incidents.reportedAt));
  }

  const filtered = await query.limit(limit).offset((page - 1) * limit);

  return createPaginatedResponse(filtered, {
    total: filtered.length,
    page,
    limit,
  });
};

export const updateIncident = async (
  id: string,
  incident: z.infer<typeof UpdateIncidentSchema>,
  user: User,
  needPartialCheck: boolean,
) => {
  const updated = await db
    .update({ ...incidents, updatedAt: new Date() })
    .set(incident)
    .where(
      and(
        eq(incidents.id, id),
        eq(incidents.upkeepCompanyId, user.roleId),
        ...(needPartialCheck ? [eq(incidents.reportedBy, user.id)] : []),
      ),
    )
    .returning({
      id: incidents.id,
      status: incidents.status,
      archived: incidents.archived,
      reportedBy: incidents.reportedBy,
      upkeepCompanyId: incidents.upkeepCompanyId,
      slug: incidents.slug,
      title: incidents.title,
      description: incidents.description,
      severity: incidents.severity,
      reportedAt: incidents.reportedAt,
      locationId: incidents.locationId,
      assetIds: incidents.assetIds,
      reportedByName: incidents.reportedByName,
      reportedByEmail: incidents.reportedByEmail,
    });

  const updatedIncident = updated.at(0);

  if (!updatedIncident) {
    throw new Error('Incident not found');
  }

  let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

  if ('status' in incident && 'status' in updatedIncident) {
    const statusMap = {
      open: auditTrailActionEnum.enumValues[7],
      in_review: auditTrailActionEnum.enumValues[6],
      closed: auditTrailActionEnum.enumValues[5],
    };

    action = statusMap[
      updatedIncident.status as keyof typeof statusMap
    ] as (typeof auditTrailActionEnum.enumValues)[number];
  }

  if ('archived' in incident && 'archived' in updatedIncident) {
    action = updatedIncident.archived ? 'archived' : 'unarchived';
  }

  if ('severity' in incident && 'severity' in updatedIncident) {
    action = updatedIncident.severity ? 'updated' : 'updated';
  }

  await db.insert(auditTrail).values({
    entityId: updatedIncident.id,
    entityType: 'incident',
    details: JSON.stringify(updatedIncident),
    userId: updatedIncident.reportedBy,
    upkeepCompanyId: updatedIncident.upkeepCompanyId,
    timestamp: new Date(),
    action,
  });

  return {
    ...updatedIncident,
    action,
  };
};
