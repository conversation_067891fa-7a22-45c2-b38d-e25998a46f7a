import { buildDefaultHeaders, type Headers } from '@server/utils/requestUtils';
import { Location, PublicSearchInput } from '@shared/schema.types';
import axios from 'axios';
import { env } from 'env';

type UpstreamLocation = {
  id: string;
  stringName: string;
  arrayOfSublocations?: Array<{
    id: string;
    stringName: string;
    objectId: string;
  }>;
};

type GetLocationsParams = {
  id?: string[] | string;
  search?: string;
  limit: number;
  offset: number;
  sort?: string;
};

type GetLocationsCountParams = {
  search?: string;
};

// Map upstream response to our Location type
const mapLocationResponse = (upstreamLocation: { id: string; stringName: string }): Location => ({
  id: upstreamLocation.id,
  name: upstreamLocation.stringName,
});

// Map public API response to our Location type
// Based on the API response structure with main locations and sublocations
const mapPublicLocationResponse = (upstreamLocation: UpstreamLocation): Location[] => {
  const locations: Location[] = [];

  // Add the main location
  locations.push({
    id: upstreamLocation.id,
    name: upstreamLocation.stringName,
  });

  // Add sublocations if they exist
  if (upstreamLocation.arrayOfSublocations) {
    upstreamLocation.arrayOfSublocations.forEach((sublocation) => {
      locations.push({
        id: sublocation.id,
        name: sublocation.stringName,
      });
    });
  }

  return locations;
};

export const getLocations = async (params: GetLocationsParams, headers: Headers): Promise<Location[]> => {
  const requestBody = {
    limit: params.limit,
    offset: params.offset,
    sort: params.sort || 'createdAt DESC',
    ...(params.search && { search: params.search }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/locations/search`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
    params: {
      ...(params.id && { id: params.id }),
    },
  });

  // Map the response to only return needed properties
  return response.data.results.map(mapLocationResponse);
};

export const getLocationsCount = async (params: GetLocationsCountParams, headers: Headers): Promise<number> => {
  const requestBody = {
    ...(params.search && { search: params.search }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/locations/search/count`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });
  return response.data.result as number;
};

export const getLocationById = async (id: string, headers: Headers) => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/locations/${id}`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
  });

  // Map the response to only return needed properties
  return mapLocationResponse(response.data.result);
};

/**
 * Public location search - calls the UpKeep API without authentication requirements
 * Uses the search endpoint that requires only roleId
 */
export const searchLocationsPublic = async (params: PublicSearchInput): Promise<Location[]> => {
  const requestBody = {
    roleId: params.roleId,
    ...(params.search && { search: params.search }),
    ...(params.limit && { limit: params.limit }),
    ...(params.objectId && { objectId: params.objectId }),
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/app/search/locations`, requestBody, {
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  // Check if response has expected structure
  if (!response.data.success || !response.data.results) {
    return [];
  }

  // Map the response to be consistent with our Location type
  // Flatten the main locations and sublocations into a single array
  const allLocations: Location[] = [];

  response.data.results.forEach((location: UpstreamLocation) => {
    const mappedLocations = mapPublicLocationResponse(location);
    allLocations.push(...mappedLocations);
  });

  return allLocations;
};
