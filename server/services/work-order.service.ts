import { env } from 'env';
import axios from 'axios';
import { buildDefaultHeaders, type Headers } from '@server/utils/requestUtils';
import { TRPCError } from '@trpc/server';
import type {
  CreateWorkOrderFromCapaInput,
  CountWorkOrdersByCapaIdInput,
  WorkOrder,
  RawWorkOrderFromApi,
  CreateWorkOrderParams,
} from '@shared/schema.types';
import { formatNoteWithBreakLines } from '@server/utils/formating';
import { convertNumberToPriority, convertPriorityToNumber } from '@server/utils/workOrderPriority';

export const createWorkOrder = async (params: CreateWorkOrderParams, headers: Headers): Promise<boolean> => {
  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/work-orders/create/wrapper`, params, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  return response.data.success;
};

export const searchWorkOrdersByCapaId = async (
  params: { capaId: string[]; limit: number; offset: number; sort?: string },
  headers: Headers,
): Promise<WorkOrder[]> => {
  const requestBody = {
    limit: params.limit,
    offset: params.offset,
    sort: params.sort || 'createdAt DESC',
    capaId: params.capaId,
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/work-orders/search`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  if (!response.data?.success || !response.data?.results) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to search work orders',
    });
  }

  const apiData = response.data.results;

  // Transform the response to match our simplified WorkOrder type
  return apiData.map((workOrder: RawWorkOrderFromApi) => ({
    id: workOrder.id,
    workOrderNumber: workOrder.workOrderNumber,
    title: workOrder.mainDescription,
    currentStatus: workOrder.currentStatus,
    priority: convertNumberToPriority(workOrder.priorityNumber),
    dueDate: workOrder.dueDate,
    assignedTo: workOrder?.userAssignedTo?.objectId,
    locationId: workOrder?.objectLocationForWorkOrder?.objectId,
    assetId: workOrder?.objectAsset?.id,
    assetName: workOrder?.objectAsset?.Name,
  }));
};

export const getWorkOrdersCountByCapa = async (
  params: CountWorkOrdersByCapaIdInput,
  headers: Headers,
): Promise<number> => {
  const requestBody = {
    capaId: params.capaId,
  };

  const response = await axios.post(`${env.UPKEEP_API_URL}/api/v1/work-orders/search/count`, requestBody, {
    headers: {
      ...buildDefaultHeaders(headers),
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  if (!response.data?.success) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to count work orders',
    });
  }

  return response.data.result as number;
};

export const createWorkOrderFromCapa = async (
  capaData: CreateWorkOrderFromCapaInput,
  headers: Headers,
): Promise<boolean> => {
  // Validate required fields
  if (
    !capaData.id ||
    !capaData.title ||
    !capaData.slug ||
    !capaData.actionsToAddress ||
    !capaData.priority ||
    !capaData.dueDate
  ) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Missing required fields: id, title, slug, actionsToAddress, priority, and dueDate are mandatory',
    });
  }

  // Map CAPA fields to work order parameters
  const mainDescription = capaData.title;

  // Format the note with the CAPA link and actions
  const capaUrl = `${env.EHS_URL}/capas/${capaData.id}`;
  const formattedActions = formatNoteWithBreakLines(capaData.actionsToAddress);

  const note = `<div style='white-space:normal'>This Work Order was created from <a href="${capaUrl}" target="_blank">${capaData.slug}</a>. <br/> <br/> ${formattedActions}</div>`;

  const priorityNumber = convertPriorityToNumber(capaData.priority);

  const workOrderParams: CreateWorkOrderParams = {
    mainDescription,
    note,
    priorityNumber,
    dueDate: capaData.dueDate,
    objectLocationForWorkOrder: capaData.locationId ?? undefined,
    objectAsset: capaData.assetId ?? undefined,
    userAssignedTo: capaData.userAssignedTo,
    capaId: capaData.id,
  };

  // Create the work order
  return await createWorkOrder(workOrderParams, headers);
};
