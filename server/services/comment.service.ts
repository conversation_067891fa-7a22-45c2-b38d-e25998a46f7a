import { User } from '@shared/schema.types';
import { auditTrail, auditTrailActionEnum, commentMentions, comments } from '@shared/schema';
import { CreateCommentFormSchema, IdSchema, ListCommentsSchema } from '@shared/schema.types';
import { and, asc, eq, isNull } from 'drizzle-orm';
import { z } from 'zod';
import { db } from '../db'; // Fixed import path

export const createComment = async (
  { entityId, entityType, content }: z.infer<typeof CreateCommentFormSchema>,
  user: User,
) => {
  // Create the comment first
  const [newComment] = await db
    .insert(comments)
    .values({
      ...(entityType === 'incident' ? { incidentId: entityId } : { capaId: entityId }),
      content,
      userId: user.id,
      upkeepCompanyId: user.roleId,
    })
    .returning();

  // Extract mentions from content
  const mentions = content.match(/@[a-zA-Z0-9]+/g);
  if (mentions) {
    const mentionPromises = mentions.map(async (mention) => {
      const mentionedUserId = mention.slice(1); // Remove @ symbol
      const position = content.indexOf(mention);

      return db.insert(commentMentions).values({
        commentId: newComment.id,
        userId: mentionedUserId,
        position,
        mentionText: mention,
      });
    });

    // Insert all mentions
    await Promise.all(mentionPromises);
  }

  await db.insert(auditTrail).values({
    entityId,
    entityType,
    details: JSON.stringify(newComment),
    userId: newComment.userId,
    upkeepCompanyId: newComment.upkeepCompanyId,
    action: auditTrailActionEnum.enumValues[3],
  });

  return newComment;
};

export const fetchComments = async (
  { entityId, entityType, options }: z.infer<typeof ListCommentsSchema>,
  user: User,
) => {
  const { limit = 50, offset = 0 } = options || {};

  // Build the where condition based on entity type
  const whereCondition =
    entityType === 'incident'
      ? and(eq(comments.incidentId, entityId), isNull(comments.capaId))
      : and(eq(comments.capaId, entityId), isNull(comments.incidentId));

  // Fetch comments with their mentions
  const commentsWithMentions = await db
    .select({
      id: comments.id,
      content: comments.content,
      createdAt: comments.createdAt,
      updatedAt: comments.updatedAt,
      userId: comments.userId,
      upkeepCompanyId: comments.upkeepCompanyId,
      incidentId: comments.incidentId,
      capaId: comments.capaId,
      mentions: {
        id: commentMentions.id,
        userId: commentMentions.userId,
        position: commentMentions.position,
        mentionText: commentMentions.mentionText,
        createdAt: commentMentions.createdAt,
      },
    })
    .from(comments)
    .leftJoin(commentMentions, eq(comments.id, commentMentions.commentId))
    .where(and(whereCondition, eq(comments.upkeepCompanyId, user.roleId)))
    .orderBy(asc(comments.createdAt))
    .limit(limit)
    .offset(offset);

  type GroupedComment = {
    id: string;
    content: string;
    createdAt: Date;
    updatedAt: Date | null;
    userId: string;
    upkeepCompanyId: string;
    incidentId: string | null;
    capaId: string | null;
    mentions: Array<{
      id: string;
      userId: string;
      position: number | null;
      mentionText: string | null;
      createdAt: Date | null;
    }>;
  };

  // Group mentions by comment
  const groupedComments = commentsWithMentions.reduce(
    (acc, row) => {
      const commentId = row.id;

      if (!acc[commentId]) {
        acc[commentId] = {
          id: row.id,
          content: row.content,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          userId: row.userId,
          upkeepCompanyId: row.upkeepCompanyId,
          incidentId: row.incidentId,
          capaId: row.capaId,
          mentions: [],
        };
      }

      // Add mention if it exists
      if (row.mentions?.id) {
        acc[commentId].mentions.push(row.mentions);
      }

      return acc;
    },
    {} as Record<string, GroupedComment>,
  );

  return Object.values(groupedComments);
};

export const fetchCommentById = async ({ id }: z.infer<typeof IdSchema>, user: User) => {
  const result = await db
    .select({
      id: comments.id,
      content: comments.content,
      createdAt: comments.createdAt,
      updatedAt: comments.updatedAt,
      userId: comments.userId,
      upkeepCompanyId: comments.upkeepCompanyId,
      incidentId: comments.incidentId,
      capaId: comments.capaId,
      mentions: {
        id: commentMentions.id,
        userId: commentMentions.userId,
        position: commentMentions.position,
        mentionText: commentMentions.mentionText,
        createdAt: commentMentions.createdAt,
      },
    })
    .from(comments)
    .leftJoin(commentMentions, eq(comments.id, commentMentions.commentId))
    .where(and(eq(comments.id, id), eq(comments.upkeepCompanyId, user.roleId)));

  if (result.length === 0) {
    return null;
  }

  // Group mentions for the single comment
  const comment = {
    id: result[0].id,
    content: result[0].content,
    createdAt: result[0].createdAt,
    updatedAt: result[0].updatedAt,
    userId: result[0].userId,
    upkeepCompanyId: result[0].upkeepCompanyId,
    incidentId: result[0].incidentId,
    capaId: result[0].capaId,
    mentions: result.filter((row) => row.mentions?.id).map((row) => row.mentions!),
  };

  return comment;
};

export const deleteComment = async ({ id }: z.infer<typeof IdSchema>, user: User) => {
  // First, delete all mentions for this comment
  await db.delete(commentMentions).where(eq(commentMentions.commentId, id));

  // Then delete the comment itself, but only if the user owns it or has appropriate permissions
  const result = await db
    .delete(comments)
    .where(
      and(
        eq(comments.id, id),
        eq(comments.upkeepCompanyId, user.roleId),
        // Optionally, only allow the comment author to delete their own comments
        // eq(comments.userId, user.id)
      ),
    )
    .returning();

  return result.length > 0 ? { success: true, id } : { success: false, id };
};
