import { DeleteObjectsCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createId } from '@paralleldrive/cuid2';
import { files } from '@shared/schema';
import {
  CreateFileSchema,
  GetPresignedUrlInputSchema,
  IdArraySchema,
  ListFilesSchema,
  UpdateFileSchema,
  User,
} from '@shared/schema.types';
import { and, eq, inArray } from 'drizzle-orm';
import { db } from 'server/db';
import { z } from 'zod';
import { env } from '../../env';

const s3Client = new S3Client({
  region: env.AWS_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export const getPresignedUrl = async (
  params: z.infer<typeof GetPresignedUrlInputSchema>,
  user: Pick<User, 'roleId'> & { id?: User['id'] },
) => {
  const fileExtension = params.fileName.split('.').pop();

  const uniqueId = createId();
  const key = `ehs-files/${user.roleId}/${params.entityType}/${params.entityId}/${uniqueId}.${fileExtension}`;

  const command = new PutObjectCommand({
    Bucket: env.S3_BUCKET_NAME,
    Key: key,
    ContentType: params.mimeType,
  });

  const presignedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: 3600, // URL expires in 1 hour
    // Disable checksums to avoid signature issues
    signableHeaders: new Set(['host']),
  });

  // Create file record
  const file = await createFile({
    fileName: params.fileName,
    fileSize: params.fileSize,
    mimeType: params.mimeType,
    presignedUrl,
    s3Key: key,
    s3Bucket: env.S3_BUCKET_NAME,
    expiresAt: new Date(Date.now() + 3600 * 1000), // 1 hour from now
    uploadedBy: user?.id,
    upkeepCompanyId: user?.roleId,
    entityId: params.entityId,
    entityType: params.entityType,
  });

  return {
    presignedUrl,
    file,
  };
};

export const createFile = async (params: z.infer<typeof CreateFileSchema>) => {
  const file = await db
    .insert(files)
    .values({
      ...params,
      status: 'pending',
    })
    .returning();

  return file.at(0);
};

export const getFiles = async (params: z.infer<typeof ListFilesSchema>, user: User) => {
  const conditions = [eq(files.upkeepCompanyId, user.roleId)];

  if (params.entityType) {
    conditions.push(eq(files.entityType, params.entityType));
  }

  if (params.entityId) {
    conditions.push(eq(files.entityId, params.entityId));
  }

  if (params.status) {
    conditions.push(eq(files.status, params.status));
  }

  return await db
    .select()
    .from(files)
    .where(and(...conditions))
    .orderBy(files.createdAt);
};

export const updateFile = async (id: string, params: z.infer<typeof UpdateFileSchema>, user: Pick<User, 'roleId'>) => {
  const presignedUrl = await getPresignedReadUrl(params.s3Key!);

  const file = await db
    .update(files)
    .set({
      ...params,
      presignedUrl,
      updatedAt: new Date(),
    })
    .where(and(eq(files.id, id), eq(files.upkeepCompanyId, user.roleId)))
    .returning();

  return file.at(0);
};

export const getPresignedReadUrl = async (s3Key: string) => {
  const command = new GetObjectCommand({
    Bucket: env.S3_BUCKET_NAME,
    Key: s3Key,
  });

  const presignedUrl = await getSignedUrl(s3Client, command);

  return presignedUrl;
};

export const removeFiles = async (ids: z.infer<typeof IdArraySchema>) => {
  const deletedFiles = await db.delete(files).where(inArray(files.id, ids)).returning();

  const command = new DeleteObjectsCommand({
    Bucket: env.S3_BUCKET_NAME,
    Delete: {
      Objects: deletedFiles?.map((file) => ({ Key: file.s3Key })),
    },
  });

  await s3Client.send(command);

  return deletedFiles;
};
