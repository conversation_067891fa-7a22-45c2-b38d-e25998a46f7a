import { logger } from '@server/utils/logger';
import { buildDefaultHeaders, type Headers } from '@server/utils/requestUtils';
import axios from 'axios';
import { env } from 'env';
import { User, UserPublic, FeatureFlag, UserSchema, featureFlags } from '@shared/schema.types';
import {
  USER_ACCOUNT_TYPE_MAP,
  UserPermission,
  AllowedActions,
  permissions,
  USER_ACCOUNTS,
} from '@shared/user-permissions';

export const getMe = async ({ headers }: { headers: Headers }): Promise<User> => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/users/me`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
  });

  const user = response.data.result;

  user.featureFlags = featureFlags.reduce(
    (acc, flag) => {
      acc[flag] = user.featureFlags[flag] ?? false;
      return acc;
    },
    {} as Record<FeatureFlag, boolean>,
  );

  const result = UserSchema.safeParse({
    ...user,
    groupId: `${user.groupId}`,
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
    permissions: getUserPermissions(user.userAccountType),
    userAccountType: USER_ACCOUNT_TYPE_MAP[user.userAccountType as keyof typeof USER_ACCOUNT_TYPE_MAP],
  });

  if (!result.success) {
    logger.error('Error parsing user', result.error);
    throw new Error('Error parsing user');
  }

  return result.data;
};

export const getUserPermissions = (userAccountType: string): UserPermission[] => {
  // Return admin permissions if admin, otherwise tech permissions
  return userAccountType === USER_ACCOUNTS.ADMIN
    ? permissions[USER_ACCOUNTS.ADMIN]
    : permissions[USER_ACCOUNTS.TECHNICIAN];
};

export const getUsers = async ({
  headers,
  search,
  objectId,
}: {
  headers: Headers;
  search?: string;
  objectId?: string[];
}): Promise<User[]> => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/users`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
    params: {
      search: search || '',
      objectId: objectId || [],
    },
  });

  const users = response.data.results as User[];

  if (!users) {
    return [];
  }

  return users.map((user) => ({
    ...user,
    userAccountType: USER_ACCOUNT_TYPE_MAP[user.userAccountType as keyof typeof USER_ACCOUNT_TYPE_MAP],
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
  }));
};

type GetUserPublicParams = {
  roleId: string;
  search?: string;
  objectId?: string[];
  headers?: Headers;
  userAccountType?: string | { [key: string]: string[] };
};

export const getUserPublic = async (params: GetUserPublicParams): Promise<UserPublic[]> => {
  try {
    const { roleId, search, objectId, headers, userAccountType } = params;
    const requestBody: {
      roleId: string;
      search?: string;
      objectId?: string[];
      userAccountType?: string | { [key: string]: string[] };
    } = {
      roleId: roleId,
      userAccountType: userAccountType
        ? userAccountType
        : {
            notContainedIn: ['6'],
          },
    };

    if (search) {
      requestBody.search = search;
    }

    if (objectId && objectId.length > 0) {
      requestBody.objectId = objectId;
    }

    if (userAccountType) {
      requestBody.userAccountType = userAccountType;
    }

    const response = await axios.post(`${env.UPKEEP_API_URL}/api/app/search/users`, requestBody, {
      headers: {
        ...(headers ? buildDefaultHeaders(headers) : {}),
        'Content-Type': 'application/json',
      },
      ...(headers && { withCredentials: true }),
    });

    const users = response.data.results.map((user: UserPublic) => ({
      ...user,
      name: `${user.firstName} ${user.lastName}`,
      email: user.username,
    })) as UserPublic[];

    return users;
  } catch (error) {
    logger.error('Error searching users', error);
    return [];
  }
};

export const getUserById = async (id: string, headers: Headers): Promise<User> => {
  const response = await axios.get(`${env.UPKEEP_API_URL}/api/v1/users/${id}`, {
    headers: buildDefaultHeaders(headers),
    withCredentials: true,
  });

  const user = response.data.result as User;
  return {
    ...user,
    userAccountType: USER_ACCOUNT_TYPE_MAP[user.userAccountType as keyof typeof USER_ACCOUNT_TYPE_MAP],
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
  };
};

export const hasPermission = (
  user: User,
  dataObject: UserPermission['dataObject'],
  action: UserPermission['action'],
  requireFullAccess = false,
): boolean => {
  const permission = user.permissions.find((p) => p.dataObject === dataObject && p.action === action);

  if (!permission) {
    return false;
  }

  if (requireFullAccess) {
    return permission.permissionLevel === 'full';
  }

  // Either full or partial access is acceptable
  return permission.permissionLevel === 'full' || permission.permissionLevel === 'partial';
};

export const userHasIncidentPermission = (user: User, action: AllowedActions, requireFullAccess = false): boolean => {
  return hasPermission(user, 'ehs-incident', action, requireFullAccess);
};

export const canEditIncident = (user: User, incidentOwnerId: string, incidentStatus: string): boolean => {
  const isAdmin = user.userAccountType === USER_ACCOUNTS.ADMIN;

  // Admins can edit any incident
  if (isAdmin) {
    return userHasIncidentPermission(user, 'edit', true); // Require full access
  }

  // For technicians, they need partial edit permission
  if (!userHasIncidentPermission(user, 'edit')) {
    return false; // No edit permission at all
  }

  // If technician has partial permission, they can only edit:
  // 1. Their own incidents
  // 2. Only when status is open
  return incidentOwnerId === user.id && incidentStatus === 'open';
};

export const canExportIncident = (user: User, incidentOwnerId: string): boolean => {
  const isAdmin = user.userAccountType === USER_ACCOUNTS.ADMIN;

  // Admins can export any incident
  if (isAdmin) {
    return userHasIncidentPermission(user, 'export', true); // Require full access
  }

  // For technicians, they need partial export permission
  if (!userHasIncidentPermission(user, 'export')) {
    return false; // No export permission at all
  }

  // If technician has partial permission, they can only export their own incidents
  return incidentOwnerId === user.id;
};
