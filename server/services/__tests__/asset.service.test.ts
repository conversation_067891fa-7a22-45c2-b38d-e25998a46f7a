import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Asset, GetAssetsParams, GetAssetsCountParams, PublicSearchInput } from '../../../shared/schema.types';

// Simple mock for axios that avoids complex typing issues
const mockAxios = {
  post: vi.fn(),
  get: vi.fn(),
};

// Mock axios module
vi.mock('axios', () => ({
  default: mockAxios,
}));

// Mock request utils to return headers as-is
vi.mock('../../utils/requestUtils', () => ({
  buildDefaultHeaders: vi.fn((headers) => headers),
}));

describe('Asset Service', () => {
  let assetService: typeof import('../asset.service');

  const mockHeaders = {
    authorization: 'Bearer test-token',
    'content-type': 'application/json',
  };

  const mockUpstreamAsset = {
    id: 'asset-1',
    Name: 'Test Pump',
    Description: 'High pressure pump',
  };

  const mockMappedAsset: Asset = {
    id: 'asset-1',
    name: 'Test Pump',
    description: 'High pressure pump',
  };

  beforeEach(async () => {
    vi.clearAllMocks();
    assetService = await import('../asset.service');
  });

  describe('getAssets', () => {
    const baseParams: GetAssetsParams = {
      limit: 10,
      offset: 0,
      sort: 'createdAt DESC',
    };

    it('should fetch assets with basic parameters', async () => {
      const mockResponse = {
        data: {
          results: [mockUpstreamAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.getAssets(baseParams, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search',
        {
          limit: 10,
          offset: 0,
          sort: 'createdAt DESC',
        },
        expect.objectContaining({
          withCredentials: true,
          params: {},
        }),
      );

      expect(result).toEqual([mockMappedAsset]);
    });

    it('should include search parameter when provided', async () => {
      const paramsWithSearch: GetAssetsParams = {
        ...baseParams,
        search: 'pump',
      };

      const mockResponse = {
        data: {
          results: [mockUpstreamAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      await assetService.getAssets(paramsWithSearch, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search',
        expect.objectContaining({
          search: 'pump',
        }),
        expect.any(Object),
      );
    });

    it('should include objectLocation parameter when provided', async () => {
      const paramsWithLocation: GetAssetsParams = {
        ...baseParams,
        objectLocation: ['warehouse-a', 'warehouse-b'],
      };

      const mockResponse = {
        data: {
          results: [mockUpstreamAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      await assetService.getAssets(paramsWithLocation, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search',
        expect.objectContaining({
          objectLocation: ['warehouse-a', 'warehouse-b'],
        }),
        expect.any(Object),
      );
    });

    it('should include both search and objectLocation when provided', async () => {
      const paramsWithBoth: GetAssetsParams = {
        ...baseParams,
        search: 'pump',
        objectLocation: ['warehouse-a'],
      };

      const mockResponse = {
        data: {
          results: [mockUpstreamAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      await assetService.getAssets(paramsWithBoth, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search',
        expect.objectContaining({
          search: 'pump',
          objectLocation: ['warehouse-a'],
        }),
        expect.any(Object),
      );
    });
  });

  describe('getAssetsCount', () => {
    it('should fetch asset count with basic parameters', async () => {
      const mockResponse = {
        data: {
          result: 42,
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.getAssetsCount({}, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search/count',
        {},
        expect.objectContaining({
          withCredentials: true,
        }),
      );

      expect(result).toBe(42);
    });

    it('should include objectLocation parameter when provided', async () => {
      const paramsWithLocation: GetAssetsCountParams = {
        objectLocation: ['warehouse-a'],
      };

      const mockResponse = {
        data: {
          result: 15,
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.getAssetsCount(paramsWithLocation, mockHeaders);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/search/count',
        expect.objectContaining({
          objectLocation: ['warehouse-a'],
        }),
        expect.any(Object),
      );

      expect(result).toBe(15);
    });
  });

  describe('getAssetById', () => {
    it('should fetch single asset by id', async () => {
      const mockResponse = {
        data: {
          result: mockUpstreamAsset,
        },
      };

      mockAxios.get.mockResolvedValue(mockResponse);

      const result = await assetService.getAssetById('asset-1', mockHeaders);

      expect(mockAxios.get).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/v1/assets/asset-1',
        expect.objectContaining({
          withCredentials: true,
        }),
      );

      expect(result).toEqual(mockMappedAsset);
    });
  });

  describe('searchAssetsPublic', () => {
    it('should fetch assets using public search with basic parameters', async () => {
      const params: PublicSearchInput = {
        roleId: 'role-123',
        search: '',
        limit: 1000,
      };

      const mockPublicAsset = {
        id: 'asset-2',
        Name: 'Test Compressor',
        objectLocation: { stringName: 'Warehouse A' },
      };

      const mockResponse = {
        data: {
          success: true,
          results: [mockPublicAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.searchAssetsPublic(params);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/app/search/assets',
        expect.objectContaining({
          roleId: 'role-123',
          limit: 1000,
        }),
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          withCredentials: true,
        }),
      );

      expect(result).toEqual([
        {
          id: 'asset-2',
          name: 'Test Compressor',
          description: 'Warehouse A',
        },
      ]);
    });

    it('should include locationId parameter when provided', async () => {
      const params: PublicSearchInput = {
        roleId: 'role-123',
        search: 'compressor',
        limit: 500,
        locationId: 'warehouse-b',
      };

      const mockPublicAsset = {
        id: 'asset-3',
        Name: 'Warehouse Compressor',
        objectLocation: { stringName: 'Warehouse B' },
      };

      const mockResponse = {
        data: {
          success: true,
          results: [mockPublicAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.searchAssetsPublic(params);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/app/search/assets',
        expect.objectContaining({
          roleId: 'role-123',
          search: 'compressor',
          limit: 500,
          objectLocation: 'warehouse-b',
        }),
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          withCredentials: true,
        }),
      );

      expect(result).toEqual([
        {
          id: 'asset-3',
          name: 'Warehouse Compressor',
          description: 'Warehouse B',
        },
      ]);
    });

    it('should work without locationId parameter (backward compatibility)', async () => {
      const params: PublicSearchInput = {
        roleId: 'role-123',
        search: 'asset',
        limit: 100,
      };

      const mockPublicAsset = {
        id: 'asset-4',
        Name: 'Any Asset',
        objectLocation: { stringName: 'Any Location' },
      };

      const mockResponse = {
        data: {
          success: true,
          results: [mockPublicAsset],
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.searchAssetsPublic(params);

      // Verify objectLocation is not included when locationId is not provided
      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/app/search/assets',
        expect.objectContaining({
          roleId: 'role-123',
          search: 'asset',
          limit: 100,
        }),
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          withCredentials: true,
        }),
      );

      // Verify objectLocation is not in the request body
      expect(mockAxios.post).toHaveBeenCalledWith(
        'https://api.test.upkeep.com/api/app/search/assets',
        expect.not.objectContaining({
          objectLocation: expect.anything(),
        }),
        expect.any(Object),
      );

      expect(result).toEqual([
        {
          id: 'asset-4',
          name: 'Any Asset',
          description: 'Any Location',
        },
      ]);
    });

    it('should return empty array when response is not successful', async () => {
      const params: PublicSearchInput = {
        roleId: 'role-123',
        search: '',
        limit: 1000,
      };

      const mockResponse = {
        data: {
          success: false,
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await assetService.searchAssetsPublic(params);

      expect(result).toEqual([]);
    });
  });
});
