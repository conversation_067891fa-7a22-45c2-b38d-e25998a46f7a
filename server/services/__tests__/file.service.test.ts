import { beforeEach, describe, expect, it, vi } from 'vitest';
import { getPresignedUrl, createFile, getFiles } from '../file.service';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import type { User } from '@shared/schema.types';

// Mock AWS SDK
vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn(),
  PutObjectCommand: vi.fn().mockImplementation((input) => ({ input })),
}));

vi.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: vi.fn().mockResolvedValue('https://test-presigned-url.com'),
}));

// Mock CUID2
vi.mock('@paralleldrive/cuid2', () => ({
  createId: vi.fn(() => 'test-unique-id'),
}));

// Mock environment
vi.mock('../../env', () => ({
  env: {
    AWS_REGION: 'us-east-1',
    AWS_ACCESS_KEY_ID: 'test-key',
    AWS_SECRET_ACCESS_KEY: 'test-secret',
    S3_BUCKET_NAME: 'test-bucket',
  },
}));

// Mock database - using the correct import path
vi.mock('server/db', () => ({
  db: {
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn().mockResolvedValue([{ id: 'test-file-id' }]),
      })),
    })),
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn().mockResolvedValue([{ id: 'test-file-id', status: 'completed' }]),
        })),
      })),
    })),
    select: vi.fn(() => ({
      from: vi.fn(() => ({
        where: vi.fn(() => ({
          orderBy: vi.fn().mockResolvedValue([{ id: 'test-file-1' }, { id: 'test-file-2' }]),
        })),
      })),
    })),
  },
}));

// Mock drizzle-orm
vi.mock('drizzle-orm', () => ({
  eq: vi.fn((field, value) => ({ field, value, operator: 'eq' })),
  and: vi.fn((...conditions) => ({ type: 'and', conditions })),
}));

// Mock shared schema
vi.mock('@shared/schema', () => ({
  files: {
    upkeepCompanyId: 'upkeepCompanyId',
    entityType: 'entityType',
    entityId: 'entityId',
    status: 'status',
    createdAt: 'createdAt',
  },
}));

describe('file.service.ts', () => {
  const mockUser: User = {
    id: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    roleId: 'company-123',
    groupName: 'Test Group',
    groupId: 'group-1',
    userAccountType: '1',
    permissions: [],
    featureFlags: { webEHS: true },
  };

  const mockFileParams = {
    fileName: 'test.jpg',
    fileSize: 1024,
    mimeType: 'image/jpeg',
    entityType: 'incident' as const,
    entityId: 'incident-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getPresignedUrl', () => {
    it('should generate presigned URL and create file record', async () => {
      const result = await getPresignedUrl(mockFileParams, mockUser);

      expect(result).toMatchObject({
        presignedUrl: 'https://test-presigned-url.com',
        file: expect.objectContaining({
          id: 'test-file-id',
        }),
      });

      expect(vi.mocked(getSignedUrl)).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          input: expect.objectContaining({
            Bucket: expect.any(String),
            Key: expect.stringContaining('ehs-files'),
            ContentType: mockFileParams.mimeType,
          }),
        }),
        expect.objectContaining({ expiresIn: 3600 }),
      );
    });
  });

  describe('createFile', () => {
    it('should create file record with pending status', async () => {
      const result = await createFile({
        fileName: 'test.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        presignedUrl: 'https://test-presigned-url.com',
        s3Key: 'test-key',
        s3Bucket: 'test-bucket',
        upkeepCompanyId: 'company-123',
        uploadedBy: 'user-123',
        expiresAt: new Date(),
      });

      expect(result).toMatchObject({
        id: 'test-file-id',
      });
    });
  });

  describe('getFiles', () => {
    it('should apply all filters correctly', async () => {
      const filters = {
        entityType: 'incident' as const,
        entityId: 'incident-123',
        status: 'completed' as const,
      };

      const result = await getFiles(filters, mockUser);

      expect(result).toEqual([{ id: 'test-file-1' }, { id: 'test-file-2' }]);
    });

    it('should apply entityId filter correctly', async () => {
      const filters = {
        entityId: 'test-entity-123',
        entityType: 'incident',
      };

      const result = await getFiles(filters, mockUser);

      expect(result).toEqual([{ id: 'test-file-1' }, { id: 'test-file-2' }]);
    });
  });
});
