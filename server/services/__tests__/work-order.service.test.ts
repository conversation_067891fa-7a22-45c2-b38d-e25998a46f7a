import { describe, it, expect, vi, beforeEach, type MockedFunction } from 'vitest';
import { TRPCError } from '@trpc/server';
import type { Headers } from '../../utils/requestUtils';

// Mock dependencies first - before importing the module under test
const mockAxiosPost = vi.fn();
vi.mock('axios', () => ({
  default: {
    post: mockAxiosPost,
  },
}));

vi.mock('env', () => ({
  env: {
    UPKEEP_API_URL: 'https://api.upkeep.com',
    EHS_URL: 'https://local-ehs.onupkeep.com/ehs',
  },
}));

// Import after mocking
const { createWorkOrderFromCapa, searchWorkOrdersByCapaId, getWorkOrdersCountByCapa } = await import(
  '../work-order.service'
);

describe('Work Order Service', () => {
  const mockHeaders: Headers = {
    'x-user-id': 'test-user-id',
    'x-company-id': 'test-company-id',
  };

  const mockCapaData = {
    id: 'capa-123',
    title: 'Fix hydraulic system leak',
    slug: 'CAPA-0063',
    actionsToAddress:
      'Replace the hydraulic seal on Pump 42 immediately\nAdd hydraulic seal inspection to weekly checklist',
    priority: 'high' as const,
    dueDate: '2024-12-31T23:59:59.999Z',
    locationId: 'location-456',
    assetId: 'asset-789',
    userAssignedTo: 'user-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup default successful axios response
    mockAxiosPost.mockResolvedValue({
      data: { success: true },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  describe('createWorkOrderFromCapa', () => {
    it('should successfully create work order from CAPA data with all fields', async () => {
      const result = await createWorkOrderFromCapa(mockCapaData, mockHeaders);

      // Verify work order API was called with correct data
      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://api.upkeep.com/api/v1/work-orders/create/wrapper',
        {
          mainDescription: 'Fix hydraulic system leak',
          note: expect.stringContaining('This Work Order was created from'),
          priorityNumber: 3, // high priority = 3
          dueDate: '2024-12-31T23:59:59.999Z',
          objectLocationForWorkOrder: 'location-456',
          objectAsset: 'asset-789',
          userAssignedTo: 'user-123',
          capaId: 'capa-123',
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          withCredentials: true,
        }),
      );

      // Verify result
      expect(result).toBe(true);
    });

    it('should create work order without optional locationId, assetId, and userAssignedTo', async () => {
      const capaDataWithoutOptionals = {
        ...mockCapaData,
        locationId: undefined,
        assetId: undefined,
        userAssignedTo: undefined,
      };

      const result = await createWorkOrderFromCapa(capaDataWithoutOptionals, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          objectLocationForWorkOrder: undefined,
          objectAsset: undefined,
          userAssignedTo: undefined,
        }),
        expect.any(Object),
      );

      expect(result).toBe(true);
    });

    it('should handle different priority levels correctly', async () => {
      const testCases = [
        { priority: 'low' as const, expectedNumber: 1 },
        { priority: 'medium' as const, expectedNumber: 2 },
        { priority: 'high' as const, expectedNumber: 3 },
      ];

      for (const { priority, expectedNumber } of testCases) {
        const capaDataWithPriority = { ...mockCapaData, priority };

        const result = await createWorkOrderFromCapa(capaDataWithPriority, mockHeaders);

        expect(result).toBe(true);
        expect(mockAxiosPost).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            priorityNumber: expectedNumber,
          }),
          expect.any(Object),
        );

        vi.clearAllMocks();
        // Reset axios mock for next iteration
        mockAxiosPost.mockResolvedValue({
          data: { success: true },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        });
      }
    });

    it('should format actions with proper line breaks', async () => {
      const capaDataWithLineBreaks = {
        ...mockCapaData,
        actionsToAddress: 'Action 1\nAction 2\r\nAction 3\rAction 4',
      };

      const result = await createWorkOrderFromCapa(capaDataWithLineBreaks, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          note: expect.stringContaining('Action 1<br />Action 2<br />Action 3<br />Action 4'),
        }),
        expect.any(Object),
      );

      expect(result).toBe(true);
    });

    it('should include userAssignedTo when provided', async () => {
      const capaDataWithUser = {
        ...mockCapaData,
        userAssignedTo: 'assigned-user-456',
      };

      const result = await createWorkOrderFromCapa(capaDataWithUser, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          userAssignedTo: 'assigned-user-456',
        }),
        expect.any(Object),
      );

      expect(result).toBe(true);
    });

    it('should throw BAD_REQUEST error when id is missing', async () => {
      const invalidCapaData = {
        ...mockCapaData,
        id: '',
      };

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(TRPCError);

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(
        'Missing required fields: id, title, slug, actionsToAddress, priority, and dueDate are mandatory',
      );

      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should throw BAD_REQUEST error when title is missing', async () => {
      const invalidCapaData = {
        ...mockCapaData,
        title: '',
      };

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(TRPCError);

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(
        'Missing required fields: id, title, slug, actionsToAddress, priority, and dueDate are mandatory',
      );

      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should throw BAD_REQUEST error when slug is missing', async () => {
      const invalidCapaData = {
        ...mockCapaData,
        slug: '',
      };

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(TRPCError);

      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should throw BAD_REQUEST error when actionsToAddress is missing', async () => {
      const invalidCapaData = {
        ...mockCapaData,
        actionsToAddress: '',
      };

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(TRPCError);

      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should throw BAD_REQUEST error when dueDate is missing', async () => {
      const invalidCapaData = {
        ...mockCapaData,
        dueDate: '',
      };

      await expect(createWorkOrderFromCapa(invalidCapaData, mockHeaders)).rejects.toThrow(TRPCError);

      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should handle work order API failures', async () => {
      mockAxiosPost.mockRejectedValue(new Error('UpKeep API error'));

      await expect(createWorkOrderFromCapa(mockCapaData, mockHeaders)).rejects.toThrow('UpKeep API error');

      expect(mockAxiosPost).toHaveBeenCalled();
    });

    it('should handle work order API returning false result', async () => {
      mockAxiosPost.mockResolvedValue({
        data: { success: false },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const result = await createWorkOrderFromCapa(mockCapaData, mockHeaders);

      expect(result).toBe(false);
      expect(mockAxiosPost).toHaveBeenCalled();
    });

    it('should create proper CAPA link in note', async () => {
      const result = await createWorkOrderFromCapa(mockCapaData, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          note: expect.stringContaining(
            '<a href="https://local-ehs.onupkeep.com/ehs/capas/capa-123" target="_blank">CAPA-0063</a>',
          ),
        }),
        expect.any(Object),
      );

      expect(result).toBe(true);
    });
  });

  describe('searchWorkOrdersByCapaId', () => {
    // Mock the API response structure (flattened based on user's changes)
    const mockApiResponse = {
      success: true,
      results: [
        {
          id: 'wo-123',
          workOrderNumber: '008',
          mainDescription: 'Fix hydraulic leak - Created from CAPA',
          currentStatus: 'open',
          priorityNumber: 3,
          capaId: 'capa-123',
          userAssignedTo: { objectId: 'user-123' },
          objectLocationForWorkOrder: { objectId: 'location-123' },
          objectAsset: { id: 'asset-123', Name: 'Test Asset' },
          dueDate: '2024-12-31',
        },
        {
          id: 'wo-124',
          workOrderNumber: '007',
          mainDescription: 'Secondary work order',
          currentStatus: 'in_progress',
          priorityNumber: 2,
          capaId: 'capa-123',
          userAssignedTo: { objectId: 'user-456' },
          objectLocationForWorkOrder: { objectId: 'location-456' },
          objectAsset: { id: 'asset-456', Name: 'Another Asset' },
          dueDate: '2024-12-31',
        },
      ],
      hasMore: false,
    };

    // Expected transformed response
    const expectedResponse = [
      {
        id: 'wo-123',
        workOrderNumber: '008',
        title: 'Fix hydraulic leak - Created from CAPA',
        currentStatus: 'open',
        priority: 'high',
        dueDate: '2024-12-31',
        assignedTo: 'user-123',
        locationId: 'location-123',
        assetId: 'asset-123',
        assetName: 'Test Asset',
      },
      {
        id: 'wo-124',
        workOrderNumber: '007',
        title: 'Secondary work order',
        currentStatus: 'in_progress',
        priority: 'medium',
        dueDate: '2024-12-31',
        assignedTo: 'user-456',
        locationId: 'location-456',
        assetId: 'asset-456',
        assetName: 'Another Asset',
      },
    ];

    beforeEach(() => {
      mockAxiosPost.mockResolvedValue({
        data: mockApiResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    });

    it('should successfully search work orders by CAPA ID', async () => {
      const searchInput = {
        capaId: ['capa-123'],
        limit: 50,
        offset: 0,
        sort: 'createdAt DESC',
      };

      const result = await searchWorkOrdersByCapaId(searchInput, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://api.upkeep.com/api/v1/work-orders/search',
        {
          capaId: ['capa-123'],
          limit: 50,
          offset: 0,
          sort: 'createdAt DESC',
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          withCredentials: true,
        }),
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should use default values for optional parameters', async () => {
      const searchInput = {
        capaId: ['capa-123'],
        limit: 50,
        offset: 0,
      };

      const result = await searchWorkOrdersByCapaId(searchInput, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://api.upkeep.com/api/v1/work-orders/search',
        {
          capaId: ['capa-123'],
          limit: 50,
          offset: 0,
          sort: 'createdAt DESC',
        },
        expect.any(Object),
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle custom limit, offset, and sort parameters', async () => {
      const searchInput = {
        capaId: ['capa-123'],
        limit: 25,
        offset: 10,
        sort: 'title ASC',
      };

      const result = await searchWorkOrdersByCapaId(searchInput, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://api.upkeep.com/api/v1/work-orders/search',
        {
          capaId: ['capa-123'],
          limit: 25,
          offset: 10,
          sort: 'title ASC',
        },
        expect.any(Object),
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle API failures', async () => {
      mockAxiosPost.mockRejectedValue(new Error('UpKeep API error'));

      const searchInput = {
        capaId: ['capa-123'],
        limit: 50,
        offset: 0,
      };

      await expect(searchWorkOrdersByCapaId(searchInput, mockHeaders)).rejects.toThrow('UpKeep API error');
    });

    it('should handle empty response from API', async () => {
      const emptyApiResponse = {
        success: true,
        results: [],
        hasMore: false,
      };

      mockAxiosPost.mockResolvedValue({
        data: emptyApiResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const searchInput = {
        capaId: ['capa-123'],
        limit: 50,
        offset: 0,
      };

      const result = await searchWorkOrdersByCapaId(searchInput, mockHeaders);

      expect(result).toEqual([]);
    });

    it('should throw INTERNAL_SERVER_ERROR when response data is null', async () => {
      mockAxiosPost.mockResolvedValue({
        data: null,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const searchInput = {
        capaId: ['capa-123'],
        limit: 50,
        offset: 0,
      };

      await expect(searchWorkOrdersByCapaId(searchInput, mockHeaders)).rejects.toThrow(TRPCError);
      await expect(searchWorkOrdersByCapaId(searchInput, mockHeaders)).rejects.toThrow('Failed to search work orders');
    });
  });

  describe('getWorkOrdersCountByCapa', () => {
    it('should successfully count work orders by CAPA ID', async () => {
      const mockCountResponse = {
        success: true,
        result: 6,
      };

      mockAxiosPost.mockResolvedValue({
        data: mockCountResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const countInput = {
        capaId: ['capa-123'],
      };

      const result = await getWorkOrdersCountByCapa(countInput, mockHeaders);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://api.upkeep.com/api/v1/work-orders/search/count',
        {
          capaId: ['capa-123'],
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          withCredentials: true,
        }),
      );

      expect(result).toBe(6);
    });

    it('should handle zero count response', async () => {
      const mockCountResponse = {
        success: true,
        result: 0,
      };

      mockAxiosPost.mockResolvedValue({
        data: mockCountResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const countInput = {
        capaId: ['capa-456'],
      };

      const result = await getWorkOrdersCountByCapa(countInput, mockHeaders);

      expect(result).toBe(0);
    });

    it('should throw INTERNAL_SERVER_ERROR when count API response is unsuccessful', async () => {
      const mockCountResponse = {
        success: false,
        result: null,
      };

      mockAxiosPost.mockResolvedValue({
        data: mockCountResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const countInput = {
        capaId: ['capa-123'],
      };

      await expect(getWorkOrdersCountByCapa(countInput, mockHeaders)).rejects.toThrow(TRPCError);
      await expect(getWorkOrdersCountByCapa(countInput, mockHeaders)).rejects.toThrow('Failed to count work orders');
    });

    it('should handle API failures for count', async () => {
      mockAxiosPost.mockRejectedValue(new Error('UpKeep count API error'));

      const countInput = {
        capaId: ['capa-123'],
      };

      await expect(getWorkOrdersCountByCapa(countInput, mockHeaders)).rejects.toThrow('UpKeep count API error');
    });
  });
});
