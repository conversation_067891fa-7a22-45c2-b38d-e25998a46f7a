import { openai } from '@ai-sdk/openai';
import {
  CreateCapasSchema,
  IncidentCategorySchema,
  ReportTypeSchema,
  SeveritySchema,
  StatusSchema,
} from '@shared/schema.types';
import { generateObject, experimental_transcribe as transcribe } from 'ai';
import { addDays, format, subDays } from 'date-fns';
import { z } from 'zod';
import { OpenAIModel } from '../../env';
import { getCapaPrompts } from './prompts.service';

export const transcribeAudio = async (audio: Buffer) => {
  const transcript = await transcribe({
    model: openai.transcription('whisper-1'),
    audio: audio,
  });

  return transcript;
};

export const AnalyzeIncidentTranscriptSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: IncidentCategorySchema,
  severity: SeveritySchema.optional().default(SeveritySchema.enum.low),
  type: ReportTypeSchema.optional(),
  status: StatusSchema.optional(),
  immediateActions: z.string().optional(),
  reportedAt: z.coerce.date().optional(),
});

export type AnalyzeIncidentTranscript = z.infer<typeof AnalyzeIncidentTranscriptSchema>;

export const analyzeIncidentTranscript = async (
  transcript: string,
  timezone?: string,
): Promise<AnalyzeIncidentTranscript> => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd HH:mm:ss');

  // Example past dates for the prompt
  const lastWeekDate = format(subDays(currentDate, 7), 'yyyy-MM-dd');
  const lastMonthDate = format(subDays(currentDate, 30), 'yyyy-MM-dd');
  const yesterday = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_4_TURBO),
    schema: AnalyzeIncidentTranscriptSchema,
    system:
      'You are an AI assistant specialized in extracting detailed and accurate structured information about safety incidents from transcripts. ' +
      'Your task is to precisely identify key incident details while strictly adhering to the provided categorization systems. ' +
      'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
      'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing.' +
      `Today's date is ${formattedCurrentDate} for reference. When you see "last week", it refers to a date like ${lastWeekDate}, not a date in the future. ` +
      `When you see "last month", it refers to a date like ${lastMonthDate}, not a date in the future. ` +
      `When you see "yesterday", it refers to a date like ${yesterday}, not a date in the future. ` +
      "If the user doesn't provide date, you may assume the current date." +
      'The response should always be in english, despite the transcript being in another language.',
    prompt: `Extract comprehensive details from this safety incident transcript. Return ONLY a JSON object with these precise keys:
  
            title (a clear, concise title summarizing the incident)
            description (a detailed description of what happened)
            location (specific location where the incident occurred)
            type (choose one of the following must be one of: ${ReportTypeSchema.options.join(', ')})
            category (must be one of: ${IncidentCategorySchema.options.join(', ')})
            severity (if mentioned: ${SeveritySchema.options.join(', ')})
            status (if mentioned: ${StatusSchema.options.join(', ')})            
            immediateActions (any immediate actions taken after the incident)            
            reportedAt (date and time the incident was reported) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone


            IMPORTANT DATE HANDLING INSTRUCTIONS:
            - Today's date is ${formattedCurrentDate}
            - If the transcript mentions "last week", use a date around ${lastWeekDate}
            - If the transcript mentions "last month", use a date around ${lastMonthDate}
            - If the transcript mentions "yesterday", use ${yesterday}
            - If the user provides only hours and minutes, use the current date and the provided hours and minutes
            - If the user doesn't provide or mention a date, use the current date on UTC timezone
            
            Only include fields where information is explicitly provided in the transcript.
            Use exact category, severity, type, and rootCause values as specified.
            
            Transcript: "${transcript}"`,
  });

  return result.object;
};

// Start with the base CAPA creation schema, but remove fields that should NOT be provided by AI
const AnalyzeCapaTranscriptSchema = CreateCapasSchema.pick({
  rcaMethod: true,
  rcaFindings: true,
  rootCause: true,
  otherRootCause: true,
  title: true,
  priority: true,
  actionsToAddress: true,
  tags: true,
  type: true,
}).extend({
  // Ensure the due date can be coerced from strings like "2025-06-03" to a Date object
  dueDate: z.coerce.date().optional(),
});

export type AnalyzeCapaTranscript = z.infer<typeof AnalyzeCapaTranscriptSchema>;

export const analyzeCapaTranscript = async (transcript: string, timezone?: string): Promise<AnalyzeCapaTranscript> => {
  const capaPrompts = getCapaPrompts();

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_4_TURBO),
    schema: AnalyzeCapaTranscriptSchema,
    system: capaPrompts.systemPrompt,
    prompt: capaPrompts.generateUserPrompt(transcript, timezone),
  });

  // Further validate that dueDate is in the future
  if (result.object.dueDate && new Date(result.object.dueDate) < new Date()) {
    // If the parsed date is in the past, add appropriate days to make it future
    // This is a fallback in case the model still produces a past date
    const correctedDate = addDays(new Date(), 7); // Default to one week from now
    result.object.dueDate = correctedDate;
  }

  return result.object;
};
