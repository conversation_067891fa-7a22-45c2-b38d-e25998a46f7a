import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, auditTrailActionEnum, capas, files, incidents, statusEnum } from '@shared/schema';
import {
  CreateCapasFormSchema,
  EditCapasFormSchema,
  ListCapasSchema,
  TransientFileSchema,
  User,
} from '@shared/schema.types';
import {
  and,
  arrayOverlaps,
  asc,
  desc,
  eq,
  getTableColumns,
  gte,
  ilike,
  inArray,
  isNull,
  lt,
  lte,
  or,
  sql,
  SQL,
} from 'drizzle-orm';
import { db } from 'server/db';
import { z } from 'zod';

/**
 * Create a new CAPA (Corrective and Preventive Action)
 *
 * @param capaInput The CAPA data to create
 * @param user The authenticated user
 * @returns The created CAPA
 */
export const createCapa = async (capaInput: z.infer<typeof CreateCapasFormSchema>, user: User) => {
  // Prepare the values for insertion, ensuring server fields are properly set
  const capaValues = {
    ...capaInput,
    // Always set these values from the user context, overriding any provided values
    createdBy: user.id,
    upkeepCompanyId: user.roleId,
    status: capaInput.status || statusEnum.enumValues[0], // Default to the first status (open)
  };

  const inserted = await db.insert(capas).values(capaValues).returning({
    id: capas.id,
    createdBy: capas.createdBy,
    upkeepCompanyId: capas.upkeepCompanyId,
    title: capas.title,
    type: capas.type,
    priority: capas.priority,
    status: capas.status,
    dueDate: capas.dueDate,
    ownerId: capas.ownerId,
    incidentId: capas.incidentId,
    rcaFindings: capas.rcaFindings,
    slug: capas.slug,
  });

  // Create audit trail entry
  await db.insert(auditTrail).values({
    entityId: inserted[0].id,
    entityType: 'CAPA',
    details: JSON.stringify(inserted[0]),
    userId: inserted[0].createdBy,
    upkeepCompanyId: inserted[0].upkeepCompanyId,
    timestamp: new Date(),
    action: auditTrailActionEnum.enumValues[0], // Default to the first status (created)
  });

  return inserted[0];
};

export const getCapaById = async (id: string, user: User) => {
  // Fetch the CAPA that matches the ID and company ID, and is not deleted
  const result = await db
    .select({
      ...getTableColumns(capas),
      incidentSlug: incidents.slug,
      attachments: sql<z.infer<typeof TransientFileSchema>[]>`
        CASE
          WHEN COUNT(${files.id}) = 0 THEN NULL
          ELSE json_agg(
            json_build_object(
              'id', ${files.id},
              'name', ${files.fileName},
              'url', ${files.presignedUrl},
              'type', ${files.mimeType},
              'size', ${files.fileSize}
            )
          )
        END
      `,
    })
    .from(capas)
    .leftJoin(incidents, eq(capas.incidentId, incidents.id))
    .leftJoin(
      files,
      and(
        eq(files.entityId, capas.id),
        eq(files.upkeepCompanyId, user.roleId),
        eq(files.status, 'completed'),
        eq(files.entityType, 'capa'),
      ),
    )
    .where(and(eq(capas.id, id), eq(capas.upkeepCompanyId, user.roleId), isNull(capas.deletedAt)))
    .groupBy(capas.id, incidents.slug);

  return result.at(0);
};

export const getOverdueCapas = async () => {
  const result = await db
    .select({
      ...getTableColumns(capas),
      incidentSlug: incidents.slug,
      incidentTitle: incidents.title,
    })
    .from(capas)
    .leftJoin(incidents, eq(capas.incidentId, incidents.id))
    .where(and(eq(capas.status, statusEnum.enumValues[0]), lt(capas.dueDate, new Date())));

  return result;
};

export const listCapas = async (input: z.infer<typeof ListCapasSchema>, user: User) => {
  const {
    page = 1,
    limit = 10,
    includeArchived,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    status = [],
    type = [],
    priority = [],
    owner = [],
    dueDateStart,
    dueDateEnd,
    tags = [],
  } = input;

  // Build up all conditions in an array
  const conditions: SQL[] = [eq(capas.upkeepCompanyId, user.roleId)];

  if (!includeArchived) {
    conditions.push(eq(capas.archived, false));
  }

  if (search) {
    const searchFilter = or(
      ilike(capas.title, `%${search}%`),
      ilike(capas.rcaFindings, `%${search}%`),
      ilike(capas.slug, `%${search}%`),
    );
    conditions.push(searchFilter as SQL<unknown>);
  }

  if (status.length > 0) {
    conditions.push(inArray(capas.status, status));
  }

  if (type.length > 0) {
    conditions.push(inArray(capas.type, type));
  }

  if (priority.length > 0) {
    conditions.push(inArray(capas.priority, priority));
  }

  if (owner.length > 0) {
    conditions.push(inArray(capas.ownerId, owner));
  }

  // Date range filtering for due date
  if (dueDateStart) {
    conditions.push(gte(capas.dueDate, dueDateStart));
  }

  if (dueDateEnd) {
    conditions.push(lte(capas.dueDate, dueDateEnd));
  }

  if (tags.length > 0) {
    conditions.push(arrayOverlaps(capas.tags, tags));
  }

  // Apply all conditions at once using and()
  let query = db
    .select({
      id: capas.id,
      title: capas.title,
      type: capas.type,
      status: capas.status,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      incidentId: capas.incidentId,
      incidentSlug: incidents.slug,
      archived: capas.archived,
      slug: capas.slug,
      tags: capas.tags,
      createdAt: capas.createdAt,
      updatedAt: capas.updatedAt,
      deletedAt: capas.deletedAt,
      createdBy: capas.createdBy,
    })
    .from(capas)
    .leftJoin(incidents, eq(capas.incidentId, incidents.id))
    .where(and(...conditions))
    .$dynamic();

  if (sortBy) {
    query = query.orderBy(sortOrder === 'desc' ? desc(capas.createdAt) : asc(capas.createdAt));
  }

  const filtered = await query.limit(limit).offset((page - 1) * limit);

  return createPaginatedResponse(filtered, {
    total: filtered.length,
    page,
    limit,
  });
};

/**
 * Update an existing CAPA
 *
 * @param id The ID of the CAPA to update
 * @param capaInput The CAPA data to update
 * @param user The authenticated user
 * @returns The updated CAPA data
 */
export const updateCapa = async (id: string, capaInput: z.infer<typeof EditCapasFormSchema>, user: User) => {
  const updated = await db
    .update(capas)
    .set(capaInput)
    .where(and(eq(capas.id, id), eq(capas.upkeepCompanyId, user.roleId)))
    .returning({
      status: capas.status,
      archived: capas.archived,
      id: capas.id,
      createdBy: capas.createdBy,
      upkeepCompanyId: capas.upkeepCompanyId,
      title: capas.title,
      type: capas.type,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      incidentId: capas.incidentId,
      rcaFindings: capas.rcaFindings,
      slug: capas.slug,
    });

  const updatedCapa = updated.at(0);

  if (!updatedCapa) {
    throw new Error('CAPA not found');
  }

  let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

  if ('status' in capaInput && 'status' in updatedCapa) {
    const statusMap = {
      open: auditTrailActionEnum.enumValues[7],
      in_review: auditTrailActionEnum.enumValues[6],
      closed: auditTrailActionEnum.enumValues[5],
    };

    action = statusMap[
      updatedCapa.status as keyof typeof statusMap
    ] as (typeof auditTrailActionEnum.enumValues)[number];
  }

  if ('archived' in capaInput && 'archived' in updatedCapa) {
    action = updatedCapa.archived ? 'archived' : 'unarchived';
  }

  await db.insert(auditTrail).values({
    entityId: updatedCapa.id,
    entityType: 'CAPA',
    details: JSON.stringify(updatedCapa),
    userId: updatedCapa.createdBy,
    upkeepCompanyId: updatedCapa.upkeepCompanyId,
    timestamp: new Date(),
    action,
  });

  return updatedCapa;
};
