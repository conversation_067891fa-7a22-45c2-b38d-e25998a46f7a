import { logger } from '@server/utils/logger';
import { createPaginatedResponse } from '@server/utils/pagination';
import type {
  CreateAccessPointFormSchema,
  ListAccessPointsSchema,
  PaginatedResponse,
  UpdateAccessPointSchema,
} from '@shared/schema.types';
import { and, eq, gte, ilike, inArray, isNull, lte } from 'drizzle-orm';
import { z } from 'zod';
import { accessPoints, auditTrail, auditTrailActionEnum } from '../../shared/schema';
import { db } from '../db';
import { User, UserPublic } from '@shared/schema.types';
import { getUserPublic } from '@server/services/user.service';

export const createAccessPoint = async (data: z.infer<typeof CreateAccessPointFormSchema>, user: User) => {
  const [accessPoint] = await db
    .insert(accessPoints)
    .values({
      ...data,
      upkeepCompanyId: user.roleId,
      createdBy: user.id,
      createdAt: new Date(),
    })
    .returning();

  await db.insert(auditTrail).values({
    entityId: accessPoint.id,
    entityType: 'access_point',
    details: JSON.stringify(accessPoint),
    userId: user.id,
    upkeepCompanyId: user.roleId,
    timestamp: new Date(),
    action: auditTrailActionEnum.enumValues[0], // 'created'
  });

  return accessPoint;
};

export const updateAccessPoint = async (id: string, data: z.infer<typeof UpdateAccessPointSchema>, user: User) => {
  // Get current access point for audit comparison
  const currentAccessPoint = await getAccessPointById(id, user);

  if (!currentAccessPoint) {
    throw new Error('Access point not found');
  }

  if (data.archived && data.archived === true) {
    data.status = 'inactive';
  }

  const [accessPoint] = await db
    .update(accessPoints)
    .set({ ...data, updatedAt: new Date() })
    .where(and(eq(accessPoints.id, id), eq(accessPoints.upkeepCompanyId, user.roleId)))
    .returning();

  if (!accessPoint) {
    throw new Error('Access point not found');
  }

  // Determine audit action based on what was updated
  let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

  if ('archived' in data && 'archived' in accessPoint) {
    action = accessPoint.archived ? 'archived' : 'unarchived';
  }

  await db.insert(auditTrail).values({
    entityId: accessPoint.id,
    entityType: 'access_point',
    details: JSON.stringify(accessPoint),
    userId: user.id,
    upkeepCompanyId: user.roleId,
    timestamp: new Date(),
    action,
  });

  return accessPoint;
};

export const getAccessPointById = async (id: string, user: Pick<User, 'roleId'>) => {
  const [accessPoint] = await db
    .select()
    .from(accessPoints)
    .where(and(eq(accessPoints.id, id), eq(accessPoints.upkeepCompanyId, user.roleId), isNull(accessPoints.deletedAt)));
  return accessPoint || null;
};

export const getAccessPoints = async (
  input: z.infer<typeof ListAccessPointsSchema>,
  user: User,
): Promise<PaginatedResponse<typeof accessPoints.$inferSelect & { createdByUser?: UserPublic }>> => {
  const {
    page = 1,
    limit = 50,
    search,
    includeArchived,
    locationId,
    status,
    createdBy,
    createdDateStart,
    createdDateEnd,
  } = input || {};
  const offset = (page - 1) * limit;

  // Build where conditions
  const conditions = [eq(accessPoints.upkeepCompanyId, user.roleId), isNull(accessPoints.deletedAt)];

  if (!includeArchived) {
    conditions.push(eq(accessPoints.archived, false));
  }

  if (locationId && locationId.length > 0) {
    conditions.push(inArray(accessPoints.locationId, locationId));
  }

  if (status) {
    conditions.push(eq(accessPoints.status, status));
  }

  if (createdBy && createdBy.length > 0) {
    conditions.push(inArray(accessPoints.createdBy, createdBy));
  }

  if (createdDateStart) {
    conditions.push(gte(accessPoints.createdAt, createdDateStart));
  }

  if (createdDateEnd) {
    const endOfDay = new Date(createdDateEnd);
    endOfDay.setHours(23, 59, 59, 999);
    conditions.push(lte(accessPoints.createdAt, endOfDay));
  }

  if (search) {
    conditions.push(ilike(accessPoints.name, `%${search}%`));
  }

  // Get paginated results
  const data = await db
    .select()
    .from(accessPoints)
    .where(and(...conditions))
    .limit(limit)
    .offset(offset)
    .orderBy(accessPoints.createdAt);

  const userIds = Array.from(new Set(data.map((ap) => ap.createdBy).filter(Boolean))) as string[];
  let users: UserPublic[] = [];
  if (userIds.length > 0) {
    try {
      users = await getUserPublic({ roleId: user.roleId, objectId: userIds });
    } catch (error) {
      logger.error('Error fetching users:', error);
      users = [];
    }
  }

  // Create a map for quick user lookup
  const userMap = users.reduce(
    (acc, user) => {
      acc[user.id] = user;
      return acc;
    },
    {} as Record<string, UserPublic>,
  );

  // Map users to access points
  const accessPointsWithUsers = data.map((accessPoint) => ({
    ...accessPoint,
    createdByUser: accessPoint.createdBy ? userMap[accessPoint.createdBy] : undefined,
  }));

  return createPaginatedResponse(accessPointsWithUsers, {
    total: data.length,
    page,
    limit,
  });
};
