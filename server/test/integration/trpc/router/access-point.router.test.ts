import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { accessPointRouter } from '@server/trpc/router/access-point.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { accessPoints } from '@shared/schema';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

describe('accessPointRouter', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(accessPoints);
  });

  describe('create', () => {
    it('should create a new access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
        status: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify the access point was created
      expect(accessPoint).toBeDefined();
      expect(accessPoint.name).toBe(mockAccessPointInput.name);
    });

    it('should validate required fields', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();
    });
  });

  describe('update', () => {
    it('should update an access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const updatedAccessPointInput = {
        id: accessPoint.id,
        name: 'Updated Access Point',
        description: 'Updated description',
        locationId: '456',
        type: '456',
      };

      const updatedAccessPoint = await caller.update(updatedAccessPointInput);

      expect(updatedAccessPoint).toBeDefined();
      expect(updatedAccessPoint.id).toBe(accessPoint.id);
      expect(updatedAccessPoint.name).toBe(updatedAccessPointInput.name);
      expect(updatedAccessPoint.description).toBe(updatedAccessPointInput.description);
      expect(updatedAccessPoint.locationId).toBe(updatedAccessPointInput.locationId);
    });
  });

  describe('list', () => {
    it('should list access points', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const accessPoints = await caller.list({});

      expect(accessPoints).toBeDefined();

      expect(accessPoints.data.length).toBe(1);
    });
  });

  describe('getById', () => {
    it('should get an access point by id', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const accessPointById = await caller.getById({ id: accessPoint.id });

      expect(accessPointById).toBeDefined();

      expect(accessPointById.id).toBe(accessPoint.id);
      expect(accessPointById.name).toBe(mockAccessPointInput.name);
    });
  });
});
