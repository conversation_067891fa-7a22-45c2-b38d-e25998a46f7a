import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { commentRouter } from '@server/trpc/router/comment.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { capaPriorityEnum, capas, capaTypeEnum, comments, statusEnum } from '@shared/schema';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

const mockCapaInput = {
  title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
  type: capaTypeEnum.enumValues[0],
  rcaFindings: 'Test findings',
  ownerId: mockUser.id,
  actionsToAddress: 'Test actions',
  status: statusEnum.enumValues[0],
  priority: capaPriorityEnum.enumValues[0],
  dueDate: new Date('2024-12-31'),
  upkeepCompanyId: mockUser.roleId,
  createdBy: mockUser.id,
};

let createdCapaId: string;

describe('commentRouter', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);

    const capa = await db.insert(capas).values(mockCapaInput).returning();
    createdCapaId = capa[0].id;
  });

  afterEach(async () => {
    await db.delete(comments);
  });

  describe('create', () => {
    it('should create a new comment', async () => {
      const mockCommentInput = {
        content: 'Test comment',
        entityType: 'capa' as const,
        entityId: createdCapaId,
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);
    });
  });

  describe('list', () => {
    it('should list comments', async () => {
      const mockCommentInput = {
        content: 'Test comment for list',
        entityType: 'capa' as const,
        entityId: createdCapaId,
      };

      const caller = commentRouter.createCaller(mockContext);
      await caller.create(mockCommentInput);
      const comments = await caller.list({ entityId: createdCapaId, entityType: 'capa' });

      expect(comments).toBeDefined();
      expect(comments.length).toBe(1);
      expect(comments[0].content).toBe(mockCommentInput.content);
    });
  });

  describe('getById', () => {
    it('should get a comment by id', async () => {
      const mockCommentInput = {
        content: 'Test comment for get',
        entityType: 'capa' as const,
        entityId: createdCapaId,
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);
      const commentById = await caller.getById({ id: comment.id });

      expect(commentById).toBeDefined();
      expect(commentById?.id).toBe(comment.id);
      expect(commentById?.content).toBe(comment.content);
    });
  });

  describe('delete', () => {
    it('should delete a comment', async () => {
      const mockCommentInput = {
        content: 'Test comment for delete',
        entityType: 'capa' as const,
        entityId: createdCapaId,
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);
      await caller.delete({ id: comment.id });
      const commentById = await caller.getById({ id: comment.id });

      expect(commentById).toBeNull();
    });
  });
});
