import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { capaRouter } from '@server/trpc/router/capa.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { capaTypeEnum, statusEnum, capaPriorityEnum } from '@shared/schema';
import { db } from '@server/db';
import { capas, files } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

// Mock the notification service
vi.mock('@server/services/email/capa-notification.service', () => ({
  handleCAPANotifications: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Asset' }]),
}));

describe('capa router', () => {
  const mockContext = createMockContext(mockUser) as any;
  let createdCapaId: string;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Setup default mock implementations
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    // Clean up any created CAPAs and files
    await db.delete(files);
    await db.delete(capas);
  });

  describe('create', () => {
    it('should create a new capa', async () => {
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      // Store the ID for cleanup
      createdCapaId = capa.id;

      // Verify the CAPA was created
      expect(capa).toBeDefined();
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined(); // this is generated by the database sequence
      expect(capa.type).toBe(mockCapaInput.type);
      expect(capa.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa.status).toBe(mockCapaInput.status);
      expect(capa.priority).toBe(mockCapaInput.priority);

      // Verify we can find it in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, capa.id),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(mockCapaInput.title);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should validate required fields', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();

      // Test missing title
      await expect(
        caller.create({
          type: capaTypeEnum.enumValues[0],
          rcaFindings: 'Test findings',
          ownerId: mockUser.id,
          actionsToAddress: 'Test actions',
          status: statusEnum.enumValues[0],
          priority: capaPriorityEnum.enumValues[0],
        } as any),
      ).rejects.toThrow();
    });
  });

  describe('getById', () => {
    it('should get a capa by id without files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa.id;

      // Now get it by ID
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined();
      expect(capa.type).toBe(mockCapaInput.type);
      expect(capa.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa.status).toBe(mockCapaInput.status);
      expect(capa.priority).toBe(mockCapaInput.priority);
      expect(capa.attachments).toBeNull();

      // Verify user service was called
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should get a capa with its associated files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa.id;

      // Create an associated file
      await db.insert(files).values({
        id: 'test-file-id',
        upkeepCompanyId: mockUser.roleId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'capa',
        entityId: createdCapaId,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Now get the CAPA by ID
      const capa = await caller.getById({ id: createdCapaId });

      // Verify CAPA data
      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.slug).toBeDefined();

      // Verify file data
      expect(capa.attachments).toBeDefined();
      expect(capa.attachments).toHaveLength(1);
      expect(capa.attachments[0]).toMatchObject({
        name: 'test.pdf',
        url: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        type: 'application/pdf',
        size: 1024,
      });
    });

    it('should throw error for non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow();
    });
  });

  describe('list', () => {
    it('should list capas with default pagination', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa.id;

      // Now list CAPAs
      const result = await caller.list({});

      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.data[0].id).toBe(createdCapaId);

      // Verify user service was called
      expect(getUsers).toHaveBeenCalledWith(
        expect.objectContaining({
          objectId: [mockUser.id],
        }),
      );
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should list capas with filters', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test with status filter
      const statusResult = await caller.list({
        status: [statusEnum.enumValues[0]],
      });
      expect(statusResult.data.every((capa) => capa.status === statusEnum.enumValues[0])).toBe(true);

      // Test with type filter
      const typeResult = await caller.list({
        type: [capaTypeEnum.enumValues[0]],
      });
      expect(typeResult.data.every((capa) => capa.type === capaTypeEnum.enumValues[0])).toBe(true);

      // Test with priority filter
      const priorityResult = await caller.list({
        priority: [capaPriorityEnum.enumValues[0]],
      });
      expect(priorityResult.data.every((capa) => capa.priority === capaPriorityEnum.enumValues[0])).toBe(true);

      // Verify user service was called for each filter
      expect(getUsers).toHaveBeenCalledTimes(3);
      expect(hasPermission).toHaveBeenCalledTimes(3);
    });
  });

  describe('update', () => {
    it('should update a capa', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa.id;

      // Now update it
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA Title',
        status: statusEnum.enumValues[1],
        priority: capaPriorityEnum.enumValues[1],
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);
      expect(updatedCapa.title).toBe(updateInput.title);
      expect(updatedCapa.status).toBe(updateInput.status);
      expect(updatedCapa.priority).toBe(updateInput.priority);

      // Verify the update in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, createdCapaId),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(updateInput.title);
      expect(dbCapa?.status).toBe(updateInput.status);
      expect(dbCapa?.priority).toBe(updateInput.priority);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should throw error when updating non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      await expect(
        caller.update({
          id: 'non-existent-id',
          title: 'Updated Title',
        }),
      ).rejects.toThrow();
    });
  });
});
