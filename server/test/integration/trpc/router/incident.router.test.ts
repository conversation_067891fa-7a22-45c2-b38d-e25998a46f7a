import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { files, incidents } from '@shared/schema';
import { incidentRouter } from '@server/trpc/router/incident.router';
import { getLocations } from '@server/services/location.service';

const mockIncidentInput = {
  title: 'Test incident',
  description: 'Test description',
  type: 'incident' as const,
  category: 'other' as const,
  severity: 'low' as const,
  reportedAt: new Date(),
  locationId: '123',
  assetIds: ['123'],
};

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/email/incident-notification.service', () => ({
  handleIncidentNotifications: vi.fn(),
  sendIncidentSubmittedNotification: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Asset' }]),
}));

describe('incident router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getLocations).mockResolvedValue([{ id: '123', name: 'Test Location' }]);
  });

  afterEach(async () => {
    await db.delete(files);
    await db.delete(incidents);
  });

  describe('create', () => {
    it('should create a new incident', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);

      expect(incident).toBeDefined();
      expect(incident?.title).toBe(mockIncidentInput.title);
      expect(incident?.description).toBe(mockIncidentInput.description);
    });
  });

  describe('getById', () => {
    it('should get an incident by id without files', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);
      const incidentById = await caller.getById({ id: incident?.id! });

      expect(incidentById).toBeDefined();
      expect(incidentById?.title).toBe(mockIncidentInput.title);
      expect(incidentById?.description).toBe(mockIncidentInput.description);
      expect(incidentById?.media).toBeNull();
    });

    it('should get an incident by id with files', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);

      await db.insert(files).values({
        id: 'test-file-id',
        upkeepCompanyId: mockUser.roleId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'incident',
        entityId: incident?.id!,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      const incidentById = await caller.getById({ id: incident?.id! });

      expect(incidentById).toBeDefined();
      expect(incidentById?.media).toBeDefined();
      expect(incidentById?.media?.length).toBe(1);
      expect(incidentById?.media?.[0]?.name).toBe('test.pdf');
      expect(incidentById?.media?.[0]?.url).toBe('https://test-bucket.s3.amazonaws.com/test.pdf');
      expect(incidentById?.media?.[0]?.type).toBe('application/pdf');
      expect(incidentById?.media?.[0]?.size).toBe(1024);
    });
  });

  describe('list', () => {
    it('should list incidents', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      await caller.create(mockIncidentInput);
      const incidents = await caller.list({});

      expect(incidents).toBeDefined();
      expect(incidents.data.length).toBe(1);
      expect(incidents.data[0].title).toBe(mockIncidentInput.title);
      expect(incidents.data[0].description).toBe(mockIncidentInput.description);
    });
  });

  describe('update', () => {
    it('should update an incident', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);
      const updatedIncident = await caller.update({ id: incident?.id!, title: 'Updated title' });

      expect(updatedIncident).toBeDefined();
      expect(updatedIncident?.title).toBe('Updated title');
    });
  });
});
