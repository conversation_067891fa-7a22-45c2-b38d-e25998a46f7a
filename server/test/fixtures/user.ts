import { MODULES, ALLOWED_ACTIONS, PERMISSION_LEVELS } from '@shared/user-permissions';

export const mockUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  roleId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  userAccountType: 'ADMIN',
  permissions: [
    {
      dataObject: MODULES.EHS_CAPA,
      action: ALLOWED_ACTIONS.CREATE,
      permissionLevel: PERMISSION_LEVELS.FULL,
    },
    {
      dataObject: MODULES.EHS_CAPA,
      action: ALLOWED_ACTIONS.VIEW,
      permissionLevel: PERMISSION_LEVELS.FULL,
    },
    {
      dataObject: MODULES.EHS_CAPA,
      action: ALLOWED_ACTIONS.EDIT,
      permissionLevel: PERMISSION_LEVELS.FULL,
    },
  ],
  featureFlags: { webEHS: true },
};
