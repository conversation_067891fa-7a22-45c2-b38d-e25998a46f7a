import { describe, it, expect, vi } from 'vitest';
import express from 'express';
import request from 'supertest';

describe('Express Middleware', () => {
  it('should handle JSON parsing with correct limits', async () => {
    const app = express();
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: false, limit: '10mb' }));
    
    app.post('/test', (req, res) => {
      res.json(req.body);
    });
    
    const testData = { test: 'value' };
    const response = await request(app)
      .post('/test')
      .send(testData);
    
    expect(response.status).toBe(200);
    expect(response.body).toEqual(testData);
  });
  
  it('should handle CORS middleware', async () => {
    const app = express();
    app.use(require('cors')());
    
    app.get('/test', (_, res) => {
      res.json({ success: true });
    });
    
    const response = await request(app).get('/test');
    
    expect(response.status).toBe(200);
    expect(response.header['access-control-allow-origin']).toBe('*');
  });
  
  it('should set security headers', async () => {
    const app = express();
    const testUrl = 'https://test.upkeep.com';
    
    app.use((_, res, next) => {
      res.set('Content-Security-Policy', `frame-ancestors 'self' ${testUrl};`);
      next();
    });
    
    app.get('/test', (_, res) => {
      res.json({ success: true });
    });
    
    const response = await request(app).get('/test');
    
    expect(response.status).toBe(200);
    expect(response.header['content-security-policy']).toBe(`frame-ancestors 'self' ${testUrl};`);
  });
}); 