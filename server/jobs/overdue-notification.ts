import { getOverdueCapas } from '@server/services/capa.service';
import { sendCAPAOverdueNotification } from '@server/services/email/capa-notification.service';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic } from '@server/services/user.service';
import { logger } from '@server/utils/logger';
import { USER_TYPES } from '@shared/user-permissions';
import { differenceInDays } from 'date-fns';

const run = async () => {
  logger.info('Running overdue notification job');

  const capas = await getOverdueCapas();

  // create a map with upkeepCompanyId as key and array of locationIds as value
  const locationMap = new Map<string, string[]>();
  for (const capa of capas) {
    if (!locationMap.has(capa.upkeepCompanyId)) {
      locationMap.set(capa.upkeepCompanyId, []);
    }

    if (capa.locationId) {
      const existingLocations = locationMap.get(capa.upkeepCompanyId) || [];
      locationMap.set(capa.upkeepCompanyId, [...new Set([...existingLocations, capa.locationId])]);
    }
  }

  // Fetch all locations in parallel
  const locationPromises = Array.from(locationMap.entries())
    .filter(([_, locationIds]) => locationIds.length > 0)
    .map(([roleId, locationIds]) =>
      searchLocationsPublic({
        roleId,
        objectId: locationIds,
        search: '',
        limit: locationIds.length,
      }).then((locations) => ({ roleId, locations })),
    );

  const locationResults = await Promise.all(locationPromises);

  // Create a nested lookup map for quick access: roleId -> { locationId -> location }
  const locationLookup = new Map();
  locationResults.forEach(({ roleId, locations }) => {
    const locationsByIdMap = new Map();
    locations.forEach((location) => {
      locationsByIdMap.set(location.id, location);
    });
    locationLookup.set(roleId, locationsByIdMap);
  });

  for (const capa of capas) {
    try {
      // calculate days overdue
      let daysOverdue = 0;
      if (capa.dueDate) {
        daysOverdue = differenceInDays(new Date(), capa.dueDate);
      }

      // Get location from nested lookup map
      const location = capa.locationId ? locationLookup.get(capa.upkeepCompanyId)?.get(capa.locationId) : undefined;

      const { upkeepCompanyId } = capa;

      const userPromises = [getUserPublic({ roleId: upkeepCompanyId, userAccountType: USER_TYPES.ADMIN })];

      if (capa.ownerId) {
        userPromises.push(getUserPublic({ roleId: upkeepCompanyId, objectId: [capa.ownerId] }));
      }

      const [admins, owner] = await Promise.all(userPromises);

      const toAdmins = admins.map((admin) => ({
        email: admin.email ?? '',
        name: admin.name ?? '',
        type: 'to' as const,
      }));

      const toOwner =
        owner && owner[0]
          ? [
              {
                email: owner[0].email ?? '',
                name: owner[0].name ?? '',
                type: 'to' as const,
              },
            ]
          : [];

      const toNotified = [...toAdmins, ...toOwner];

      if (toNotified.length === 0) {
        logger.warn('No recipients found for CAPA overdue notification', { capaId: capa.id });
        continue;
      }

      const notifications = [];

      if (toAdmins.length > 0) {
        notifications.push(
          sendCAPAOverdueNotification({
            capa,
            daysOverdue,
            toNotified: toAdmins,
            owner: owner && owner[0] ? owner[0] : undefined,
            location: location?.name,
            isAdmin: true,
            isTechnicianOwner: false,
          }),
        );
      }

      if (toOwner.length > 0) {
        notifications.push(
          sendCAPAOverdueNotification({
            capa,
            daysOverdue,
            toNotified: toOwner,
            owner: owner && owner[0] ? owner[0] : undefined,
            location: location?.name,
            isAdmin: false,
            isTechnicianOwner: true,
          }),
        );
      }

      await Promise.all(notifications);

      logger.info('Sent overdue notification', { capaId: capa.id, recipients: toNotified.map((r) => r.email) });
    } catch (error) {
      logger.error('Failed to send overdue notification', { error, capaId: capa.id });
    }
  }

  logger.info('Overdue notification job completed');
  process.exit(0);
};

run();
