import { vi, describe, it, expect, beforeEach } from 'vitest';

import * as capaService from '@server/services/capa.service';
import * as notificationService from '@server/services/email/capa-notification.service';
import * as locationService from '@server/services/location.service';
import * as userService from '@server/services/user.service';
import { logger } from '../../utils/logger';
import { USER_TYPES } from '@shared/user-permissions';

vi.mock('@server/services/capa.service');
vi.mock('@server/services/email/capa-notification.service');
vi.mock('@server/services/location.service');
vi.mock('@server/services/user.service');
vi.mock('../../utils/logger');

// Mock process.exit
const mockExit = vi.fn();
process.exit = mockExit as never;

describe('overdue-notification job', () => {
  const mockCapas = [
    {
      id: 'capa1',
      title: 'Test CAPA',
      dueDate: new Date(Date.now() - 86400000), // 1 day overdue
      status: 'open',
      upkeepCompanyId: 'company1',
      locationId: 'loc1',
      ownerId: 'user1',
      slug: 'capa-1',
      rcaFindings: 'Root cause',
      type: 'TypeA',
      priority: 'High',
      incidentId: 'incident1',
      incidentTitle: 'Incident Title',
      incidentSlug: 'incident-slug',
    },
  ];

  const mockLocation = [{ id: 'loc1', name: 'Test Location' }];
  const mockAdmins = [{ email: '<EMAIL>', name: 'Admin User' }];
  const mockOwner = [{ email: '<EMAIL>', name: 'Owner User' }];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();
    mockExit.mockClear();
  });

  it('should send separate notifications for admins and owner', async () => {
    (capaService.getOverdueCapas as any).mockResolvedValue(mockCapas);
    (locationService.searchLocationsPublic as any).mockResolvedValue(mockLocation);
    (userService.getUserPublic as any).mockResolvedValueOnce(mockAdmins).mockResolvedValueOnce(mockOwner);
    (notificationService.sendCAPAOverdueNotification as any).mockResolvedValue(undefined);

    await import('../overdue-notification');

    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledTimes(2);

    // Check admin notification
    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        capa: expect.objectContaining({ id: 'capa1' }),
        daysOverdue: expect.any(Number),
        toNotified: expect.arrayContaining([expect.objectContaining({ email: '<EMAIL>' })]),
        owner: expect.objectContaining({ email: '<EMAIL>' }),
        location: 'Test Location',
        isAdmin: true,
        isTechnicianOwner: false,
      }),
    );

    // Check owner notification
    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        capa: expect.objectContaining({ id: 'capa1' }),
        daysOverdue: expect.any(Number),
        toNotified: expect.arrayContaining([expect.objectContaining({ email: '<EMAIL>' })]),
        owner: expect.objectContaining({ email: '<EMAIL>' }),
        location: 'Test Location',
        isAdmin: false,
        isTechnicianOwner: true,
      }),
    );
  });

  it('should NOT send notification if there is no one to notify', async () => {
    (capaService.getOverdueCapas as any).mockResolvedValue(mockCapas);
    (locationService.searchLocationsPublic as any).mockResolvedValue(mockLocation);
    (userService.getUserPublic as any)
      .mockResolvedValueOnce([]) // admins
      .mockResolvedValueOnce([]); // owner
    (notificationService.sendCAPAOverdueNotification as any).mockResolvedValue(undefined);

    await import('../overdue-notification');

    expect(notificationService.sendCAPAOverdueNotification).not.toHaveBeenCalled();
    expect(logger.warn).toHaveBeenCalledWith(
      'No recipients found for CAPA overdue notification',
      expect.objectContaining({ capaId: mockCapas[0].id }),
    );
  });

  it('should handle CAPA without ownerId (only admins notified)', async () => {
    const capaNoOwner = { ...mockCapas[0], ownerId: undefined };
    (capaService.getOverdueCapas as any).mockResolvedValue([capaNoOwner]);
    (locationService.searchLocationsPublic as any).mockResolvedValue(mockLocation);
    (userService.getUserPublic as any).mockResolvedValueOnce(mockAdmins);
    (notificationService.sendCAPAOverdueNotification as any).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Should only call getUserPublic once (for admins)
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledWith(
      expect.objectContaining({
        roleId: 'company1',
        userAccountType: USER_TYPES.ADMIN,
      }),
    );

    // Should send notification only to admins
    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledTimes(1);
    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        capa: expect.objectContaining({ id: 'capa1' }),
        toNotified: expect.arrayContaining([expect.objectContaining({ email: '<EMAIL>' })]),
        owner: undefined,
        location: 'Test Location',
        isAdmin: true,
        isTechnicianOwner: false,
      }),
    );
  });

  it('should handle CAPA without locationId (location is undefined)', async () => {
    const capaNoLocation = { ...mockCapas[0], locationId: undefined };
    (capaService.getOverdueCapas as any).mockResolvedValue([capaNoLocation]);
    (locationService.searchLocationsPublic as any).mockResolvedValue([]);
    (userService.getUserPublic as any).mockResolvedValueOnce(mockAdmins).mockResolvedValueOnce(mockOwner);
    (notificationService.sendCAPAOverdueNotification as any).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Should not call searchLocationsPublic
    expect(locationService.searchLocationsPublic).not.toHaveBeenCalled();
    expect(notificationService.sendCAPAOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        location: undefined,
      }),
    );
  });

  it('should log error if sendCAPAOverdueNotification throws', async () => {
    (capaService.getOverdueCapas as any).mockResolvedValue(mockCapas);
    (locationService.searchLocationsPublic as any).mockResolvedValue(mockLocation);
    (userService.getUserPublic as any).mockResolvedValueOnce(mockAdmins).mockResolvedValueOnce(mockOwner);
    (notificationService.sendCAPAOverdueNotification as any).mockRejectedValue(new Error('Email error'));

    await import('../overdue-notification');

    expect(logger.error).toHaveBeenCalledWith(
      'Failed to send overdue notification',
      expect.objectContaining({
        error: expect.any(Error),
        capaId: mockCapas[0].id,
      }),
    );
  });
});
