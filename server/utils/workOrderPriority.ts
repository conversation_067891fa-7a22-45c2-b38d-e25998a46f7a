export const convertNumberToPriority = (priorityNumber: number): 'low' | 'medium' | 'high' => {
  switch (priorityNumber) {
    case 1:
      return 'low';
    case 2:
      return 'medium';
    case 3:
      return 'high';
    default:
      return 'medium'; // Default to medium for unknown values
  }
};

export const convertPriorityToNumber = (priority: 'low' | 'medium' | 'high'): number => {
  switch (priority) {
    case 'low':
      return 1;
    case 'medium':
      return 2;
    case 'high':
      return 3;
    default:
      return 2; // Default to medium
  }
};
