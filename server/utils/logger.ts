import { env } from 'env';
import winston from 'winston';

function getWinstonFormat(environmentPrefix: string) {
  let winstonFormat = winston.format.combine(
    winston.format.json(),
    winston.format.errors({ stack: true }),
  );

  if (environmentPrefix === 'local') {
    // improves presentation of local log metadata.
    winstonFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
      winston.format.prettyPrint(),
      winston.format.colorize(),
      winston.format.errors({ stack: true }),
    );
  }
  return winstonFormat;
}

export const logger = winston.createLogger({
  defaultMeta: {
    instance: {
      created: new Date().toISOString(),
    },
    app: 'ehs',
  },
  transports: [
    new winston.transports.Console({
      level: env.LOG_LEVEL,
      format: getWinstonFormat(env.ENVIRONMENT_PREFIX),
      handleExceptions: !['test', 'ci'].includes(env.ENVIRONMENT_PREFIX),
      silent: ['test', 'ci'].includes(env.ENVIRONMENT_PREFIX) && env.SHOW_LOGS,
    }),
  ],
});
