import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock winston with more detailed implementation
const mockFormatCombine = vi.fn().mockReturnValue('combined-format');
const mockFormatJson = vi.fn().mockReturnValue('json-format');
const mockFormatErrors = vi.fn().mockReturnValue('errors-format');
const mockFormatTimestamp = vi.fn().mockReturnValue('timestamp-format');
const mockFormatPrettyPrint = vi.fn().mockReturnValue('pretty-format');
const mockFormatColorize = vi.fn().mockReturnValue('color-format');

const mockConsoleTransport = vi.fn();

// Create winston mock
vi.mock('winston', () => ({
  default: {
    createLogger: vi.fn().mockReturnValue({
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    }),
    format: {
      combine: mockFormatCombine,
      json: mockFormatJson,
      errors: mockFormatErrors,
      timestamp: mockFormatTimestamp,
      prettyPrint: mockFormatPrettyPrint,
      colorize: mockFormatColorize,
    },
    transports: {
      Console: mockConsoleTransport,
    },
  },
}));

// Mock environment variables
const mockEnv = {
  LOG_LEVEL: 'info',
  ENVIRONMENT_PREFIX: 'local',
  SHOW_LOGS: true,
};

vi.mock('env', () => ({
  env: mockEnv,
}));

describe('Logger', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a logger with correct configuration', async () => {
    // Import the logger module - this will trigger the code
    await import('../logger');
    
    // Access winston from the mock
    const winston = (await import('winston')).default;
    
    // Test that createLogger was called with the right configuration
    expect(winston.createLogger).toHaveBeenCalledWith(expect.objectContaining({
      defaultMeta: expect.objectContaining({
        app: 'ehs',
        instance: expect.objectContaining({
          created: expect.any(String),
        }),
      }),
      transports: expect.any(Array),
    }));
  });

  it('should use pretty print format for local environment', async () => {
    // Set env to local before importing
    mockEnv.ENVIRONMENT_PREFIX = 'local';
    
    // Re-import to trigger code with local environment
    vi.resetModules();
    await import('../logger');
    
    // Test format functions were called for local environment
    expect(mockFormatTimestamp).toHaveBeenCalled();
    expect(mockFormatPrettyPrint).toHaveBeenCalled();
    expect(mockFormatColorize).toHaveBeenCalled();
    expect(mockFormatCombine).toHaveBeenCalled();
  });

  it('should use simple format for non-local environments', async () => {
    // Set env to production before importing
    mockEnv.ENVIRONMENT_PREFIX = 'production';
    
    // Re-import to trigger code with production environment
    vi.resetModules();
    await import('../logger');
    
    // Test format functions were called for production environment
    expect(mockFormatJson).toHaveBeenCalled();
    expect(mockFormatErrors).toHaveBeenCalled();
    expect(mockFormatCombine).toHaveBeenCalled();
  });
}); 