import { describe, it, expect } from 'vitest';
import { formatNoteWithBreakLines } from '../formating';

describe('formating utils', () => {
  describe('formatNoteWithBreakLines', () => {
    it('should convert Unix line breaks (\\n) to <br />', () => {
      const input = 'Line 1\nLine 2\nLine 3';
      const expected = 'Line 1<br />Line 2<br />Line 3';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should convert Windows line breaks (\\r\\n) to <br />', () => {
      const input = 'Line 1\r\nLine 2\r\nLine 3';
      const expected = 'Line 1<br />Line 2<br />Line 3';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should convert Mac line breaks (\\r) to <br />', () => {
      const input = 'Line 1\rLine 2\rLine 3';
      const expected = 'Line 1<br />Line 2<br />Line 3';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle mixed line break types in the same string', () => {
      const input = 'Line 1\r\nLine 2\nLine 3\rLine 4';
      const expected = 'Line 1<br />Line 2<br />Line 3<br />Line 4';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle empty string', () => {
      const input = '';
      const expected = '';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle string with no line breaks', () => {
      const input = 'Single line text with no breaks';
      const expected = 'Single line text with no breaks';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle multiple consecutive line breaks', () => {
      const input = 'Line 1\n\n\nLine 2';
      const expected = 'Line 1<br /><br /><br />Line 2';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle line breaks at the beginning of string', () => {
      const input = '\nLine 1\nLine 2';
      const expected = '<br />Line 1<br />Line 2';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle line breaks at the end of string', () => {
      const input = 'Line 1\nLine 2\n';
      const expected = 'Line 1<br />Line 2<br />';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle text with existing HTML tags', () => {
      const input = '<p>Paragraph 1</p>\n<p>Paragraph 2</p>';
      const expected = '<p>Paragraph 1</p><br /><p>Paragraph 2</p>';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle text with special characters and line breaks', () => {
      const input = 'Action 1: Fix & replace\nAction 2: Test @ location\nAction 3: Document $ cost';
      const expected = 'Action 1: Fix & replace<br />Action 2: Test @ location<br />Action 3: Document $ cost';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle realistic CAPA actions text', () => {
      const input =
        'Action 1: Replace the hydraulic seal on Pump 42 immediately\nAction 2: Inspect all other pumps for similar wear\nAction 3: Update maintenance schedule\nAction 4: Train operators on proper usage';
      const expected =
        'Action 1: Replace the hydraulic seal on Pump 42 immediately<br />Action 2: Inspect all other pumps for similar wear<br />Action 3: Update maintenance schedule<br />Action 4: Train operators on proper usage';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should preserve whitespace within lines', () => {
      const input = '  Line 1 with spaces  \n  Line 2 with spaces  ';
      const expected = '  Line 1 with spaces  <br />  Line 2 with spaces  ';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });

    it('should handle Unicode characters with line breaks', () => {
      const input = 'Línea 1 con acentos\nLigne 2 avec accents\nLine 3 with émojis 🔧';
      const expected = 'Línea 1 con acentos<br />Ligne 2 avec accents<br />Line 3 with émojis 🔧';
      const result = formatNoteWithBreakLines(input);
      expect(result).toBe(expected);
    });
  });
});
