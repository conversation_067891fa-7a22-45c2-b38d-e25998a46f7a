import { PaginatedResponse } from '@shared/schema.types';

export interface PaginationParams {
  page: number;
  limit: number;
  total: number;
}

export function calculatePaginationMeta({ page, limit, total }: PaginationParams) {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNextPage,
    hasPreviousPage,
  };
}

export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

export function createPaginatedResponse<T>(
  data: T[],
  paginationParams: PaginationParams
): PaginatedResponse<T> {
  return {
    data,
    pagination: calculatePaginationMeta(paginationParams),
  };
} 