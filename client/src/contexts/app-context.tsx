import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { createContext, ReactNode, useContext } from 'react';

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppContextProvider');
  }
  return context;
};

export type AppContextType = {
  user: RouterOutputs['user']['me'] | undefined;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
};

const AppContext = createContext<AppContextType>({} as AppContextType);

export const AppContextProvider = ({ children }: { children: ReactNode }) => {
  const {
    data: user,
    isLoading,
    isError,
    isSuccess,
  } = trpc.user.me.useQuery(undefined, {
    staleTime: 1000 * 60 * 15, // 15 minutes
    networkMode: 'online',
  });

  return (
    <AppContext.Provider
      value={{
        user,
        isLoading,
        isError,
        isSuccess,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export { AppContext };
