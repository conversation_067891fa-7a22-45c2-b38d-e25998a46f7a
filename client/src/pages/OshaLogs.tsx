// @ts-nocheck

import React, { useState, useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Calendar,
  Download,
  Filter,
  Search,
  Eye,
  Building
} from "lucide-react";

// OSHA Log types for the application
export interface OshaLogEntry {
  id: number;
  logId: string;
  title: string;
  type: 'Death' | 'Days Away' | 'Restricted' | 'Other Recordable' | '';
  status: 'Draft' | 'Submitted' | 'Archived';
  employee: string;
  dateTime: string;
  location: string;
  linkedIncident: string | null;
  privacyCase: boolean;
}

// Form 300A summary data interface
export interface OshaSummaryData {
  companyName: string;
  facilityId: string;
  naicsCode: string;
  year: number;
  totalDeaths: number;
  totalDaysAway: number;
  totalRestricted: number;
  totalOtherRecordable: number;
  totalCases: number;
  totalEmployees: number;
  totalHoursWorked: number;
  trcRate: number;
  dartRate: number;
}

export default function OshaLogs() {
  const queryClient = useQueryClient();
  const [location, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState<string>("log");
  const [filterYear, setFilterYear] = useState<string>("2025");
  const [filterType, setFilterType] = useState<string>("");
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [filterFacility, setFilterFacility] = useState<string>("");
  const [showPrivacyCases, setShowPrivacyCases] = useState<boolean>(true);
  const [includeArchived, setIncludeArchived] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // Fetch OSHA logs from the API
  const { data: oshaLogs = [], isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/osha-logs'],
    refetchInterval: 10000, // Auto-refresh every 10 seconds
  });
  
  // Mock data for initial rendering until API is ready
  const defaultSummaryData: OshaSummaryData = {
    companyName: "UpKeep Manufacturing",
    facilityId: "FAC-001",
    naicsCode: "336390",
    year: 2025,
    totalDeaths: 1,
    totalDaysAway: 2,
    totalRestricted: 1,
    totalOtherRecordable: 1,
    totalCases: 5,
    totalEmployees: 250,
    totalHoursWorked: 520000,
    trcRate: 1.92,
    dartRate: 0.77
  };
  
  // Fetch OSHA summary data from the API
  const { data: summaryData = defaultSummaryData, isLoading: isLoadingSummary } = useQuery<OshaSummaryData>({
    queryKey: ['/api/osha-summary', filterYear],
    refetchInterval: 10000, // Auto-refresh every 10 seconds
  });

  // Apply filters to the OSHA logs
  const filteredLogs = useMemo(() => {
    if (!Array.isArray(oshaLogs)) return [];
    
    return oshaLogs.filter((log: OshaLogEntry) => {
      // Year filter
      if (filterYear && !log.logId.includes(filterYear)) {
        return false;
      }
      
      // Case Type filter
      if (filterType && filterType !== "all" && log.type !== filterType) {
        return false;
      }
      
      // Status filter
      if (filterStatus && filterStatus !== "all" && log.status !== filterStatus) {
        return false;
      }
      
      // Facility filter
      if (filterFacility && filterFacility !== "all") {
        // In a real implementation, we would filter by facility
        // For now, we just keep all records if the filter is for "all"
        return false;
      }
      
      // Privacy Cases filter
      if (!showPrivacyCases && log.privacyCase) {
        return false;
      }
      
      // Archived filter
      if (!includeArchived && log.status === "Archived") {
        return false;
      }
      
      // Search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          log.title.toLowerCase().includes(query) ||
          log.employee.toLowerCase().includes(query) ||
          log.logId.toLowerCase().includes(query) ||
          log.location.toLowerCase().includes(query) ||
          (log.linkedIncident && log.linkedIncident.toLowerCase().includes(query))
        );
      }
      
      return true;
    });
  }, [oshaLogs, filterYear, filterType, filterStatus, filterFacility, showPrivacyCases, includeArchived, searchQuery]);

  const handleRowClick = (logId: string) => {
    // Find the OSHA form with this logId to get its ID
    if (Array.isArray(oshaLogs)) {
      const form = oshaLogs.find((log: OshaLogEntry) => log.logId === logId);
      if (form && form.id) {
        // Use the form's ID for the path parameter
        setLocation(`/osha-view/${form.id}`);
      } else {
        // Fallback to query parameter if the form is not found (should not happen)
        toast({
          title: "Error",
          description: "Could not find the requested OSHA form",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Loading...",
        description: "Please wait while we load the OSHA forms data",
      });
    }
  };
  
  const handleExport300A = () => {
    if (!summaryData) {
      toast({
        title: "Error",
        description: "Summary data is not available. Please try again later.",
        variant: "destructive"
      });
      return;
    }
    
    try {
      toast({
        title: "Export initiated",
        description: "OSHA Form 300A export started. Your file will download shortly.",
      });
      
      // Create a printable div for the OSHA Form 300A
      const printDiv = document.createElement('div');
      printDiv.style.width = '8.5in';
      printDiv.style.height = '11in';
      printDiv.style.margin = '0 auto';
      printDiv.style.padding = '0.5in';
      printDiv.style.fontFamily = 'Arial, sans-serif';
      printDiv.style.fontSize = '10pt';
      printDiv.style.position = 'relative';
      
      // Form title section
      const title = document.createElement('div');
      title.style.textAlign = 'center';
      title.style.marginBottom = '0.25in';
      title.innerHTML = `
        <h1 style="font-size: 18pt; margin: 0;">OSHA Form 300A</h1>
        <h2 style="font-size: 14pt; margin: 5px 0;">Summary of Work-Related Injuries and Illnesses</h2>
        <p style="margin: 5px 0;">All establishments covered by Part 1904 must complete this Summary page, even if no injuries or illnesses occurred during the year.</p>
        <p style="margin: 5px 0;"><strong>Year: ${summaryData.year}</strong></p>
      `;
      printDiv.appendChild(title);
      
      // Establishment information section
      const estInfo = document.createElement('div');
      estInfo.style.marginBottom = '0.25in';
      estInfo.style.borderTop = '2px solid #000';
      estInfo.style.borderBottom = '2px solid #000';
      estInfo.style.padding = '0.25in 0';
      estInfo.innerHTML = `
        <h3 style="font-size: 12pt; margin: 0 0 10px 0;">Establishment Information</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.25in;">
          <div>
            <p style="margin: 0 0 5px 0;"><strong>Company Name:</strong> ${summaryData.companyName}</p>
            <p style="margin: 0 0 5px 0;"><strong>Facility ID:</strong> ${summaryData.facilityId}</p>
            <p style="margin: 0 0 5px 0;"><strong>NAICS Code:</strong> ${summaryData.naicsCode}</p>
          </div>
          <div>
            <p style="margin: 0 0 5px 0;"><strong>Annual Average Number of Employees:</strong> ${summaryData.totalEmployees}</p>
            <p style="margin: 0 0 5px 0;"><strong>Total Hours Worked by All Employees:</strong> ${summaryData.totalHoursWorked.toLocaleString()}</p>
          </div>
        </div>
      `;
      printDiv.appendChild(estInfo);
      
      // Summary of Work-Related Injuries and Illnesses
      const summaryTable = document.createElement('div');
      summaryTable.style.marginBottom = '0.25in';
      summaryTable.innerHTML = `
        <h3 style="font-size: 12pt; margin: 0 0 10px 0;">Number of Cases</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
          <tr>
            <th style="border: 1px solid #000; padding: 5px; width: 50%; text-align: left;">Type of Case</th>
            <th style="border: 1px solid #000; padding: 5px; width: 50%; text-align: center;">Number</th>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">Deaths</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.totalDeaths}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">Cases with days away from work</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.totalDaysAway}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">Cases with job transfer or restriction</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.totalRestricted}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">Other recordable cases</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.totalOtherRecordable}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px; font-weight: bold;">Total</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center; font-weight: bold;">${summaryData.totalCases}</td>
          </tr>
        </table>
      `;
      printDiv.appendChild(summaryTable);
      
      // Injury Rates Section
      const ratesSection = document.createElement('div');
      ratesSection.style.marginBottom = '0.25in';
      ratesSection.innerHTML = `
        <h3 style="font-size: 12pt; margin: 0 0 10px 0;">Injury and Illness Rates</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
          <tr>
            <th style="border: 1px solid #000; padding: 5px; width: 70%; text-align: left;">Rate</th>
            <th style="border: 1px solid #000; padding: 5px; width: 30%; text-align: center;">Value</th>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">Total Recordable Case Rate (per 200,000 hours)</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.trcRate.toFixed(2)}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 5px;">DART Rate (per 200,000 hours)</td>
            <td style="border: 1px solid #000; padding: 5px; text-align: center;">${summaryData.dartRate.toFixed(2)}</td>
          </tr>
        </table>
      `;
      printDiv.appendChild(ratesSection);
      
      // Certification Section
      const certSection = document.createElement('div');
      certSection.style.marginBottom = '0.25in';
      certSection.style.borderTop = '2px solid #000';
      certSection.style.padding = '0.25in 0';
      certSection.innerHTML = `
        <h3 style="font-size: 12pt; margin: 0 0 10px 0;">Certification</h3>
        <p style="margin: 0 0 15px 0;">I certify that I have examined this document and that to the best of my knowledge the entries are true, accurate, and complete.</p>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.25in;">
          <div>
            <p style="margin: 0 0 20px 0;"><strong>Company Executive:</strong> ________________________</p>
            <p style="margin: 0 0 5px 0;"><strong>Title:</strong> ________________________</p>
          </div>
          <div>
            <p style="margin: 0 0 20px 0;"><strong>Phone:</strong> ________________________</p>
            <p style="margin: 0 0 5px 0;"><strong>Date:</strong> ________________________</p>
          </div>
        </div>
      `;
      printDiv.appendChild(certSection);
      
      // Footer note
      const footerNote = document.createElement('div');
      footerNote.style.fontSize = '8pt';
      footerNote.style.color = '#666';
      footerNote.style.position = 'absolute';
      footerNote.style.bottom = '0.25in';
      footerNote.style.left = '0.5in';
      footerNote.style.right = '0.5in';
      footerNote.innerHTML = `
        <p style="margin: 0;">Post this Summary page from February 1 to April 30 of the year following the year covered by the form.</p>
        <p style="margin: 5px 0 0 0;">Generated by UpKeep EHS on ${new Date().toLocaleDateString()}</p>
      `;
      printDiv.appendChild(footerNote);
      
      // Add to document, print, then remove
      document.body.appendChild(printDiv);
      
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printDiv.innerHTML;
      
      setTimeout(() => {
        window.print();
        document.body.innerHTML = originalContents;
        
        toast({
          title: "Export complete",
          description: "OSHA Form 300A has been sent to the printer or saved as PDF.",
          duration: 3000
        });
      }, 500);
    } catch (error) {
      console.error("Error exporting OSHA Form 300A:", error);
      toast({
        title: "Export failed",
        description: "There was an error exporting the OSHA Form 300A.",
        variant: "destructive"
      });
    }
  };

  // Format a date string for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MM/dd/yyyy");
    } catch (e) {
      return "Invalid date";
    }
  };
  
  // Get badge color based on case type
  const getCaseTypeBadgeColor = (type: string) => {
    switch (type) {
      case "Death":
        return "bg-red-100 text-red-800 hover:bg-red-200";
      case "Days Away":
        return "bg-amber-100 text-amber-800 hover:bg-amber-200";
      case "Restricted":
        return "bg-orange-100 text-orange-800 hover:bg-orange-200";
      case "Other Recordable":
        return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };
  
  // Get badge color based on status
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Draft":
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      case "Submitted":
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case "Archived":
        return "bg-purple-100 text-purple-800 hover:bg-purple-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">OSHA Logs</h1>
          <p className="text-gray-500">Manage and track OSHA recordable incidents</p>
        </div>
        
        <div className="mt-4 md:mt-0 space-x-2">
          <Select value={filterYear} onValueChange={setFilterYear}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Select Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2025">2025</SelectItem>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs defaultValue="log" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="log">OSHA Log (Form 300)</TabsTrigger>
          <TabsTrigger value="summary">Summary (Form 300A)</TabsTrigger>
          <TabsTrigger value="reports">Individual Reports (Form 301)</TabsTrigger>
        </TabsList>
        
        {/* OSHA Log (Form 300) Tab */}
        <TabsContent value="log">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <CardTitle>OSHA Form 300 Log of Work-Related Injuries and Illnesses</CardTitle>
                <div className="flex space-x-2 mt-4 sm:mt-0">
                  <Button variant="outline" size="sm" onClick={handleExport300A}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex flex-col md:flex-row justify-between space-y-4 md:space-y-0">
                {/* Search */}
                <div className="relative w-full md:w-72">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search logs..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                {/* Filters */}
                <div className="flex flex-wrap gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Case Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="Death">Death</SelectItem>
                      <SelectItem value="Days Away">Days Away</SelectItem>
                      <SelectItem value="Restricted">Restricted</SelectItem>
                      <SelectItem value="Other Recordable">Other Recordable</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Draft">Draft</SelectItem>
                      <SelectItem value="Submitted">Submitted</SelectItem>
                      <SelectItem value="Archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={filterFacility} onValueChange={setFilterFacility}>
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Facility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Facilities</SelectItem>
                      <SelectItem value="FAC-001">Main Plant</SelectItem>
                      <SelectItem value="FAC-002">Warehouse</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex space-x-4 mb-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-privacy-cases"
                    checked={showPrivacyCases}
                    onCheckedChange={setShowPrivacyCases}
                  />
                  <Label htmlFor="show-privacy-cases">Show Privacy Cases</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="include-archived"
                    checked={includeArchived}
                    onCheckedChange={setIncludeArchived}
                  />
                  <Label htmlFor="include-archived">Include Archived</Label>
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100vh-400px)] w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>OSHA Log ID</TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead>Case Type</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingLogs ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          Loading OSHA logs...
                        </TableCell>
                      </TableRow>
                    ) : filteredLogs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          No OSHA logs found matching your criteria.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredLogs.map((log: OshaLogEntry) => (
                        <TableRow 
                          key={log.id} 
                          className="cursor-pointer hover:bg-gray-50"
                          onClick={() => handleRowClick(log.logId)}
                        >
                          <TableCell className="font-medium">{log.logId}</TableCell>
                          <TableCell>
                            {log.privacyCase ? (
                              <span className="italic">Privacy Case</span>
                            ) : (
                              log.employee
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge className={getCaseTypeBadgeColor(log.type)}>
                              {log.type}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatDate(log.dateTime)}</TableCell>
                          <TableCell>{log.location}</TableCell>
                          <TableCell>
                            <Badge className={getStatusBadgeColor(log.status)}>
                              {log.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRowClick(log.logId);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Summary (Form 300A) Tab */}
        <TabsContent value="summary">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <CardTitle>OSHA Form 300A Summary of Work-Related Injuries and Illnesses</CardTitle>
                <div className="flex space-x-2 mt-4 sm:mt-0">
                  <Button variant="outline" size="sm" onClick={handleExport300A}>
                    <Download className="h-4 w-4 mr-2" />
                    Export PDF
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingSummary ? (
                <div className="text-center py-8">Loading summary data...</div>
              ) : !summaryData ? (
                <div className="text-center py-8">No summary data available for {filterYear}.</div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Establishment Information Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <Building className="h-5 w-5 mr-2" />
                        Establishment Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm text-gray-500">Company Name</Label>
                          <p className="font-medium">{summaryData.companyName}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Facility ID</Label>
                          <p className="font-medium">{summaryData.facilityId}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">NAICS Code</Label>
                          <p className="font-medium">{summaryData.naicsCode}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Year</Label>
                          <p className="font-medium">{summaryData.year}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Annual Average Number of Employees</Label>
                          <p className="font-medium">{summaryData.totalEmployees}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Total Hours Worked</Label>
                          <p className="font-medium">{summaryData.totalHoursWorked.toLocaleString()}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Cases Summary Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <Calendar className="h-5 w-5 mr-2" />
                        Cases Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm text-gray-500">Deaths</Label>
                            <p className="font-medium text-red-600">{summaryData.totalDeaths}</p>
                          </div>
                          <div>
                            <Label className="text-sm text-gray-500">Days Away From Work</Label>
                            <p className="font-medium text-amber-600">{summaryData.totalDaysAway}</p>
                          </div>
                          <div>
                            <Label className="text-sm text-gray-500">Job Transfer/Restriction</Label>
                            <p className="font-medium text-orange-600">{summaryData.totalRestricted}</p>
                          </div>
                          <div>
                            <Label className="text-sm text-gray-500">Other Recordable Cases</Label>
                            <p className="font-medium text-blue-600">{summaryData.totalOtherRecordable}</p>
                          </div>
                        </div>
                        
                        <div className="border-t pt-4">
                          <div className="flex justify-between items-center">
                            <Label className="text-sm text-gray-500">Total Cases</Label>
                            <p className="font-bold text-lg">{summaryData.totalCases}</p>
                          </div>
                        </div>
                        
                        <div className="border-t pt-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm text-gray-500">Total Recordable Case (TRC) Rate</Label>
                              <p className="font-medium">{summaryData.trcRate.toFixed(2)}</p>
                            </div>
                            <div>
                              <Label className="text-sm text-gray-500">DART Rate</Label>
                              <p className="font-medium">{summaryData.dartRate.toFixed(2)}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Individual Reports (Form 301) Tab */}
        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <CardTitle>OSHA Form 301 Injury and Illness Incident Reports</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex flex-col md:flex-row justify-between space-y-4 md:space-y-0">
                {/* Search */}
                <div className="relative w-full md:w-72">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search reports..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100vh-400px)] w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Case ID</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Case Type</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingLogs ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          Loading reports...
                        </TableCell>
                      </TableRow>
                    ) : filteredLogs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          No reports found matching your criteria.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredLogs.map((log: OshaLogEntry) => (
                        <TableRow 
                          key={log.id} 
                          className="cursor-pointer hover:bg-gray-50"
                          onClick={() => handleRowClick(log.logId)}
                        >
                          <TableCell className="font-medium">{log.logId}</TableCell>
                          <TableCell>{log.title}</TableCell>
                          <TableCell>
                            {log.privacyCase ? (
                              <span className="italic">Privacy Case</span>
                            ) : (
                              log.employee
                            )}
                          </TableCell>
                          <TableCell>{formatDate(log.dateTime)}</TableCell>
                          <TableCell>
                            <Badge className={getCaseTypeBadgeColor(log.type)}>
                              {log.type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRowClick(log.logId);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}