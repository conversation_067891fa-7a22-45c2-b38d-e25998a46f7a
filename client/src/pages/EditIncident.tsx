import { MediaUpload } from '@/components/composite/media-upload';
import { EditIncidentError } from '@/components/incidents/edit/edit-incident-error';
import { EditIncidentLoading } from '@/components/incidents/edit/edit-incident-loading';
import { AsyncDropdown } from '@/components/ui/async-dropdown';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { incidentCategoryEnum, reportTypeEnum, rootCauseEnum, severityEnum, statusEnum } from '@shared/schema';
import { EditIncidentFormSchema, IdArraySchema } from '@shared/schema.types';
import axios from 'axios';
import { format } from 'date-fns';
import { ArrowLeft, CalendarIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { z } from 'zod';

const FormSchema = EditIncidentFormSchema;

type FormValues = z.infer<typeof FormSchema>;

export default function EditIncident({ params }: { params: { id: string } }) {
  const [_, navigate] = useLocation();
  const incidentId = params.id;
  const utils = trpc.useUtils();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [locationSearch, setLocationSearch] = useState('');
  const [assetSearch, setAssetSearch] = useState('');
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: 'incident',
      title: '',
      reportedAt: new Date(),
      category: undefined,
      severity: undefined,
      description: '',
      immediateActions: '',
      rootCause: undefined,
    },
  });

  const { data: incident, isLoading, error, isSuccess } = trpc.incident.getById.useQuery({ id: incidentId });

  const { data: locations, isLoading: isLoadingLocations } = trpc.location.search.useQuery({
    page: 1,
    limit: 100,
    search: locationSearch,
  });

  const { data: assets, isLoading: isLoadingAssets } = trpc.asset.search.useQuery({
    page: 1,
    limit: 100,
    search: assetSearch,
    locationId: form.watch('locationId') ?? undefined,
  });

  useEffect(() => {
    const selectedLocationId = form.watch('locationId');
    if (selectedLocationId && selectedLocationId !== incident?.locationId) {
      form.setValue('assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId'), incident?.locationId]);

  const { mutate: updateIncident } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: incidentId });
      utils.auditTrail.get.invalidate({ entityId: incidentId, entityType: 'incident' });
    },
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();

  // Update form values when incident data is loaded
  useEffect(() => {
    if (incident && isSuccess) {
      form.reset({
        type: incident.type,
        title: incident.title,
        reportedAt: incident.reportedAt,
        category: incident.category,
        severity: incident.severity,
        description: incident.description,
        immediateActions: incident.immediateActions,
        rootCause: incident.rootCause,
        otherRootCause: incident.rootCause === 'other' ? incident.otherRootCause : '',
        status: incident.status,
        oshaReportable: incident.oshaReportable,
        locationId: incident.locationId,
        assetIds: incident.assetIds,
        media: incident.media,
      });
    }
  }, [incident, isSuccess]);

  // Handle form submission
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as any)[field] = values[field as keyof FormValues];
      }
    }

    try {
      updateIncident({
        ...toUpdate,
        id: incidentId,
      });

      const hasNewMedia = values.media?.some((file) => !file.id);

      console.log('toUpdate', toUpdate);
      console.log('incident', incident);
      console.log('values', values);

      console.log('hasNewMedia', hasNewMedia);

      // user can remove all media files

      // user can add some new files

      // user can remove some files

      // clean media for incident
      if (hasNewMedia) {
        await removeFiles(incident?.media?.map((file) => file.id).filter(Boolean) as z.infer<typeof IdArraySchema>);
      }

      // upload new media
      for (const [index, file] of values.media?.entries() ?? []) {
        const result = await getPresignedUrl({
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          entityType: 'incident',
          entityId: incident?.id,
        });

        try {
          await axios.put(result.presignedUrl, mediaFiles[index], {
            headers: {
              Accept: 'application/json',
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      // Show success message
      toast('Incident updated', {
        description: 'Your changes have been saved successfully.',
      });

      // Navigate back to the incident details page
      navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incidentId));
    } catch (error) {
      console.error('Error updating incident', error);
      toast('Error updating incident', {
        description: 'There was a problem saving your changes. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return <EditIncidentLoading />;
  }

  if (error || !incident) {
    return <EditIncidentError />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button
          variant="ghost"
          onClick={() => window.history.back()}
          className="gap-1 hover:bg-neutral-300 text-neutral-900"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 className="text-2xl font-bold text-neutral-black">Edit #{incident.slug}</h1>
      </div>

      {/* Main layout container - full width form */}
      <div>
        <div>
          {incident && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* Report Type */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Report Type <span className="text-destructive-500">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select report type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(reportTypeEnum.enumValues).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type === 'incident' ? 'Incident' : 'Near Miss'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        An incident is an event that caused injury or damage. A near miss is an event that could have
                        caused injury or damage but didn't.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Title <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Brief, descriptive title" {...field} />
                      </FormControl>
                      <FormDescription>Provide a clear, concise title that describes the incident.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date and Time */}
                <FormField
                  control={form.control}
                  name="reportedAt"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Date & Time <span className="text-red-500">*</span>
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground',
                              )}
                            >
                              {field.value ? format(field.value, 'PPP p') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              if (date) {
                                // Preserve time from original date if it exists
                                const currentDate = field.value;
                                if (currentDate) {
                                  const newDate = new Date(date);
                                  newDate.setHours(currentDate.getHours());
                                  newDate.setMinutes(currentDate.getMinutes());
                                  field.onChange(newDate);
                                } else {
                                  field.onChange(date);
                                }
                              }
                            }}
                          />

                          {/* Time picker */}
                          <div className="p-3 border-t border-gray-100">
                            <div className="flex items-center justify-between">
                              <label className="text-sm font-medium">Time:</label>
                              <Input
                                type="time"
                                className="w-36"
                                value={
                                  field.value
                                    ? `${field.value.getHours().toString().padStart(2, '0')}:${field.value.getMinutes().toString().padStart(2, '0')}`
                                    : ''
                                }
                                onChange={(e) => {
                                  if (e.target.value && field.value) {
                                    const [hours, minutes] = e.target.value.split(':').map(Number);
                                    const newDate = new Date(field.value);
                                    newDate.setHours(hours);
                                    newDate.setMinutes(minutes);
                                    field.onChange(newDate);
                                  }
                                }}
                              />
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                      <FormDescription>When did the incident occur?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Location */}
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Location</FormLabel>
                      </div>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Where did it happen?"
                          {...field}
                          value={field.value}
                          options={locations?.data.reduce(
                            (acc, location) => {
                              acc[location.id] = location.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingLocations}
                          search={locationSearch}
                          onSearch={(search) => {
                            setLocationSearch(search);
                          }}
                          multi={false}
                        />
                      </FormControl>
                      <FormDescription>
                        Specific area, building, or equipment where the incident occurred
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Assets */}
                <FormField
                  control={form.control}
                  name="assetIds"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Assets</FormLabel>
                      </div>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Select assets"
                          {...field}
                          value={field.value ?? ''}
                          options={assets?.data.reduce(
                            (acc, asset) => {
                              acc[asset.id] = asset.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingAssets}
                          search={assetSearch}
                          onSearch={(search) => {
                            setAssetSearch(search);
                          }}
                          multi={true}
                        />
                      </FormControl>
                      <FormDescription>
                        {/* Equipment or asset involved (AI will suggest based on selected location) */}
                        Select the assets that were involved in the incident. If the incident involved multiple assets,
                        select all that apply.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Category */}
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hazard Category</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Type of hazard" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(incidentCategoryEnum.enumValues).map((category: string) => (
                              <SelectItem key={category} value={category}>
                                {category.charAt(0).toUpperCase() + category.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>Examples: Chemical, Electrical, Ergonomic</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Severity Level */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="severity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Severity Level <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select severity" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(severityEnum.enumValues).map((level) => (
                              <SelectItem key={level} value={level}>
                                {level.charAt(0).toUpperCase() + level.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>How severe was the incident or potential outcome?</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Incident Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={incident.status}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusEnum.enumValues.map((status) => (
                            <SelectItem key={status} value={status}>
                              {status
                                .split('_')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(' ')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Change the current state of this incident. All updates are logged in the Status Timeline.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Detailed description of what happened..."
                          className="min-h-[120px]"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>Provide details about what happened, when, where, and how.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Immediate Actions Taken */}
                <FormField
                  control={form.control}
                  name="immediateActions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Immediate Actions Taken</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Actions taken immediately after the incident..."
                          className="min-h-[100px]"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        What actions were taken immediately after the incident occurred?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Root Cause and OSHA Reportable */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  {/* Root Cause */}
                  <FormField
                    control={form.control}
                    name="rootCause"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Root Cause</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || undefined}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select root cause" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(rootCauseEnum.enumValues).map((cause) => (
                              <SelectItem key={cause} value={cause}>
                                {cause
                                  .split('_')
                                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                  .join(' ')}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>What was the underlying cause of the incident?</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* OSHA Reportable */}
                  <FormField
                    control={form.control}
                    name="oshaReportable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">OSHA Reportable</FormLabel>
                          <FormDescription>Requires OSHA Form 300 and reporting to authorities.</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value || false} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Conditional input for "other" root cause */}
                {form.watch('rootCause') === 'other' && (
                  <FormField
                    control={form.control}
                    name="otherRootCause"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Specify other root cause</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Please specify the root cause"
                            {...field}
                            required
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Media section */}
                <div className="w-full">
                  <h3 className="text-base font-medium mb-2">Media Files</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Current media files are displayed below. You can add new files here.
                  </p>

                  <MediaUpload
                    className="w-full"
                    files={form.watch('media') ?? []}
                    setFiles={(tFiles, files) => {
                      form.setValue('media', tFiles);
                      setMediaFiles(files);
                    }}
                  />
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incidentId))}
                    className="px-4 font-medium text-primary-700 border-primary-700 hover:bg-primary-300"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="px-5">
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </div>
  );
}
