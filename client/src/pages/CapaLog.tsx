import { CapaFilters } from '@/components/capas/list/capa-filters';
import { CapaMobileFilters } from '@/components/capas/list/capa-mobile-filters';
import { CapaMobileView } from '@/components/capas/list/capa-mobile-view';
import { CapaTable } from '@/components/capas/list/capa-table';
import { IncidentsLoading } from '@/components/incidents/list/incidents-loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { useIsMobile } from '@/hooks/use-mobile';
import { trpc } from '@/providers/trpc';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import { type DateRange } from 'react-day-picker';
import { useLocation } from 'wouter';

export default function CapaLog() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();

  const [searchTerm, setSearchTerm] = useState<string>('');

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const utils = trpc.useUtils();

  const [filters, setFilters] = useState({
    status: [] as (typeof statusEnum.enumValues)[number][],
    type: [] as (typeof capaTypeEnum.enumValues)[number][],
    priority: [] as (typeof capaPriorityEnum.enumValues)[number][],
    owner: [] as string[],
    includeArchived: false,
    dateRange: undefined as DateRange | undefined,
    tags: [] as (typeof capaTagsEnum.enumValues)[number][],
  });

  // Fetch capas data
  const {
    data: capas,
    isLoading,
    error,
  } = trpc.capa.list.useQuery({
    page: 1,
    limit: 100,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    search: debouncedSearchTerm,
    status: filters.status,
    type: filters.type,
    priority: filters.priority,
    owner: filters.owner,
    includeArchived: filters.includeArchived,
    dueDateStart: filters.dateRange?.from,
    dueDateEnd: filters.dateRange?.to,
    tags: filters.tags,
  });

  const { mutateAsync: updateCapa } = trpc.capa.update.useMutation({
    onSuccess: () => {
      utils.capa.list.invalidate();
    },
  });

  const { data: users } = trpc.user.getUsers.useQuery({ search: '' });

  // Handle adding or removing a filter
  const toggleFilter = (type: 'status' | 'type' | 'priority' | 'owner' | 'tags', value: any) => {
    setFilters((prev) => {
      const currentFilters = [...prev[type]];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      return { ...prev, [type]: currentFilters };
    });
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      status: [],
      type: [],
      priority: [],
      owner: [],
      includeArchived: false,
      dateRange: undefined,
      tags: [],
    });
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      filters.status.length +
      filters.type.length +
      filters.priority.length +
      filters.owner.length +
      (filters.includeArchived ? 1 : 0) +
      (filters.dateRange?.from || filters.dateRange?.to ? 1 : 0)
    );
  }, [filters]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">CAPA Tracker</h1>
          <p className="text-muted-foreground">Track and manage all corrective and preventive actions</p>
        </div>

        <div className="flex items-center w-full md:w-auto gap-4">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search CAPAs..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <Button onClick={() => navigate(ROUTES.CAPA_NEW)} className="whitespace-nowrap">
            + Create CAPA
          </Button>
        </div>
      </div>

      {/* Mobile filters */}
      <CapaMobileFilters
        filters={filters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        users={users || []}
        setFilters={setFilters}
      />

      {/* Desktop Dropdown filters section */}
      <CapaFilters
        filters={filters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        capas={capas?.data || []}
        users={users || []}
        setFilters={setFilters}
      />

      {/* Loading state */}
      {isLoading ? (
        <IncidentsLoading />
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-md text-red-800 my-4">
          <h3 className="font-medium">Error loading CAPAs</h3>
          <p>Unable to load data. Please try refreshing the page.</p>
        </div>
      ) : (
        <>
          {isMobile ? (
            <CapaMobileView
              capas={capas}
              activeFilterCount={activeFilterCount}
              resetFilters={resetFilters}
              updateCapa={updateCapa}
            />
          ) : (
            <CapaTable
              capas={capas}
              activeFilterCount={activeFilterCount}
              resetFilters={resetFilters}
              navigate={navigate}
              updateCapa={updateCapa}
            />
          )}
        </>
      )}
    </div>
  );
}
