// @ts-nocheck

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import confetti from 'canvas-confetti';
import { format, isAfter, isBefore, differenceInDays, parseISO } from 'date-fns';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  ExternalLink,
  FileText,
  Filter,
  Zap,
  PanelRight,
  Pencil,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Tag,
  TrendingUp,
  Wrench,
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { REPORT_TYPE, type Incident, type Capa } from '@shared/schema';
import { useIsMobile } from '@/hooks/use-mobile';

// Types for chart data
interface ChartData {
  name: string;
  value: number;
  color?: string;
}

// Component for colorful stat cards
const StatCard = ({
  title,
  value,
  icon,
  trend,
  trendLabel,
  color = 'primary',
}: {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  trendLabel?: string;
  color?: 'primary' | 'green' | 'amber' | 'red' | 'indigo' | 'purple';
}) => {
  const colorClasses = {
    primary: 'bg-primary-50 text-primary-700 border-primary-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    amber: 'bg-amber-50 text-amber-700 border-amber-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    indigo: 'bg-indigo-50 text-indigo-700 border-indigo-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
  };

  return (
    <div className={`rounded-lg border p-4 ${colorClasses[color]}`}>
      <div className="flex justify-between">
        <div>
          <p className="text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {trend !== undefined && (
            <div className="flex items-center mt-1 text-xs">
              <span className={trend > 0 ? 'text-green-600' : 'text-red-600'}>
                {trend > 0 ? '↑' : '↓'} {Math.abs(trend)}%
              </span>
              <span className="ml-1 text-gray-600">{trendLabel}</span>
            </div>
          )}
        </div>
        <div className="h-10 w-10 rounded-full flex items-center justify-center">{icon}</div>
      </div>
    </div>
  );
};

// Function to get time-based greeting with safety manager's name
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours();
  let greeting = '';
  let emoji = '';

  // Determine greeting and emoji based on time of day
  if (hour < 12) {
    greeting = 'Good morning';
    emoji = '☀️';
  } else if (hour < 17) {
    greeting = 'Good afternoon';
    emoji = '⭐';
  } else if (hour < 22) {
    greeting = 'Good evening';
    emoji = '🌙';
  } else {
    greeting = 'Good night';
    emoji = '✨';
  }

  // Add a sparkle and "Safety Manager" for personalization (in a real app, this would be the user's name)
  return (
    <span className="flex items-center">
      <span className="sparkle-glow mr-1">{emoji}</span>
      <span>{greeting}, Safety Manager!</span>
    </span>
  );
};

export default function Dashboard() {
  const [isMobile] = useState(() => window.innerWidth < 768);
  const { toast } = useToast();
  const [coachMood, setCoachMood] = useState<'normal' | 'happy' | 'concerned' | 'busy'>('normal');
  const [confettiTriggered, setConfettiTriggered] = useState(false);

  // Fetch incident data
  const { data: incidents, isLoading: loadingIncidents } = useQuery<Incident[]>({
    queryKey: ['/api/incidents'],
    retry: 1,
  });

  // Fetch CAPA data
  const { data: capas, isLoading: loadingCapas } = useQuery<Capa[]>({
    queryKey: ['/api/capas'],
    retry: 1,
  });

  // Update coach mood based on data
  useEffect(() => {
    if (incidents && capas) {
      // Count incidents in last 7 days
      const recentIncidentCount = incidents.filter((incident) => {
        const createdDate = new Date(incident.createdAt || Date.now());
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return createdDate >= sevenDaysAgo;
      }).length;

      // Count overdue CAPAs
      const overdueCapaCount = capas.filter((capa) => {
        if (!capa.dueDate) return false;
        const dueDate = new Date(capa.dueDate);
        return isAfter(new Date(), dueDate) && capa.status !== 'Closed';
      }).length;

      // Set mood based on conditions
      if (overdueCapaCount > 3) {
        setCoachMood('concerned');
      } else if (recentIncidentCount > 5) {
        setCoachMood('busy');
      } else if (recentIncidentCount === 0 && overdueCapaCount === 0) {
        setCoachMood('happy');

        // Trigger confetti for great performance
        if (!confettiTriggered) {
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
          });
          setConfettiTriggered(true);
        }
      } else {
        setCoachMood('normal');
      }
    }
  }, [incidents, capas, confettiTriggered]);

  // Analysis helper functions
  const getIncidentsByType = () => {
    if (!incidents) return [];

    const counts: Record<string, number> = {};
    incidents.forEach((incident) => {
      const type = incident.reportType;
      counts[type] = (counts[type] || 0) + 1;
    });

    return Object.entries(counts).map(([name, value]) => ({
      name: name === 'near_miss' ? 'Near Miss' : 'Incident',
      value,
      color: name === 'near_miss' ? '#FDBA74' : '#F87171',
    }));
  };

  const getHazardCategoryBreakdown = () => {
    if (!incidents) return [];

    const counts: Record<string, number> = {};
    incidents.forEach((incident) => {
      const category = incident.hazardCategory || 'Uncategorized';
      counts[category] = (counts[category] || 0) + 1;
    });

    return Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5); // Top 5 categories
  };

  const getCapaStatusCounts = () => {
    if (!capas) return { open: 0, inProgress: 0, closed: 0 };

    return {
      open: capas.filter((capa) => capa.status === 'Open').length,
      inProgress: capas.filter((capa) => capa.status === 'In Review').length,
      closed: capas.filter((capa) => capa.status === 'Closed').length,
    };
  };

  const getOverdueCapas = () => {
    if (!capas) return [];

    return capas
      .filter((capa) => {
        if (!capa.dueDate || capa.status === 'Closed') return false;
        return isAfter(new Date(), new Date(capa.dueDate));
      })
      .sort((a, b) => {
        if (!a.dueDate || !b.dueDate) return 0;
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      })
      .slice(0, 3); // Top 3 most overdue
  };

  const getRecurringRootCauses = () => {
    if (!incidents) return [];

    const counts: Record<string, number> = {};
    incidents.forEach((incident) => {
      const rootCause = incident.rootCause || 'Uncategorized';
      counts[rootCause] = (counts[rootCause] || 0) + 1;
    });

    return Object.entries(counts)
      .map(([name, value]) => ({
        name:
          name === 'human_error'
            ? 'Human Error'
            : name === 'equipment_failure'
              ? 'Equipment Failure'
              : name === 'environmental'
                ? 'Environmental'
                : name === 'procedural'
                  ? 'Procedural'
                  : 'Other',
        value,
      }))
      .sort((a, b) => b.value - a.value);
  };

  const getIncidentLocations = () => {
    if (!incidents) return [];

    const counts: Record<string, number> = {};
    incidents.forEach((incident) => {
      const location = incident.location || 'Unknown';
      counts[location] = (counts[location] || 0) + 1;
    });

    return Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 3); // Top 3 locations
  };

  const getAssetInsights = () => {
    if (!incidents) return [];

    const assetCounts: Record<string, number> = {};

    // Count mentions of assets in incident titles, descriptions, and linkedAssets
    incidents.forEach((incident) => {
      // Extract potential asset names from incident data
      const assetMentions: string[] = [];

      // Check linkedAssets if it exists and is an array
      if (incident.linkedAssets && Array.isArray(incident.linkedAssets)) {
        assetMentions.push(...incident.linkedAssets);
      }

      // Check title for asset mentions (simple approach - look for common patterns)
      const title = incident.title || '';
      const titleMatches = title.match(/Pump \d+|Line \d+|Machine \d+|Equipment \d+/g);
      if (titleMatches) assetMentions.push(...titleMatches);

      // Check description for asset mentions
      const description = incident.description || '';
      const descMatches = description.match(/Pump \d+|Line \d+|Machine \d+|Equipment \d+/g);
      if (descMatches) assetMentions.push(...descMatches);

      // Count each unique asset
      const uniqueAssets = Array.from(new Set(assetMentions));
      uniqueAssets.forEach((asset) => {
        assetCounts[asset] = (assetCounts[asset] || 0) + 1;
      });
    });

    return Object.entries(assetCounts)
      .filter(([_, count]) => count > 1) // Only assets with multiple incidents
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 3); // Top 3 problematic assets
  };

  const getRecentInjuryStreak = () => {
    if (!incidents) return 0;

    // Find most recent injury incident
    const injuryIncidents = incidents.filter(
      (incident) => incident.reportType === 'incident' && incident.severityLevel === 'high',
    );

    if (injuryIncidents.length === 0) return 30; // Default if no injuries found

    // Sort by date descending
    injuryIncidents.sort((a, b) => {
      const dateA = new Date(a.createdAt || a.dateTime || Date.now());
      const dateB = new Date(b.createdAt || b.dateTime || Date.now());
      return dateB.getTime() - dateA.getTime();
    });

    // Calculate days since most recent injury
    const mostRecentDate = new Date(injuryIncidents[0].createdAt || injuryIncidents[0].dateTime || Date.now());
    const daysSince = differenceInDays(new Date(), mostRecentDate);

    return daysSince;
  };

  const getOshaCount = () => {
    if (!incidents) return 0;
    return incidents.filter((incident) => incident.oshaReportable).length;
  };

  // Generate safety coach briefing with enhanced coaching tone and emojis
  const generateCoachBriefing = () => {
    if (!incidents || !capas) {
      return {
        urgentRisks: '⏳ Loading your safety data, standby...',
        overdueCapas: '',
        patterns: '',
        oshaStatus: '',
        streaks: '',
        suggestedAction: '',
      };
    }

    // Recent incidents (last 7 days)
    const recentIncidents = incidents.filter((incident) => {
      const createdDate = new Date(incident.createdAt || incident.dateTime || Date.now());
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return createdDate >= sevenDaysAgo;
    });

    // Count locations for recent incidents
    const locationCounts: Record<string, number> = {};
    recentIncidents.forEach((incident) => {
      const location = incident.location || 'Unspecified';
      locationCounts[location] = (locationCounts[location] || 0) + 1;
    });

    // Get most frequent location
    const topLocations = Object.entries(locationCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 1);

    const topLocation = topLocations.length > 0 ? topLocations[0][0] : null;

    // Urgent risks section
    let urgentRisks = '';
    if (recentIncidents.length === 0) {
      urgentRisks =
        "🌟 No new incidents reported this week! Fantastic job keeping the team safe. Let's keep this momentum going!";
    } else {
      // Count incident types
      const hazardCounts: Record<string, number> = {};
      recentIncidents.forEach((incident) => {
        const category = incident.hazardCategory || 'Uncategorized';
        hazardCounts[category] = (hazardCounts[category] || 0) + 1;
      });

      // Get top hazard
      const topHazards = Object.entries(hazardCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 2);

      if (topHazards.length > 0) {
        const hazardTrend = topHazards[0][1] > 2 ? '⚠️ Alert! ' : '👀 Heads up! ';
        urgentRisks = `${hazardTrend}${recentIncidents.length} new incident${recentIncidents.length > 1 ? 's' : ''} this week — primarily ${topHazards.map(([hazard, count]) => `${count} ${hazard.toLowerCase()}`).join(' and ')}${topLocation ? ` with ${topLocations[0][1]} incident${topLocations[0][1] > 1 ? 's' : ''} near ${topLocation}` : ''}.`;
      }
    }

    // Overdue CAPAs section
    const overdueCapaList = getOverdueCapas();
    let overdueCapas = '';
    if (overdueCapaList.length === 0) {
      overdueCapas = '✅ All CAPAs are on track! Your team is keeping pace with follow-up actions. Outstanding work!';
    } else {
      const mostOverdue = overdueCapaList[0];
      const daysOverdue = differenceInDays(new Date(), new Date(mostOverdue.dueDate || Date.now()));

      const urgencyLevel = daysOverdue > 7 ? '🚨 Action needed! ' : '⏰ Quick follow-up: ';
      overdueCapas = `${urgencyLevel}${overdueCapaList.length} CAPA${overdueCapaList.length > 1 ? 's' : ''} ${overdueCapaList.length > 1 ? 'are' : 'is'} overdue — oldest one assigned to ${mostOverdue.ownerName || 'unassigned'} is ${daysOverdue} days past due. Let's help them clear this backlog.`;
    }

    // Patterns section
    const assetInsights = getAssetInsights();
    let patterns = '';
    if (assetInsights.length > 0) {
      const topAsset = assetInsights[0];
      patterns = `🔄 Pattern detected! ${topAsset.name} has been linked to ${topAsset.value} incidents recently. I strongly recommend scheduling a thorough equipment review ASAP.`;
    } else {
      patterns =
        '🔍 No recurring patterns detected in recent incidents. Your preventive measures seem to be working effectively!';
    }

    // OSHA status
    const oshaCount = getOshaCount();
    let oshaStatus = '';
    if (oshaCount > 0) {
      oshaStatus = `📋 ${oshaCount} incident${oshaCount > 1 ? 's' : ''} marked as OSHA-reportable this year. Your Form 300 log is 80% complete — just a few more details needed before the quarterly deadline.`;
    } else {
      oshaStatus =
        '📋 No OSHA-reportable incidents this year! Your compliance logs are up to date and ready for any potential inspection.';
    }

    // Safety streaks
    const injuryFreeDays = getRecentInjuryStreak();
    let streaks = '';
    if (injuryFreeDays > 30) {
      streaks = `🏆 Incredible achievement! ${injuryFreeDays} days without recordable injuries! This deserves celebration — perhaps recognize the team's commitment at your next all-hands?`;
    } else if (injuryFreeDays > 14) {
      streaks = `🎯 Strong performance! ${injuryFreeDays} days injury-free and counting. The team is building solid momentum on safety practices.`;
    } else if (injuryFreeDays > 0) {
      streaks = `🔼 ${injuryFreeDays} days without a recordable injury. Let's keep building this streak by reinforcing daily safety checks.`;
    } else {
      streaks =
        "🔄 Safety is our priority every day. Let's restart our streak with renewed focus on preventive measures.";
    }

    // Suggested action
    let suggestedAction = '';
    if (overdueCapaList.length > 3) {
      suggestedAction =
        "💪 Tackle those overdue CAPAs! Hold a quick 30-minute 'CAPA sprint' with key team members to clear at least 3 items this week.";
    } else if (overdueCapaList.length > 0) {
      suggestedAction =
        '🎯 Follow up with CAPA owners today. A quick check-in can help remove blockers and get action items back on track.';
    } else if (assetInsights.length > 0) {
      suggestedAction = `🔍 Time for a targeted safety audit! Schedule a 45-minute walkthrough focused on ${assetInsights[0].name} with maintenance and operations leads.`;
    } else if (recentIncidents.length === 0 && injuryFreeDays > 30) {
      suggestedAction =
        "🚀 While things are calm, this is the perfect time to level up your team's safety training. Consider refreshing your hazard identification modules.";
    } else if (recentIncidents.length > 0) {
      suggestedAction =
        "📊 Create a quick 5-minute safety briefing for tomorrow's huddle highlighting recent incidents and prevention strategies.";
    } else {
      suggestedAction = '📝 Great time to review and update your safety procedures while things are running smoothly!';
    }

    return {
      urgentRisks,
      overdueCapas,
      patterns,
      oshaStatus,
      streaks,
      suggestedAction,
    };
  };

  const briefing = generateCoachBriefing();
  const capaStatusCounts = getCapaStatusCounts();
  const typeData = getIncidentsByType();
  const hazardData = getHazardCategoryBreakdown();
  const locationData = getIncidentLocations();
  const rootCauseData = getRecurringRootCauses();
  const assetInsights = getAssetInsights();
  const overdueCapas = getOverdueCapas();

  if (loadingIncidents || loadingCapas) {
    return (
      <div className="container py-10">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">Safety Dashboard</h1>
          <p>Loading your safety data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8 bg-neutral-50">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-neutral-800">Safety Dashboard</h1>
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" className="hidden md:flex">
              <Calendar className="h-4 w-4 mr-2" />
              Today
            </Button>
            <Button size="sm" variant="outline" className="hidden md:flex">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm" className="bg-primary-500 hover:bg-primary-600">
              <Zap className="h-4 w-4 mr-2" />
              Quick Actions
            </Button>
          </div>
        </div>

        {/* AI Safety Coach Briefing Card - Enhanced with magical blue theme */}
        <Card className="mb-8 relative overflow-hidden border-0 shadow-lg">
          {/* Magical gradient background */}
          <div className="absolute inset-0 bg-linear-to-br from-blue-400/90 via-blue-500/90 to-blue-700/90"></div>

          {/* Sparkle elements - Positioned absolutely with custom animations */}
          <div className="absolute top-6 right-12 w-8 h-8 text-yellow-300 sparkle-glow">✨</div>
          <div
            className="absolute bottom-12 left-10 w-7 h-7 text-yellow-300 sparkle-float"
            style={{ animationDelay: '0.5s' }}
          >
            ✨
          </div>
          <div
            className="absolute top-1/2 right-1/4 w-6 h-6 text-yellow-300 sparkle-glow"
            style={{ animationDelay: '1.2s' }}
          >
            ✨
          </div>
          <div
            className="absolute top-20 left-1/4 w-5 h-5 text-yellow-200 sparkle-float"
            style={{ animationDelay: '2s' }}
          >
            ✨
          </div>
          <div
            className="absolute bottom-1/3 right-1/5 w-4 h-4 text-yellow-100 sparkle-glow"
            style={{ animationDelay: '3s' }}
          >
            ✨
          </div>

          <CardHeader className="pb-4 relative z-10">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold text-white flex items-center">
                  <span className="mr-2 text-2xl">✨</span> AI Safety Coach
                </CardTitle>
                <CardDescription className="text-blue-100 mt-1 font-medium">
                  {format(new Date(), 'EEEE, MMMM d, yyyy')}
                </CardDescription>
              </div>

              {/* Replace avatar with personalized greeting */}
              <div className="bg-white/20 backdrop-blur-xs px-4 py-2 rounded-full text-white font-semibold shadow-inner">
                {getTimeBasedGreeting()}
              </div>
            </div>
          </CardHeader>

          <CardContent className="pb-6 relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-5">
                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">⚡</span>Urgent or Emerging Risks
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.urgentRisks}</p>
                </div>

                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">⏰</span>Overdue CAPAs
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.overdueCapas}</p>
                </div>

                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">🔄</span>Patterns or Asset Clusters
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.patterns}</p>
                </div>
              </div>

              <div className="space-y-5">
                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">📋</span>OSHA Status
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.oshaStatus}</p>
                </div>

                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">🏆</span>Safety Performance
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.streaks}</p>
                </div>

                <div className="bg-blue-600/20 rounded-lg p-4 backdrop-blur-xs">
                  <h3 className="font-semibold text-blue-100 mb-2 flex items-center">
                    <span className="mr-2">✨</span>Suggested Next Action
                  </h3>
                  <p className="text-white text-sm leading-relaxed">{briefing.suggestedAction}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dashboard Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {/* Incident Overview Card */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Incident Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3 mb-4">
                <StatCard
                  title="This Month"
                  value={incidents?.length || 0}
                  icon={<AlertCircle className="h-6 w-6 text-primary-500" />}
                  trend={-15}
                  trendLabel="vs last month"
                />
                <StatCard
                  title="Open Cases"
                  value={incidents?.filter((i) => i.status === 'Open').length || 0}
                  icon={<Clock className="h-6 w-6 text-amber-500" />}
                  color="amber"
                />
              </div>

              <h3 className="text-sm font-medium text-neutral-600 mb-2">Incident Type Breakdown</h3>
              <div className="h-[180px] mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={typeData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      label={(entry) => entry.name}
                    >
                      {typeData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.color || `#${(((1 << 24) * Math.random()) | 0).toString(16)}`}
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <Link
                href="/incidents"
                className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
              >
                View Incident Log <ExternalLink className="h-3 w-3 ml-1" />
              </Link>
            </CardContent>
          </Card>

          {/* CAPA Tracker Card */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">CAPA Tracker</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-3 mb-4">
                <div className="bg-green-50 text-green-800 rounded-lg p-2 text-center">
                  <div className="text-xs font-medium">Closed</div>
                  <div className="text-xl font-bold">{capaStatusCounts.closed}</div>
                </div>
                <div className="bg-amber-50 text-amber-800 rounded-lg p-2 text-center">
                  <div className="text-xs font-medium">In Review</div>
                  <div className="text-xl font-bold">{capaStatusCounts.inProgress}</div>
                </div>
                <div className="bg-primary-50 text-primary-800 rounded-lg p-2 text-center">
                  <div className="text-xs font-medium">Open</div>
                  <div className="text-xl font-bold">{capaStatusCounts.open}</div>
                </div>
              </div>

              <h3 className="text-sm font-medium text-neutral-600 mb-2">Overdue CAPAs</h3>
              {overdueCapas.length > 0 ? (
                <div className="space-y-2 mb-4">
                  {overdueCapas.map((capa) => {
                    const daysOverdue = capa.dueDate ? differenceInDays(new Date(), new Date(capa.dueDate)) : 0;

                    return (
                      <div key={capa.id} className="flex justify-between items-center p-2 bg-red-50 rounded-md text-sm">
                        <div>
                          <div className="font-medium text-neutral-800">{capa.title}</div>
                          <div className="text-xs text-neutral-600">Owner: {capa.ownerName || 'Unassigned'}</div>
                        </div>
                        <Badge variant="destructive" className="ml-2 whitespace-nowrap">
                          {daysOverdue} days overdue
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[120px] mb-4 bg-green-50 rounded-lg">
                  <div className="text-center">
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">No overdue CAPAs!</p>
                  </div>
                </div>
              )}

              <Link
                href="/capas"
                className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
              >
                View CAPA Log <ExternalLink className="h-3 w-3 ml-1" />
              </Link>
            </CardContent>
          </Card>

          {/* Tag & Pattern Intelligence Card */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Pattern Intelligence</CardTitle>
            </CardHeader>
            <CardContent>
              <h3 className="text-sm font-medium text-neutral-600 mb-2">Recurring Root Causes</h3>
              <div className="h-[130px] mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={rootCauseData} layout="vertical">
                    <XAxis type="number" hide />
                    <YAxis type="category" dataKey="name" width={100} />
                    <Tooltip />
                    <Bar dataKey="value" fill="#3B82F6" radius={[0, 4, 4, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <h3 className="text-sm font-medium text-neutral-600 mb-2">Asset-Level Insights</h3>
              {assetInsights.length > 0 ? (
                <div className="space-y-2 mb-4">
                  {assetInsights.map((asset, idx) => (
                    <div key={idx} className="flex justify-between items-center p-2 bg-amber-50 rounded-md text-sm">
                      <div className="font-medium text-neutral-800">{asset.name}</div>
                      <Badge variant="outline" className="bg-amber-100 border-amber-200 text-amber-800">
                        {asset.value} incidents
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="mb-4 p-3 bg-neutral-50 rounded-lg text-sm text-neutral-600">
                  No recurring asset-related incidents detected
                </div>
              )}

              <h3 className="text-sm font-medium text-neutral-600 mb-2">Incident Hotspots</h3>
              <div className="space-y-2">
                {locationData.map((location, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm">{location.name}</span>
                    <div className="flex-1 mx-2">
                      <Progress
                        value={(location.value / (Math.max(...locationData.map((l) => l.value)) || 1)) * 100}
                        className="h-2"
                      />
                    </div>
                    <span className="text-sm font-medium">{location.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Compliance & Reporting Card - Full Width*/}
        <Card className="mb-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Compliance & Reporting</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <StatCard
                title="OSHA Reportable"
                value={getOshaCount()}
                icon={<FileText className="h-6 w-6 text-indigo-500" />}
                color="indigo"
              />

              <div className="flex flex-col justify-between p-4 bg-neutral-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Audit Readiness</p>
                  <Progress value={85} className="h-2 mt-2" />
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-neutral-500">Last updated 2 days ago</span>
                  <span className="text-sm font-medium text-green-600">85%</span>
                </div>
              </div>

              <div className="flex flex-col justify-between p-4 bg-neutral-50 rounded-lg">
                <p className="text-sm font-medium text-neutral-600">Form 300 Log Status</p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-neutral-500">5 incidents pending</span>
                  <Badge className="bg-amber-100 text-amber-800 border-amber-200">In Progress</Badge>
                </div>
              </div>

              <div className="flex items-center justify-center p-4 bg-neutral-50 rounded-lg">
                <Button className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Compliance Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
