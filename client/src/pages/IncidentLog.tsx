import { Filters } from '@/components/incidents/list/incidents-filters';
import { IncidentsEmpty } from '@/components/incidents/list/incidents-empty';
import { IncidentsLoading } from '@/components/incidents/list/incidents-loading';
import { MobileFilters } from '@/components/incidents/list/incidents-mobile-filters';
import { IncidentsMobileView } from '@/components/incidents/list/incidents-mobile-view';
import { IncidentsTable } from '@/components/incidents/list/incidents-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useIsMobile } from '@/hooks/use-mobile';
import { trpc } from '@/providers/trpc';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X, XCircle } from 'lucide-react';
import { useMemo, useState } from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { Redirect, useLocation } from 'wouter';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';

export default function IncidentLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const [filters, setFilters] = useState({
    status: [] as (typeof statusEnum.enumValues)[number][],
    type: [] as (typeof reportTypeEnum.enumValues)[number][],
    severity: [] as (typeof severityEnum.enumValues)[number][],
    oshaReportable: null as boolean | null,
    includeArchived: false,
    locationIds: [] as string[],
  });

  const {
    data: incidents,
    isLoading,
    error,
  } = trpc.incident.list.useQuery({
    page: 1,
    limit: 100,
    search: debouncedSearchTerm || undefined,
    archived: filters.includeArchived || undefined,
    sortBy: 'reportedAt',
    sortOrder: 'desc',
    status: filters.status,
    type: filters.type,
    severity: filters.severity,
    oshaReportable: filters.oshaReportable || undefined,
    locationIds: filters.locationIds,
  });

  const { mutateAsync: updateIncident, error: updateIncidentError } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.list.invalidate();
    },
  });

  const { data: locations } = trpc.location.search.useQuery({
    search: '',
    limit: 100,
  });

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      filters.status.length +
      filters.type.length +
      filters.severity.length +
      (filters.oshaReportable !== null ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearchTerm.trim() ? 1 : 0) +
      filters.locationIds.length
    );
  }, [filters, debouncedSearchTerm]);

  if (!hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)) {
    return <Redirect to={ROUTES.INCIDENT_LIST} />;
  }

  const toggleFilter = (type: 'status' | 'type' | 'severity' | 'locationIds', value: any) => {
    setFilters((prev) => {
      const currentFilters = [...prev[type]];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      return { ...prev, [type]: currentFilters };
    });
  };

  // Toggle OSHA reportable filter
  const toggleOshaFilter = () => {
    setFilters((prev) => ({
      ...prev,
      oshaReportable: prev.oshaReportable === null ? true : prev.oshaReportable === true ? false : null,
    }));
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      status: [],
      type: [],
      severity: [],
      oshaReportable: null,
      includeArchived: false,
      locationIds: [],
    });
    setSearchTerm('');
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Incident Log</h1>
        <div className="flex items-center w-full md:w-auto">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search incidents..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="ml-4">
            <Button onClick={() => navigate(ROUTES.INCIDENT_NEW)}>+ Create Incident</Button>
          </div>
          <MobileFilters
            toggleFilter={toggleFilter}
            filters={filters}
            toggleOshaFilter={toggleOshaFilter}
            setFilters={setFilters}
            activeFilterCount={activeFilterCount}
            resetFilters={resetFilters}
            locations={locations?.data}
          />
        </div>
      </div>

      {/* Filters bar */}
      <Filters
        toggleFilter={toggleFilter}
        filters={filters}
        toggleOshaFilter={toggleOshaFilter}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        locations={locations?.data}
      />

      {/* Error state */}
      {error ? (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading incidents</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>Please refresh the page or try again later.</p>
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {/* Loading state */}
      {isLoading ? <IncidentsLoading /> : null}

      {/* Desktop Table View */}
      {!isMobile && incidents?.data && incidents.data.length > 0 ? (
        <IncidentsTable incidents={incidents} activeFilterCount={activeFilterCount} resetFilters={resetFilters} />
      ) : null}

      {/* Mobile Card View */}
      {isMobile && incidents?.data && incidents.data.length > 0 ? (
        <IncidentsMobileView incidents={incidents} activeFilterCount={activeFilterCount} resetFilters={resetFilters} />
      ) : null}

      {/* Empty state - when no incidents are found and not loading */}
      {!isLoading && !error && incidents?.data && incidents.data.length === 0 ? (
        <IncidentsEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={resetFilters} />
      ) : null}
    </div>
  );
}
