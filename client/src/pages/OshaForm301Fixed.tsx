// @ts-nocheck

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { format } from "date-fns";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";

import { 
  ArrowLeft, 
  Calendar, 
  CheckCircle, 
  Clock, 
  Download, 
  HelpCircle, 
  Info, 
  MapPin, 
  PenLine,
  Save,
  Send 
} from "lucide-react";

// OSHA Form 301 schema
const oshaForm301Schema = z.object({
  // Section 1 - Employee Info
  employeeName: z.string().min(1, "Employee name is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  
  // Section 2 - Incident Details (auto-filled where possible)
  dateOfInjury: z.string().min(1, "Date is required"),
  timeOfEvent: z.string().min(1, "Time is required"), 
  location: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  
  // Section 3 - Nature of Injury
  bodyPartAffected: z.string().min(1, "Body part is required"),
  injuryType: z.string().min(1, "Injury type is required"),
  treatmentLocation: z.string().optional(),
  
  // Section 4 - OSHA Questions
  wasHospitalized: z.boolean().default(false),
  resultedInDeath: z.boolean().default(false),
  daysAwayFromWork: z.string().optional(),
  daysOfRestrictedDuty: z.string().optional(),
});

type OshaForm301Values = z.infer<typeof oshaForm301Schema>;

export default function OshaForm301() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const [aiSuggestions, setAiSuggestions] = useState<{
    bodyPartAffected: string;
    injuryType: string;
  }>({
    bodyPartAffected: "",
    injuryType: ""
  });
  
  const [progress, setProgress] = useState(0);
  const [completionStatus, setCompletionStatus] = useState<'not-started' | 'in-progress' | 'complete'>('not-started');
  
  // Extract parameters from URL
  const params = new URLSearchParams(window.location.search);
  const incidentId = params.get('incidentId');
  const viewMode = params.get('mode') === 'view';
  const editMode = params.get('mode') === 'edit';
  
  // Query incident data
  const { data: incident, isLoading, error } = useQuery({
    queryKey: [`/api/incidents/${incidentId}`],
    queryFn: ({ queryKey }) => 
      fetch(queryKey[0].toString()).then(res => res.json()),
    enabled: !!incidentId
  });

  const form = useForm<OshaForm301Values>({
    resolver: zodResolver(oshaForm301Schema),
    defaultValues: {
      employeeName: "",
      jobTitle: "",
      dateOfInjury: "",
      timeOfEvent: "",
      location: "",
      description: "",
      bodyPartAffected: "",
      injuryType: "",
      treatmentLocation: "",
      wasHospitalized: false,
      resultedInDeath: false,
      daysAwayFromWork: "",
      daysOfRestrictedDuty: "",
    },
  });
  
  // Auto-fill form with incident data when loaded
  useEffect(() => {
    if (incident) {
      // Extract date and time from incident.dateTime
      const date = new Date(incident.dateTime);
      const dateStr = format(date, "yyyy-MM-dd");
      const timeStr = format(date, "HH:mm");
      
      form.reset({
        ...form.getValues(),
        dateOfInjury: dateStr,
        timeOfEvent: timeStr,
        location: incident.location || "",
        description: incident.description || "",
        
        // AI suggestions based on description
        bodyPartAffected: aiSuggestions.bodyPartAffected || extractBodyPart(incident.description || ""),
        injuryType: aiSuggestions.injuryType || extractInjuryType(incident.description || ""),
        
        // OSHA flags
        wasHospitalized: incident.oshaReportable || false,
      });
      
      // Calculate form completion progress
      updateProgress();
    }
  }, [incident, aiSuggestions]);
  
  // Simple extraction functions (these would ideally be replaced with AI suggestions)
  function extractBodyPart(description: string): string {
    const bodyParts = ["head", "neck", "arm", "hand", "finger", "leg", "foot", "back", "chest", "eye"];
    for (const part of bodyParts) {
      if (description.toLowerCase().includes(part)) {
        return part.charAt(0).toUpperCase() + part.slice(1);
      }
    }
    return "";
  }
  
  function extractInjuryType(description: string): string {
    const injuryTypes = ["cut", "burn", "fracture", "sprain", "bruise", "contusion", "laceration"];
    for (const type of injuryTypes) {
      if (description.toLowerCase().includes(type)) {
        return type.charAt(0).toUpperCase() + type.slice(1);
      }
    }
    return "";
  }
  
  function updateProgress() {
    const formValues = form.getValues();
    const requiredFields = [
      "employeeName", "jobTitle", "dateOfInjury", "timeOfEvent", 
      "description", "bodyPartAffected", "injuryType"
    ];
    
    const filledFields = requiredFields.filter(field => 
      formValues[field as keyof OshaForm301Values]
    ).length;
    
    const calculatedProgress = Math.round((filledFields / requiredFields.length) * 100);
    setProgress(calculatedProgress);
    
    if (calculatedProgress === 0) {
      setCompletionStatus('not-started');
    } else if (calculatedProgress < 100) {
      setCompletionStatus('in-progress');
    } else {
      setCompletionStatus('complete');
    }
  }
  
  function onSubmit(data: OshaForm301Values) {
    console.log("OSHA Form Data:", data);
    
    // Simulate form submission
    toast({
      title: "OSHA Form 301 Submitted",
      description: "The OSHA log has been recorded successfully.",
      duration: 5000,
    });
    
    // Navigate back to the incident detail page
    if (incidentId) {
      setLocation(`/incidents/${incidentId}`);
    } else {
      setLocation("/incidents");
    }
  }
  
  function handleSaveDraft() {
    // Save the current form data as a draft
    toast({
      title: "Draft Saved",
      description: "Your OSHA form data has been saved as a draft.",
      duration: 3000,
    });
  }
  
  function handleExportPdf() {
    // In a real app, this would generate and download a PDF
    toast({
      title: "PDF Export",
      description: "OSHA Form 301 PDF would be generated here.",
      duration: 3000,
    });
  }
  
  if (isLoading) {
    return (
      <div className="container max-w-3xl py-10">
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => window.history.back()} 
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Loading OSHA Form 301...</h1>
        </div>
      </div>
    );
  }
  
  if (error || !incidentId) {
    return (
      <div className="container max-w-3xl py-10">
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => window.history.back()} 
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-red-600">Error Loading OSHA Form</h1>
          <p className="mt-2">Unable to load incident data. Please try again.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="pb-20">
      {/* Sticky Top Nav Bar */}
      <div className="sticky top-0 z-30 w-full bg-white border-b shadow-xs">
        <div className="container max-w-3xl px-4 py-3 flex items-center justify-between">
          <Button 
            variant="ghost" 
            onClick={() => setLocation(`/incidents/${incidentId}`)} 
            className="flex items-center gap-1 py-1 h-9"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Incident</span>
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="hidden md:block w-44">
              <div className="flex items-center gap-2">
                <Progress value={progress} className="h-2 flex-1" />
                <span className="text-xs font-medium">{progress}%</span>
              </div>
            </div>
            
            {completionStatus === 'not-started' && (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                🔴 Not Started
              </Badge>
            )}
            {completionStatus === 'in-progress' && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                🟡 In Progress
              </Badge>
            )}
            {completionStatus === 'complete' && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                🟢 Complete
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      <div className="container max-w-3xl px-4 py-8">
        <div className="mb-8">
          <div className="flex flex-col gap-1">
            <h1 className="text-2xl font-bold">OSHA Form 301: Injury and Illness Incident Report</h1>
            <p className="text-muted-foreground">Linked to Incident #{incidentId}</p>
          </div>
          
          {/* Mobile Progress Bar - only visible on smaller screens */}
          <div className="mt-6 md:hidden">
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <Progress value={progress} className="h-2" />
              </div>
              <span className="text-sm font-medium">{progress}%</span>
            </div>
          </div>
        </div>
      
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-8">
          <div className="flex gap-3">
            <Info className="h-5 w-5 text-amber-600 shrink-0 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">OSHA Recordkeeping Requirement</h3>
              <p className="text-sm text-amber-700 mt-1">
                This form must be retained for 5 years following the year to which it pertains.
                Completing this form will automatically update your OSHA 300 Log and 300A Summary.
              </p>
            </div>
          </div>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Section 1: Employee Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">1</span>
                  Employee Information
                </CardTitle>
                <CardDescription>Identify the employee involved in the incident</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="employeeName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employee Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Full name" 
                          {...field} 
                          disabled={viewMode}
                          className={viewMode ? "bg-gray-50" : ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Job position or title" 
                          {...field} 
                          disabled={viewMode}
                          className={viewMode ? "bg-gray-50" : ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
            
            {/* Section 2: Incident Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">2</span>
                  Incident Details
                </CardTitle>
                <CardDescription>Information about when and where the incident occurred</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="dateOfInjury"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Date of Injury
                        </FormLabel>
                        <FormControl>
                          <Input 
                            type="date" 
                            {...field} 
                            disabled={viewMode} 
                            className={viewMode ? "bg-gray-50" : ""} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="timeOfEvent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          Time of Event
                        </FormLabel>
                        <FormControl>
                          <Input 
                            type="time" 
                            {...field} 
                            disabled={viewMode} 
                            className={viewMode ? "bg-gray-50" : ""} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        Location
                      </FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Where the incident occurred" 
                          {...field} 
                          disabled={viewMode}
                          className={viewMode ? "bg-gray-50" : ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Describe what happened</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe the sequence of events and how the injury occurred" 
                          className={`min-h-[100px] ${viewMode ? "bg-gray-50" : ""}`}
                          {...field}
                          disabled={viewMode}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
            
            {/* Section 3: Nature of Injury */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">3</span>
                  Nature of Injury
                </CardTitle>
                <CardDescription>Details about the injury or illness</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bodyPartAffected"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Body Part Affected
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span><HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" /></span>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>AI-suggested based on incident description</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., Hand, back, eye, etc." 
                            {...field} 
                            disabled={viewMode}
                            className={viewMode ? "bg-gray-50" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="injuryType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Type of Injury
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span><HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" /></span>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>AI-suggested based on incident description</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., Cut, burn, fracture, etc." 
                            {...field} 
                            disabled={viewMode}
                            className={viewMode ? "bg-gray-50" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="treatmentLocation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Treatment Location</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Where the employee was treated, if applicable" 
                          {...field} 
                          disabled={viewMode}
                          className={viewMode ? "bg-gray-50" : ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
            
            {/* Section 4: OSHA Questions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">4</span>
                  OSHA Questions
                </CardTitle>
                <CardDescription>Required information for OSHA compliance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="wasHospitalized"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between space-x-3 space-y-0 rounded-md border p-4">
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Was the employee hospitalized?
                          </FormLabel>
                          <FormDescription>
                            Overnight stay as an in-patient
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={viewMode ? undefined : field.onChange}
                            disabled={viewMode}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="resultedInDeath"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between space-x-3 space-y-0 rounded-md border p-4">
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Did the injury result in death?
                          </FormLabel>
                          <FormDescription>
                            Required for fatality reporting
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={viewMode ? undefined : field.onChange}
                            disabled={viewMode}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="daysAwayFromWork"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of days away from work</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            placeholder="0" 
                            {...field}
                            disabled={viewMode}
                            className={viewMode ? "bg-gray-50" : ""}
                          />
                        </FormControl>
                        <FormDescription>
                          If none, enter 0
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="daysOfRestrictedDuty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of restricted duty days</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            placeholder="0" 
                            {...field}
                            disabled={viewMode}
                            className={viewMode ? "bg-gray-50" : ""}
                          />
                        </FormControl>
                        <FormDescription>
                          If none, enter 0
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 shrink-0 mt-0.5" />
                    <div>
                      <p className="text-sm text-blue-800">
                        This form will be used to update your organization's OSHA 300 Log of 
                        Work-Related Injuries and Illnesses and the Annual Summary (Form 300A).
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Form Actions */}
            <div className="flex flex-wrap justify-between gap-4 pt-2">
              {viewMode ? (
                <div className="flex gap-2 flex-wrap w-full">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleExportPdf}
                    className="mr-auto"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export as PDF
                  </Button>
                  
                  {editMode !== true && (
                    <Button 
                      type="button"
                      onClick={() => setLocation(`/osha-form-301?incidentId=${incidentId}&mode=edit`)}
                      variant="outline"
                      className="bg-primary-50 border-primary-200 text-primary-700 hover:bg-primary-100"
                    >
                      <PenLine className="mr-2 h-4 w-4" />
                      Edit Log
                    </Button>
                  )}
                  
                  <Button 
                    type="button"
                    onClick={() => setLocation(`/incidents/${incidentId}`)}
                    className="bg-primary-500 hover:bg-primary-700"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Incident
                  </Button>
                </div>
              ) : (
                <>
                  <div className="flex gap-2 flex-wrap">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={handleSaveDraft}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save Draft
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={handleExportPdf}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Export as PDF
                    </Button>
                  </div>
                  
                  <Button 
                    type="submit"
                    className="bg-primary-500 hover:bg-primary-700"
                    disabled={!form.formState.isValid}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Submit Log Entry
                  </Button>
                </>
              )}
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}