import { MediaUpload } from '@/components/composite/media-upload';
import { SuccessModal } from '@/components/composite/success-modal';
import { VoiceInput } from '@/components/composite/voice-input';
import { AnalyzingLoading } from '@/components/incidents/create/analyzing-loading';
import { AsyncDropdown } from '@/components/ui/async-dropdown';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { useAppContext } from '@/contexts/app-context';
import { usePermissions } from '@/hooks/use-permissions';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { incidentCategoryEnum, reportTypeEnum, severityEnum } from '@shared/schema';
import { CATEGORY_MAP, CreateIncidentFormSchema, SEVERITY_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import axios from 'axios';
import { format } from 'date-fns';
import { CalendarIcon, Sparkles } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';
import { z } from 'zod';

const FormSchema = CreateIncidentFormSchema;

export default function NewIncident() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [createdIncidentId, setCreatedIncidentId] = useState<string | undefined>();
  const [locationSearch, setLocationSearch] = useState('');
  const [assetSearch, setAssetSearch] = useState('');

  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();
  const { user } = useAppContext();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: reportTypeEnum.enumValues[0],
      title: '',
      reportedAt: new Date(),
      category: undefined,
      severity: 'low',
      description: '',
      immediateActions: '',
      assetIds: [],
      locationId: undefined,
    },
    mode: 'onSubmit',
  });

  const { mutateAsync: analyze, isPending: isAnalyzing } = trpc.ai.analyzeIncident.useMutation({
    onSuccess: (data) => {
      console.log('Analysis complete', data);
      toast('Analysis complete', {
        description: 'We have analyzed your incident report and filled out the form for you.',
      });
    },
    onError: (error) => {
      console.error('Error analyzing incident', error);
      toast('Error analyzing incident', {
        description: 'There was a problem analyzing your incident report. Please try again.',
      });
    },
  });

  const { data: locations, isLoading: isLoadingLocations } = trpc.location.search.useQuery({
    search: locationSearch,
    limit: 100,
    page: 1,
  });

  const { data: assets, isLoading: isLoadingAssets } = trpc.asset.search.useQuery(
    {
      search: assetSearch,
      limit: 100,
      page: 1,
      locationId: form.watch('locationId') || undefined,
    },
    {
      enabled: true,
    },
  );

  useEffect(() => {
    if (form.watch('locationId')) {
      form.setValue('assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId')]);

  const { mutateAsync: createIncident } = trpc.incident.create.useMutation({
    onSuccess: () => {
      utils.incident.list.invalidate();
    },
    onError: (error) => {
      console.error('Error creating incident', error);
      toast('Error creating incident', {
        description: 'There was a problem creating your incident report. Please try again.',
      });
    },
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    try {
      const createdIncident = await createIncident({ ...values, media: undefined, mediaFiles: undefined });

      setCreatedIncidentId(createdIncident?.id);

      for (const [index, file] of values.media?.entries() ?? []) {
        const result = await getPresignedUrl({
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          entityType: 'incident',
          entityId: createdIncident?.id,
        });

        try {
          await axios.put(result.presignedUrl, mediaFiles[index], {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      toast('Incident created', {
        description: 'Your incident has been created successfully',
      });

      // Reset form
      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show the success modal and let user decide when to close it
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting incident', error);
      toast('Error reporting incident', {
        description: 'There was a problem submitting your incident report. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handler for "Report Another Incident" action
  const handleReportAnother = () => {
    setShowSuccessModal(false);
  };

  // Track which fields were auto-filled for animations
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);

  // Handle the extracted data from voice analysis
  const handleVoiceAnalysis = async (text: string) => {
    // Clear any previous animations
    setAutoFilledFields([]);

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const data = await analyze({ text, timezone });

    // Animate each field separately with staggered timing
    const animateField = (field: keyof z.infer<typeof FormSchema>, value: any, delay: number) => {
      setTimeout(() => {
        form.setValue(field, value);
        setAutoFilledFields((prev) => [...prev, field]);
      }, delay);
    };

    // Base delay between animations and running counter
    const baseDelay = 300;
    let currentDelay = 300;

    // Apply animations in sequence for first section of form
    animateField('type', data?.type, currentDelay);
    currentDelay += baseDelay;

    animateField('title', data?.title, currentDelay);
    currentDelay += baseDelay;

    if (data && 'category' in data && data.category) {
      animateField('category', data.category, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'reportedAt' in data && data.reportedAt) {
      animateField('reportedAt', data.reportedAt, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'severity' in data && data.severity) {
      animateField('severity', data.severity, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'description' in data && data.description) {
      animateField('description', data.description, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'immediateActions' in data && data.immediateActions) {
      animateField('immediateActions', data.immediateActions, currentDelay);
      currentDelay += baseDelay;
    }

    toast('🪄 AI Voice Analysis Complete', {
      description: "We've filled out the form with your spoken incident report",
    });
  };

  if (!user) {
    return <Redirect to={ROUTES.BASE} />;
  }

  if (!hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.INCIDENT_LIST} />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onReportAnother={handleReportAnother}
        onViewIncident={() => {
          if (createdIncidentId) {
            navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(createdIncidentId));
          }
        }}
      />

      <div>
        <div className="mb-6 sm:mb-8">
          <p className="text-muted-foreground text-base sm:text-lg">
            Use this form to report safety incidents or near misses that occurred in your facility.
          </p>
        </div>

        {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.USE_AI_VOICE) && (
          <div className="mb-5">
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
              <h1 className="text-lg sm:text-xl font-semibold text-gray-800">Describe the issue</h1>
              <VoiceInput onAnalysisComplete={handleVoiceAnalysis} isPublic={false} isLoading={isAnalyzing} />
              {/* AI Analysis Loading */}
              {isAnalyzing && <AnalyzingLoading />}
            </div>
          </div>
        )}

        {/* Media Upload Component */}

        {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.ATTACH_MEDIA) && (
          <div className="mb-10 w-full px-0 sm:px-0">
            <MediaUpload
              className="w-full max-w-full"
              files={form.watch('media') ?? []}
              setFiles={(tFiles, files) => {
                form.setValue('media', tFiles);
                setMediaFiles(files);
              }}
            />
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Report Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Report Type <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportType') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                    value={field.value} // Use value instead of defaultValue to keep it updated
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('reportType') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(reportTypeEnum.enumValues).map((type) => {
                        return (
                          <SelectItem key={type} value={type}>
                            {type === 'incident' ? 'Incident' : 'Near Miss'}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    An incident is an event that caused injury or damage. A near miss is an event that could have caused
                    injury or damage but didn't.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Title <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('title') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Briefly describe what happened"
                      className={autoFilledFields.includes('title') ? 'border-indigo-300' : ''}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A short description of the incident (e.g., "Fall from ladder in warehouse")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <FormField
              control={form.control}
              name="reportedAt"
              render={({ field }) => (
                <FormItem className={`flex flex-col`}>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Date and Time <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportedAt') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={cn(
                            'w-full pl-3 text-left text-base md:text-sm font-normal',
                            !field.value && 'text-muted-foreground',
                            autoFilledFields.includes('reportedAt') ? 'border-indigo-300' : '',
                          )}
                        >
                          {field.value ? format(field.value, 'PPP HH:mm') : <span>Pick a date</span>}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={field.value} onSelect={field.onChange} />
                      <div className="p-3 border-t border-border">
                        <Input
                          type="time"
                          value={field.value ? format(field.value, 'HH:mm') : ''}
                          onChange={(e) => {
                            const [hours, minutes] = e.target.value.split(':');
                            const newDate = new Date(field.value as Date);
                            newDate.setHours(parseInt(hours), parseInt(minutes));
                            field.onChange(newDate);
                          }}
                        />
                      </div>
                    </PopoverContent>
                  </Popover>
                  <FormDescription>When did the incident occur?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Location</FormLabel>
                    {autoFilledFields.includes('location') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncDropdown
                      placeholder="Where did it happen?"
                      {...field}
                      value={field.value}
                      options={locations?.data.reduce(
                        (acc, location) => {
                          acc[location.id] = location.name;
                          return acc;
                        },
                        {} as Record<string, string>,
                      )}
                      loading={isLoadingLocations}
                      search={locationSearch}
                      onSearch={(search) => {
                        setLocationSearch(search);
                      }}
                      multi={false}
                    />
                  </FormControl>
                  <FormDescription>Specific area, building, or equipment where the incident occurred</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Assets */}
            <FormField
              control={form.control}
              name="assetIds"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Assets</FormLabel>
                    {autoFilledFields.includes('assetIds') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncDropdown
                      placeholder="Select assets"
                      {...field}
                      value={field.value ?? ''}
                      options={assets?.data.reduce(
                        (acc, asset) => {
                          acc[asset.id] = asset.name;
                          return acc;
                        },
                        {} as Record<string, string>,
                      )}
                      loading={isLoadingAssets}
                      search={assetSearch}
                      onSearch={(search) => {
                        setAssetSearch(search);
                      }}
                      multi={true}
                    />
                  </FormControl>
                  <FormDescription>
                    {/* Equipment or asset involved (AI will suggest based on selected location) */}
                    Select the assets that were involved in the incident. If the incident involved multiple assets,
                    select all that apply.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Hazard Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Hazard Category <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('category') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('category') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Type of hazard" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(incidentCategoryEnum.enumValues).map((category) => (
                        <SelectItem key={category} value={category}>
                          {CATEGORY_MAP[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Examples: Chemical, Electrical, Ergonomic, Fall, Fire, Mechanical</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Severity Level */}
            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Severity Level <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('severityLevel') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('severityLevel') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(severityEnum.enumValues).map((level) => (
                        <SelectItem key={level} value={level}>
                          {SEVERITY_MAP[level]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>How serious was this incident or how serious could it have been?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Description</FormLabel>
                    {autoFilledFields.includes('description') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what happened in detail"
                      className={`min-h-[120px] ${autoFilledFields.includes('description') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed account of what happened, what led to the incident, and any relevant
                    circumstances
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Immediate Actions */}
            <FormField
              control={form.control}
              name="immediateActions"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Immediate Actions Taken</FormLabel>
                    {autoFilledFields.includes('immediateActions') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="What actions were taken immediately after the incident?"
                      className={`min-h-[80px] ${autoFilledFields.includes('immediateActions') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe any immediate steps taken to address the situation, treat injuries, or prevent further harm
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  form.reset();
                  setAutoFilledFields([]);

                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                Reset
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Report'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
