import { AccessPointsEmpty } from '@/components/access-points/access-points-empty';
import { AccessPointsError } from '@/components/access-points/access-points-error';
import { AccessPointsFilters } from '@/components/access-points/access-points-filters';
import { AccessPointsLoading } from '@/components/access-points/access-points-loading';
import { AccessPointsMobileFilters } from '@/components/access-points/access-points-mobile-filters';
import { AccessPointsMobileView } from '@/components/access-points/access-points-mobile-view';
import { AccessPointsTable } from '@/components/access-points/access-points-table';
import { ArchiveConfirmationDialog } from '@/components/access-points/archive-confirmation-dialog';
import { CreateAccessPointModal } from '@/components/access-points/create-access-point-modal';
import { QrViewingModal } from '@/components/access-points/qr-viewing-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { trpc } from '@/providers/trpc';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { accessPointStatusEnum } from '@shared/schema';
import type { AccessPoint } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import {
  Search,
  X
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { type DateRange } from 'react-day-picker';
import { Redirect, useLocation } from 'wouter';
import { usePermissions } from '@/hooks/use-permissions';

type Filters = {
  status: 'all' | (typeof accessPointStatusEnum.enumValues)[number];
  locationId: string[];
  createdBy: string[];
  includeArchived: boolean;
  dateRange: DateRange | undefined;
};

export default function AccessPointsView() {
  const utils = trpc.useUtils();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isQrModalOpen, setIsQrModalOpen] = useState<boolean>(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<AccessPoint | undefined>(undefined);
  const [isArchiveModalOpen, setIsArchiveModalOpen] = useState<boolean>(false);
  const [accessPointToArchive, setAccessPointToArchive] = useState<AccessPoint | null>(null);

  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();

  if (!hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)) {
    return <Redirect to={ROUTES.INCIDENT_LIST} />;
  }

  const [filters, setFilters] = useState<Filters>({
    status: 'all',
    locationId: [],
    createdBy: [],
    includeArchived: false,
    dateRange: {
      from: undefined,
      to: undefined,
    },
  });

  const handleFilterChange = (key: keyof Filters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // status options array combining 'all' with enum values

  const {
    data: accessPointsResponse,
    isLoading,
    error,
  } = trpc.accessPoint.list.useQuery({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    includeArchived: filters.includeArchived,
    locationId: filters.locationId,
    createdBy: filters.createdBy,
    status: filters.status === 'all' ? undefined : filters.status,
    search: debouncedSearchTerm,
    createdDateStart: filters.dateRange?.from,
    createdDateEnd: filters.dateRange?.to,
  });

  const updateAccessPoint = trpc.accessPoint.update.useMutation({
    onSuccess: () => {
      utils.accessPoint.list.invalidate();
    },
  });

  // Get locations from API
  const { data: locationsResponse, isLoading: locationsLoading } = trpc.location.search.useQuery({
    limit: 100,
    page: 1,
  });

  const { data: allUsers, isLoading: isLoadingUsers } = trpc.user.searchUsers.useQuery({ search: '' });

  const userOptions = useMemo(() => {
    if (!allUsers) return [];
    return allUsers.map((user) => ({
      id: user.id,
      name: `${user.firstName} ${user?.lastName || ''} (${user?.username})`,
    }));
  }, [allUsers]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.ACCESS_POINTS_NEW) {
      setIsModalOpen(true);
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      status: 'all',
      locationId: [],
      createdBy: [],
      includeArchived: false,
      dateRange: {
        from: undefined,
        to: undefined,
      },
    });
    setSearchTerm('');
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.status !== 'all' ? 1 : 0) +
      (filters.locationId.length > 0 ? 1 : 0) +
      (filters.createdBy.length > 0 ? 1 : 0) +
      (filters.dateRange?.from || filters.dateRange?.to ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearchTerm.trim() ? 1 : 0)
    );
  }, [filters, debouncedSearchTerm]);

  const locations = locationsResponse?.data || [];

  // Handle viewing QR code for an existing access point
  const handleViewQRCode = async (accessPoint: AccessPoint, qrCodeUrl: string) => {
    setSelectedAccessPoint(accessPoint);

    if (qrCodeUrl) {
      // If the access point already has a QR code URL, just show it
      setQrCodeUrl(qrCodeUrl);
      setIsQrModalOpen(true);
      return;
    }

    // Otherwise, generate a QR code URL for this access point
    setIsGenerating(true);
    try {
      // Construct the full URL
      const baseUrl = window.location.origin;
      const queryParams = new URLSearchParams();
      queryParams.append('accessPointId', accessPoint.id);
      queryParams.append('roleId', accessPoint.upkeepCompanyId);

      const url = `${baseUrl}/ehs/public/incidents/new?${queryParams.toString()}`;

      // Store the QR code URL
      setQrCodeUrl(url);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
      // TODO: Show error message to user via toast or alert
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle form submission - generate QR code
  const handleGenerateQR = async (createdAccessPoint: AccessPoint) => {
    setIsGenerating(true);

    try {
      // Construct the full URL for the QR code
      const baseUrl = window.location.origin;

      // Always include the access point name in the URL
      const queryParams = new URLSearchParams();
      queryParams.append('accessPointId', createdAccessPoint.id);
      queryParams.append('roleId', createdAccessPoint.upkeepCompanyId);

      const fullUrl = `${baseUrl}/ehs/public/incidents/new?${queryParams.toString()}`;

      // Store the QR code URL
      setQrCodeUrl(fullUrl);
      // After successful generation, close the creation modal
      setIsModalOpen(false);

      // Set the created access point as selected and show QR modal immediately
      setSelectedAccessPoint(createdAccessPoint);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle status update
  const handleUpdateStatus = (id: string, status: 'active' | 'inactive') => {
    updateAccessPoint.mutate({
      id,
      status,
    });
  };

  // Handle archive action
  const handleArchive = (accessPoint: AccessPoint) => {
    setAccessPointToArchive(accessPoint);
    setIsArchiveModalOpen(true);
  };

  // Handle archive confirmation
  const handleArchiveConfirm = async () => {
    if (!accessPointToArchive) return;

    try {
      await updateAccessPoint.mutateAsync({ id: accessPointToArchive.id, archived: !accessPointToArchive.archived });
      setIsArchiveModalOpen(false);
      setAccessPointToArchive(null);
    } catch (error) {
      console.error('Error archiving access point:', error);
      // You might want to show an error toast here
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col space-y-4 mb-6">
        {/* Title and Desktop Controls */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center">
          <h1 className="text-2xl font-bold">QR Code Access Points</h1>
          
          {/* Desktop Search and Create Button */}
          <div className="hidden lg:flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search access points..."
                className="pl-8 pr-4 w-80"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-9 w-9 p-0"
                  onClick={() => setSearchTerm('')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button onClick={() => setIsModalOpen(true)}>
              + Create Access Point
            </Button>
          </div>
        </div>

        {/* Mobile Search and Controls */}
        <div className="flex lg:hidden items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search access points..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-9 w-9 p-0"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button onClick={() => setIsModalOpen(true)} size="sm">
            + Create
          </Button>
          <AccessPointsMobileFilters
            activeFilterCount={activeFilterCount}
            filters={filters}
            setFilters={setFilters}
            locations={locations}
            userOptions={userOptions}
            isLoadingUsers={isLoadingUsers}
            resetFilters={resetFilters}
          />
        </div>
      </div>

      <QrViewingModal
        isQrModalOpen={isQrModalOpen}
        setIsQrModalOpen={setIsQrModalOpen}
        accessPoint={selectedAccessPoint}
        qrCodeUrl={qrCodeUrl}
        locations={locations}
      />

      {/* Create Access Point Modal */}
      <CreateAccessPointModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        locationsLoading={locationsLoading}
        isGenerating={isGenerating}
        handleGenerateQR={handleGenerateQR}
        locations={locations}
      />

      {/* Desktop Filters bar */}
      <AccessPointsFilters
        filters={filters}
        setFilters={setFilters}
        locations={locations}
        userOptions={userOptions}
        isLoadingUsers={isLoadingUsers}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        handleFilterChange={handleFilterChange}
      />

      {/* Error state */}
      {error && <AccessPointsError />}

      {/* Loading state */}
      {isLoading && <AccessPointsLoading />}

      {/* Desktop Table View */}
      {!isLoading && !error && accessPointsResponse?.data && accessPointsResponse.data.length > 0 && (
        <AccessPointsTable
          accessPoints={accessPointsResponse.data}
          locations={locations}
          onViewQRCode={handleViewQRCode}
          onUpdateStatus={handleUpdateStatus}
          onArchive={handleArchive}
          qrCodeUrl={qrCodeUrl}
        />
      )}

      {/* Mobile Card View */}
      {!isLoading && !error && accessPointsResponse?.data && accessPointsResponse.data.length > 0 && (
        <AccessPointsMobileView
          accessPoints={accessPointsResponse.data}
          locations={locations}
          onViewQRCode={handleViewQRCode}
          onUpdateStatus={handleUpdateStatus}
          onArchive={handleArchive}
          qrCodeUrl={qrCodeUrl}
        />
      )}

      {/* Empty state - when no access points are found and not loading */}
      {!isLoading && !error && accessPointsResponse?.data && accessPointsResponse.data.length === 0 && (
        <AccessPointsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateAccessPoint={() => setIsModalOpen(true)}
        />
      )}

      {/* Archive Confirmation Dialog */}
      <ArchiveConfirmationDialog
        isOpen={isArchiveModalOpen}
        onClose={() => {
          setIsArchiveModalOpen(false);
          setAccessPointToArchive(null);
        }}
        accessPoint={accessPointToArchive}
        locations={locations}
        onConfirm={handleArchiveConfirm}
        isLoading={updateAccessPoint.isPending}
      />
    </div>
  );
}
