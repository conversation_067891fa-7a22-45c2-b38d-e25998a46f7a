import { AsyncIncidentsSelect } from '@/components/composite/async-incidents-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { AsyncDropdown } from '@/components/ui/async-dropdown';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  capaEffectivenessStatusEnum,
  capaPriorityEnum,
  capaTagsEnum,
  capaTypeEnum,
  rcaMethodEnum,
  rootCauseEnum,
  statusEnum,
} from '@shared/schema';
import {
  CAPA_EFFECTIVENESS_STATUS_MAP,
  CAPA_PRIORITY_MAP,
  CAPA_TAGS_MAP,
  CAPA_TYPE_MAP,
  EditCapasFormSchema,
  IdArraySchema,
  RCA_METHOD_MAP,
  ROOT_CAUSE_MAP,
  STATUS_MAP,
  type Incident,
} from '@shared/schema.types';
import axios from 'axios';
import { format, isValid } from 'date-fns';
import { ArrowLeft, CalendarIcon, Check, Hash } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { z } from 'zod';

// extended here because File can only be defined on frontend
const FormSchema = EditCapasFormSchema;

type FormValues = z.infer<typeof FormSchema>;

export default function EditCapa({ params }: { params: { id: string } }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const [attachmentsFiles, setAttachmentsFiles] = useState<File[]>([]);

  // State for tags, attachments, and user search
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [userSearch, setUserSearch] = useState('');
  const [incidentSearch, setIncidentSearch] = useState('');
  const [locationSearch, setLocationSearch] = useState('');
  const [assetSearch, setAssetSearch] = useState('');

  // Fetch the existing CAPA data
  const capaId = params.id;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: 'corrective',
      title: '',
      status: 'open',
      rootCause: 'other',
      otherRootCause: '',
      ownerId: '',
      dueDate: undefined,
      priority: 'medium',
      incidentId: null,
      privateToAdmins: false,
      actionsToAddress: '',
      assetId: null,
      locationId: null,
      rcaFindings: '',
      rcaMethod: '5_whys',
      archived: false,
      tags: [],
      actionsImplemented: '',
      implementationDate: undefined,
      implementedBy: '',
      voeDueDate: undefined,
      verificationFindings: '',
      voePerformedBy: '',
      voeDate: undefined,
      effectivenessStatus: 'not_effective',
      attachments: [],
    },
  });

  const {
    data: capa,
    isPending: isCapaLoading,
    error: capaError,
  } = trpc.capa.getByIdForEdit.useQuery({
    id: capaId,
  });

  // Get linked incident details if available
  const { data: linkedIncident } = trpc.incident.getById.useQuery(
    { id: capa?.incidentId! },
    {
      enabled: !!capa?.incidentId,
    },
  );

  // Get users for owner selection
  const { data: users, isLoading: isLoadingUsers } = trpc.user.getUsers.useQuery({
    search: userSearch,
  });

  // Get incidents for linking
  const { data: incidents, isLoading: isLoadingIncidents } = trpc.incident.list.useQuery({
    page: 1,
    limit: 50,
    search: incidentSearch,
  });

  const { data: locations, isLoading: isLoadingLocations } = trpc.location.search.useQuery({
    page: 1,
    limit: 50,
    search: locationSearch,
  });

  const { data: assets, isLoading: isLoadingAssets } = trpc.asset.search.useQuery({
    page: 1,
    limit: 50,
    search: assetSearch,
    locationId: form.watch('locationId') ?? undefined,
  });

  // Add useEffect to handle locationId changes
  useEffect(() => {
    form.setValue('assetId', null);
    utils.asset.search.invalidate();
  }, [form.watch('locationId')]);

  const { mutateAsync: updateCapa } = trpc.capa.update.useMutation({
    onSuccess: () => {
      utils.capa.getById.invalidate({ id: capaId });
      utils.capa.list.invalidate();
    },
    onError: (error) => {
      console.error(error);

      toast('Error updating CAPA', {
        description: 'There was a problem saving your changes.',
      });
    },
  });

  // Mutations for file upload
  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();
  const { mutateAsync: updateFile } = trpc.file.update.useMutation();
  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();

  // Update form values when CAPA data is loaded
  useEffect(() => {
    if (capa) {
      // Convert dueDate string to Date object if it exists
      const dueDate = capa.dueDate ? new Date(capa.dueDate) : undefined;

      // Handle potentially null rootCause
      const rootCause = capa.rootCause || undefined;

      // Set form values from CAPA data
      form.reset({
        type: capa.type,
        title: capa.title,
        status: capa.status,
        rootCause: rootCause,
        otherRootCause: capa.rootCause === 'other' ? capa.otherRootCause || '' : '',
        actionsToAddress: capa.actionsToAddress || '',
        ownerId: capa.ownerId || '',
        dueDate: dueDate,
        priority: capa.priority || 'medium',
        incidentId: capa.incidentId || null,
        privateToAdmins: capa.privateToAdmins || false,
        archived: capa.archived || false,
        rcaMethod: capa.rcaMethod || '5_whys',
        rcaFindings: capa.rcaFindings || '',
        assetId: capa.assetId || null,
        locationId: capa.locationId || null,
        tags: capa.tags || [],
        actionsImplemented: capa.actionsImplemented || '',
        implementationDate: capa.implementationDate || undefined,
        implementedBy: capa.implementedBy,
        voeDueDate: capa.voeDueDate || undefined,
        verificationFindings: capa.verificationFindings || '',
        voePerformedBy: capa.voePerformedBy,
        voeDate: capa.voeDate || undefined,
        effectivenessStatus: capa.effectivenessStatus || 'not_effective',
        attachments: capa.attachments ?? [],
      });

      // Set UI state for tags
      setSelectedTags(capa.tags || []);
    }
  }, [capa]);

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) => {
      const isSelected = prev.includes(tag);
      if (isSelected) {
        // Remove tag
        const newTags = prev.filter((t) => t !== tag);
        form.setValue('tags', newTags.length > 0 ? newTags : []);
        return newTags;
      } else {
        // Add tag
        const newTags = [...prev, tag];
        form.setValue('tags', newTags);
        return newTags;
      }
    });
  };

  // Handle form submission
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as any)[field] = values[field as keyof FormValues];
      }
    }

    await updateCapa({
      ...toUpdate,
      id: capaId,
    });

    if ('attachments' in toUpdate) {
      await removeFiles(capa?.attachments?.map((file) => file.id).filter(Boolean) as z.infer<typeof IdArraySchema>);
    }

    // Handle file uploads
    for (const [index, file] of values.attachments?.entries() ?? []) {
      const result = await getPresignedUrl({
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        entityType: 'capa',
        entityId: capaId,
      });

      try {
        await axios.put(result.presignedUrl, attachmentsFiles[index], {
          headers: {
            'Content-Type': result.file?.mimeType,
          },
        });

        if (!result?.file) {
          throw new Error('Error uploading file');
        } else {
          await updateFile({
            id: result.file.id,
            s3Key: result.file.s3Key,
            status: 'completed',
          });
        }

        form.setValue('attachments', []);
      } catch (error) {
        console.error('Error uploading file', error);
        toast('Error uploading file', {
          description: 'There was a problem uploading your file. Please try again.',
        });
      } finally {
        setIsSubmitting(false);
      }
    }

    toast('CAPA updated', {
      description: 'Your changes have been saved successfully.',
    });

    // Navigate back to the CAPA details page
    navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId));
  }

  if (isCapaLoading) {
    return (
      <div className="container py-6">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center gap-2 mb-6">
            <Button variant="ghost" onClick={() => window.history.back()} className="gap-1">
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div className="flex-1">
              <Skeleton className="h-8 w-64" />
            </div>
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-1/3 mb-2" />
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // If error, show error message
  if (capaError || !capa) {
    return (
      <div className="container py-6">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center gap-2 mb-6">
            <Button variant="ghost" onClick={() => window.history.back()} className="gap-1">
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Error Loading CAPA</CardTitle>
              <CardDescription>
                We couldn't load the CAPA data. Please try again or go back to the CAPA list.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => navigate('/capas')}>Return to CAPA Log</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button
          variant="ghost"
          onClick={() => window.history.back()}
          className="gap-1 hover:bg-neutral-300 text-neutral-900"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 className="text-2xl font-bold text-neutral-black">Edit #{capa.slug}</h1>
      </div>

      {/* Main layout container - full width form */}
      <div>
        <div>
          {capa && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* CAPA Summary Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">CAPA Summary</h2>

                  {/* CAPA Type */}
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          CAPA Type <span className="text-destructive-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select CAPA type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capaTypeEnum.enumValues.map((type) => (
                              <SelectItem key={type} value={type}>
                                {CAPA_TYPE_MAP[type]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select whether this is a corrective action, preventive action, or both.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Title <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Brief, descriptive title" {...field} />
                        </FormControl>
                        <FormDescription>Provide a clear, concise title that describes the CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status */}
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Status <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {statusEnum.enumValues.map((status) => (
                              <SelectItem key={status} value={status}>
                                {STATUS_MAP[status]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Current status of this CAPA. Status changes are logged in the audit trail.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Location */}
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Location</FormLabel>
                      </div>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Where did it happen?"
                          {...field}
                          value={field.value}
                          options={locations?.data.reduce(
                            (acc, location) => {
                              acc[location.id] = location.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingLocations}
                          search={locationSearch}
                          onSearch={(search) => {
                            setLocationSearch(search);
                          }}
                          multi={false}
                        />
                      </FormControl>
                      <FormDescription>
                        Specific area, building, or equipment where the incident occurred
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Assets */}
                <FormField
                  control={form.control}
                  name="assetId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Assets</FormLabel>
                      </div>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Select assets"
                          {...field}
                          value={field.value ?? ''}
                          options={assets?.data.reduce(
                            (acc, asset) => {
                              acc[asset.id] = asset.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingAssets}
                          search={assetSearch}
                          onSearch={(search) => {
                            setAssetSearch(search);
                          }}
                          multi={true}
                        />
                      </FormControl>
                      <FormDescription>
                        {/* Equipment or asset involved (AI will suggest based on selected location) */}
                        Select the assets that were involved in the incident. If the incident involved multiple assets,
                        select all that apply.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Action Details Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Action Details</h2>

                  {/* Root Cause */}
                  <FormField
                    control={form.control}
                    name="rootCause"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Root Cause</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select root cause" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rootCauseEnum.enumValues.map((cause) => (
                              <SelectItem key={cause} value={cause}>
                                {ROOT_CAUSE_MAP[cause]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Identify the primary cause of the issue being addressed.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Other Root Cause - only show when "other" is selected */}
                  {form.watch('rootCause') === 'other' && (
                    <FormField
                      control={form.control}
                      name="otherRootCause"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Other Root Cause</FormLabel>
                          <FormControl>
                            <Input placeholder="Specify the root cause" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Actions to Address */}
                  <FormField
                    control={form.control}
                    name="actionsToAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Recommended Actions</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="1. First action to take&#10;2. Second action to take&#10;3. Third action to take"
                            className="min-h-[150px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          List specific actions to take. Each line will be automatically formatted as a numbered item.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Method */}
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RCA Method</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value || '5_whys'}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select RCA method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Method used for root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Findings */}
                  <FormField
                    control={form.control}
                    name="rcaFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RCA Findings</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your root cause analysis"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Tags */}
                  <div>
                    <FormLabel>Tags</FormLabel>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {capaTagsEnum.enumValues.map((tag) => (
                        <div
                          key={tag}
                          className={`inline-flex items-center px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
                            selectedTags.includes(tag)
                              ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                          onClick={() => handleTagToggle(tag)}
                        >
                          {selectedTags.includes(tag) && <Check className="size-4 mr-1" />}
                          {CAPA_TAGS_MAP[tag]}
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Add tags to categorize this CAPA (e.g., Training, Procedure, Equipment).
                    </p>
                  </div>
                </div>

                {/* Actual Actions Taken */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Actual Actions Taken</h2>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="actionsImplemented"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Actions Implemented</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the actions implemented"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed actions implemented.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    {/* Implemented By */}
                    <FormField
                      control={form.control}
                      name="implementedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implemented By</FormLabel>
                          <FormControl>
                            <AsyncDropdown
                              placeholder="Select a person"
                              {...field}
                              value={field.value}
                              options={users?.reduce(
                                (acc, user) => {
                                  acc[user.id] = user.fullName;
                                  return acc;
                                },
                                {} as Record<string, string>,
                              )}
                              loading={isLoadingUsers}
                              search={userSearch}
                              onSearch={(search) => {
                                setUserSearch(search);
                              }}
                              multi={false}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="implementationDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implementation Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={'outline'}
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground',
                                  )}
                                >
                                  {field.value && isValid(field.value) ? (
                                    format(field.value, 'PPP')
                                  ) : (
                                    <span>Select implementation date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value || undefined}
                                onSelect={(date) => field.onChange(date || undefined)}
                                disabled={(date) => date < new Date()}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>Date this CAPA was implemented.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* VoE Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Verification of Effectiveness (VoE)</h2>

                  {/* VoE Due Date */}
                  <FormField
                    control={form.control}
                    name="voeDueDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VoE Due Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={'outline'}
                                className={cn(
                                  'w-full pl-3 text-left font-normal',
                                  !field.value && 'text-muted-foreground',
                                )}
                              >
                                {field.value && isValid(field.value) ? (
                                  format(field.value, 'PPP')
                                ) : (
                                  <span>Select VoE due date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={(date) => field.onChange(date || undefined)}
                              disabled={(date) => date < new Date()}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>Target completion date for this VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    {/* VoE Performed By */}
                    <FormField
                      control={form.control}
                      name="voePerformedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Performed By</FormLabel>
                          <FormControl>
                            <AsyncDropdown
                              placeholder="Select a person"
                              {...field}
                              value={field.value}
                              options={users?.reduce(
                                (acc, user) => {
                                  acc[user.id] = user.fullName;
                                  return acc;
                                },
                                {} as Record<string, string>,
                              )}
                              loading={isLoadingUsers}
                              search={userSearch}
                              onSearch={(search) => {
                                setUserSearch(search);
                              }}
                              multi={false}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for performing this VoE.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="voeDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={'outline'}
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground',
                                  )}
                                >
                                  {field.value && isValid(field.value) ? (
                                    format(field.value, 'PPP')
                                  ) : (
                                    <span>Select VoE date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value || undefined}
                                onSelect={(date) => field.onChange(date || undefined)}
                                disabled={(date) => date < new Date()}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>Date this VoE was completed.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="verificationFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VoE Findings</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your VoE"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Effectiveness Status */}
                  <FormField
                    control={form.control}
                    name="effectivenessStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Effectiveness Status</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value ?? undefined}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select effectiveness status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {capaEffectivenessStatusEnum.enumValues.map((status) => (
                                <SelectItem key={status} value={status}>
                                  {CAPA_EFFECTIVENESS_STATUS_MAP[status]}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormDescription>Status of this VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Assignment Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Assignment</h2>

                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    {/* Owner */}
                    <FormField
                      control={form.control}
                      name="ownerId"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Owner</FormLabel>
                          <FormControl>
                            <AsyncDropdown
                              placeholder="Select an owner"
                              {...field}
                              value={field.value}
                              options={users?.reduce(
                                (acc, user) => {
                                  acc[user.id] = user.fullName;
                                  return acc;
                                },
                                {} as Record<string, string>,
                              )}
                              loading={isLoadingUsers}
                              search={userSearch}
                              onSearch={(search) => {
                                setUserSearch(search);
                              }}
                              multi={false}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Due Date */}
                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Due Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={'outline'}
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground',
                                  )}
                                >
                                  {field.value && isValid(field.value) ? (
                                    format(field.value, 'PPP')
                                  ) : (
                                    <span>Select due date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value || undefined}
                                onSelect={(date) => field.onChange(date || undefined)}
                                disabled={(date) => date < new Date()}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>Target completion date for this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Priority */}
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>
                          Priority <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capaPriorityEnum.enumValues.map((priority) => (
                              <SelectItem key={priority} value={priority}>
                                {CAPA_PRIORITY_MAP[priority]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Set the priority level for this CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Linkage Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Linkage</h2>

                  {/* Linked Incident */}
                  <FormField
                    control={form.control}
                    name="incidentId"
                    render={({ field }) => {
                      // Find the currently selected incident from the incidents list or the linked incident
                      const currentIncident = field.value
                        ? incidents?.data?.find((inc) => inc.id === field.value) || linkedIncident
                        : null;

                      return (
                        <FormItem>
                          <FormLabel>Link to Incident</FormLabel>
                          <div className="flex flex-col gap-3">
                            {/* Display current linked incident if any */}
                            {field.value && currentIncident && (
                              <div className="flex items-center gap-2">
                                <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1 px-3 py-1">
                                  <Hash className="h-3 w-3" />
                                  <span>Incident #{currentIncident.slug}</span>
                                  <span className="ml-1 text-blue-600 hidden sm:inline">- {currentIncident.title}</span>
                                  <button
                                    type="button"
                                    className="ml-1 text-blue-800 hover:text-blue-900"
                                    onClick={() => {
                                      field.onChange(null);
                                      setIncidentSearch(''); // Clear search when removing
                                    }}
                                  >
                                    ×
                                  </button>
                                </Badge>
                              </div>
                            )}

                            {/* Show selector if nothing is linked */}
                            {!field.value && (
                              <FormControl>
                                <AsyncIncidentsSelect
                                  onChange={(incidentId) => {
                                    field.onChange(incidentId);
                                  }}
                                  value={field.value}
                                  onSearch={setIncidentSearch}
                                  search={incidentSearch}
                                  loading={isLoadingIncidents}
                                  options={incidents?.data?.reduce(
                                    (acc, incident) => {
                                      acc[incident.id] = incident;
                                      return acc;
                                    },
                                    {} as Record<string, Incident>,
                                  )}
                                  placeholder="Search and select an incident..."
                                />
                              </FormControl>
                            )}
                          </div>
                          <FormDescription>Connect this CAPA to a related incident or audit.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>

                {/* Attachments Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Attachments</h2>

                  {/* File Uploads */}
                  <div>
                    <FormLabel>Upload Files</FormLabel>
                    <div className="mt-2">
                      <MediaUpload
                        maxFiles={5}
                        maxSize={20}
                        className="bg-white"
                        files={form.watch('attachments') ?? []}
                        setFiles={(tFiles, files) => {
                          form.setValue('attachments', tFiles);
                          setAttachmentsFiles(files);
                        }}
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Upload supporting documents (PDF, PNG, JPEG, DOCX). Max size: 20MB per file.
                    </p>
                  </div>
                </div>

                {/* Team Notifications Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Team Notifications</h2>

                  {/* Private to Admins Toggle */}
                  <FormField
                    control={form.control}
                    name="privateToAdmins"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Private to Admins Only</FormLabel>
                          <FormDescription>When enabled, only administrators can view this CAPA.</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value || false} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId))}
                    className="px-4 font-medium text-primary-700 border-primary-700 hover:bg-primary-300"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="px-5">
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </div>
  );
}
