import { CapaSuccessModal } from '@/components/capas/create/capa-success-modal';
import { AsyncIncidentsSelect } from '@/components/composite/async-incidents-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { VoiceInput } from '@/components/composite/voice-input';
import { AnalyzingLoading } from '@/components/incidents/create/analyzing-loading';
import { AsyncDropdown } from '@/components/ui/async-dropdown';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { capaTagsEnum, capaTypeEnum, rcaMethodEnum, rootCauseEnum } from '@shared/schema';
import {
  CAPA_TAGS_MAP,
  CAPA_TYPE_MAP,
  CreateCapasFormSchema,
  Incident,
  RCA_METHOD_MAP,
  ROOT_CAUSE_MAP,
} from '@shared/schema.types';
import axios from 'axios';
import { format } from 'date-fns';
import { CalendarIcon, Check, Info, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation, useSearchParams } from 'wouter';
import { z } from 'zod';

const FormSchema = CreateCapasFormSchema;

export default function NewCapa() {
  const [_, navigate] = useLocation();

  const utils = trpc.useUtils();

  // States for UI
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);
  const [incidentSearch, setIncidentSearch] = useState<string>('');
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [attachmentsFiles, setAttachmentsFiles] = useState<File[]>([]);
  const [locationSearch, setLocationSearch] = useState<string>('');
  const [assetSearch, setAssetSearch] = useState<string>('');
  const [userSearch, setUserSearch] = useState<string>('');

  const [searchParams] = useSearchParams();

  const linkedIncidentId = searchParams.get('incidentId') ?? null;

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      title: '',
      type: 'corrective',
      incidentId: linkedIncidentId,
      rcaMethod: 'not_selected',
      rcaFindings: '',
      rootCause: 'other',
      otherRootCause: '',
      aiSuggestedAction: '',
      actionsToAddress: '',
      ownerId: undefined,
      dueDate: undefined,
      priority: 'medium',
      tags: [],
      privateToAdmins: false,
      status: 'open',
    },
    mode: 'onChange',
  });

  const { data: incident } = trpc.incident.getById.useQuery(
    {
      id: linkedIncidentId!,
    },
    {
      enabled: !!linkedIncidentId,
    },
  );

  const { data: incidents } = trpc.incident.list.useQuery({
    page: 1,
    limit: 10,
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { data: locations, isLoading: isLoadingLocations } = trpc.location.search.useQuery({
    page: 1,
    limit: 10,
    search: locationSearch,
  });

  const { data: assets, isLoading: isLoadingAssets } = trpc.asset.search.useQuery({
    page: 1,
    limit: 10,
    search: assetSearch,
    locationId: form.watch('locationId') || undefined,
  });

  const { data: users, isLoading: isLoadingUsers } = trpc.user.getUsers.useQuery({
    search: userSearch,
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  const { mutateAsync: createCapa } = trpc.capa.create.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: linkedIncidentId! });
      utils.capa.list.invalidate();
    },
  });

  const { mutateAsync: analyze, isPending: isAnalyzing } = trpc.ai.analyzeCapa.useMutation({
    onSuccess: (data) => {
      console.log('Analysis complete', data);
      toast('Analysis complete', {
        description: 'We have analyzed your incident report and filled out the form for you.',
      });
    },
    onError: (error) => {
      console.error('Error analyzing incident', error);
      toast('Error analyzing incident', {
        description: 'There was a problem analyzing your incident report. Please try again.',
      });
    },
  });

  useEffect(() => {
    if (form.watch('locationId')) {
      form.setValue('assetId', null);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId')]);

  useEffect(() => {
    if (linkedIncidentId && incident) {
      // Update form with actual incident data
      form.setValue('title', `CAPA for: ${incident.title}`);
      form.setValue('incidentId', linkedIncidentId);

      // Pre-fill location from incident
      if (incident.location) {
        form.setValue('locationId', incident.location.id);
      }

      if (incident.rootCause) {
        form.setValue('rootCause', incident.rootCause);
      }

      // Set appropriate priority based on hazard category
      if (incident.category && ['chemical', 'fire', 'electrical'].includes(incident.category)) {
        form.setValue('priority', 'high');
      }

      // Set default due date (2 weeks from now)
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 14);
      form.setValue('dueDate', dueDate);
    }
  }, [incident, linkedIncidentId]);

  // Separate useEffect to handle setting asset after location is set
  useEffect(() => {
    if (incident?.location && incident.assetIds?.length === 1) {
      form.setValue('assetId', incident.assetIds[0]);
    }
  }, [incident, form.watch('locationId')]);

  // Handle the voice analysis results from the VoiceInput component
  const handleVoiceAnalysis = async (text: string) => {
    setAutoFilledFields([]); // Clear any previous animations

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const analysis = await analyze({
      text,
      timezone,
    });

    console.log('Analysis', analysis);

    // Animate each field separately with staggered timing
    const animateField = (field: keyof z.infer<typeof FormSchema>, value: any, delay: number) => {
      setTimeout(() => {
        form.setValue(field, value);
        setAutoFilledFields((prev) => [...prev, field]);
      }, delay);
    };

    const baseDelay = 300;
    let currentDelay = 0;

    if (!linkedIncidentId) {
      animateField('title', analysis?.title, currentDelay);
      currentDelay += baseDelay;
    }

    if (analysis.rcaMethod) {
      animateField('rcaMethod', analysis.rcaMethod, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('rcaFindings', analysis.rcaFindings, currentDelay);
    currentDelay += baseDelay;

    animateField('actionsToAddress', analysis.actionsToAddress, currentDelay);
    currentDelay += baseDelay;

    console.log('analysis.rootCause', analysis.rootCause);

    animateField('rootCause', analysis.rootCause, currentDelay);
    currentDelay += baseDelay;

    animateField('type', analysis?.type, currentDelay);
    currentDelay += baseDelay;

    if (analysis.otherRootCause && analysis.rootCause === 'other') {
      animateField('otherRootCause', analysis?.otherRootCause, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('priority', analysis.priority, currentDelay);
    currentDelay += baseDelay;

    if (analysis.tags) {
      animateField('tags', analysis?.tags, currentDelay);
      setSelectedTags(analysis?.tags);
      currentDelay += baseDelay;
    }

    if (analysis.dueDate) {
      animateField('dueDate', analysis?.dueDate, currentDelay);
      currentDelay += baseDelay;
    }

    toast('✨ AI Analysis Complete', {
      description:
        'Form has been populated based on your voice description. Feel free to edit any field before submitting.',
    });
  };

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) => {
      const isSelected = prev.includes(tag);
      if (isSelected) {
        // Remove tag
        const newTags = prev.filter((id) => id !== tag);
        form.setValue('tags', newTags.length > 0 ? newTags : null);
        return newTags;
      } else {
        // Add tag
        const newTags = [...prev, tag];
        form.setValue('tags', newTags);
        return newTags;
      }
    });
  };

  // Handle creating another CAPA
  const handleCreateAnother = () => {
    // Reset form to default values
    form.reset({
      title: '',
      type: 'corrective',
      incidentId: null,
      rcaMethod: 'not_selected',
      rcaFindings: '',
      rootCause: 'human_error',
      otherRootCause: '',
      actionsToAddress: '',
      ownerId: '',
      dueDate: null,
      priority: 'medium',
      tags: [],
      privateToAdmins: false,
      status: 'open',
    });

    // Reset UI state
    setSelectedTags([]);
    setAutoFilledFields([]);
    setIncidentSearch('');
  };

  // Form submission handler
  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    try {
      const createdCapa = await createCapa(values);

      for (const [index, file] of values.attachments?.entries() ?? []) {
        const result = await getPresignedUrl({
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          entityType: 'capa',
          entityId: createdCapa?.id,
        });

        try {
          await axios.put(result.presignedUrl, attachmentsFiles[index], {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      toast('CAPA created', {
        description: 'Your CAPA has been created successfully',
      });

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show the success modal and let user decide when to close it
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting incident', error);
      toast('Error reporting incident', {
        description: 'There was a problem submitting your incident report. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h1 className="text-2xl font-bold text-primary-600">Create CAPA</h1>
        {linkedIncidentId && incident && (
          <div className="text-sm text-gray-500">Linked to Incident: {incident.title}</div>
        )}
      </div>

      {/* CAPA Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-6 pt-5">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className={autoFilledFields.includes('title') ? 'relative' : ''}>
                  {autoFilledFields.includes('title') && (
                    <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                  )}
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Brief title for this CAPA" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* CAPA Type and Linked Incident - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CAPA Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {capaTypeEnum.enumValues.map((type) => (
                            <SelectItem key={type} value={type}>
                              {CAPA_TYPE_MAP[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>The type of action required</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Linked Incident - Mobile Optimized Dropdown */}
              <div className="sm:col-span-8">
                <FormField
                  control={form.control}
                  name="incidentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link to Incident</FormLabel>
                      <AsyncIncidentsSelect
                        onChange={field.onChange}
                        value={field.value}
                        onSearch={setIncidentSearch}
                        search={incidentSearch}
                        options={incidents?.data?.reduce(
                          (acc, incident) => {
                            acc[incident.id] = incident;
                            return acc;
                          },
                          {} as Record<string, Incident>,
                        )}
                      />
                      <FormDescription>
                        {field.value || linkedIncidentId ? (
                          <span className="text-green-600 flex items-center gap-1">
                            <Check className="size-4" />
                            This CAPA is linked to incident
                          </span>
                        ) : (
                          'Optional: Link this CAPA to an incident. Linked CAPAs will show on the incident record.'
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Location and Asset - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Select a location"
                          {...field}
                          value={field.value}
                          options={locations?.data.reduce(
                            (acc, location) => {
                              acc[location.id] = location.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingLocations}
                          search={locationSearch}
                          onSearch={(search) => {
                            setLocationSearch(search);
                          }}
                          multi={false}
                        />
                      </FormControl>

                      <FormDescription>Specific area where the CAPA will be implemented</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="assetId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Asset (Optional)</FormLabel>
                      <FormControl>
                        <AsyncDropdown
                          placeholder="Select an asset"
                          {...field}
                          disabled={!form.watch('locationId')}
                          value={field.value}
                          options={assets?.data.reduce(
                            (acc, asset) => {
                              acc[asset.id] = asset.name;
                              return acc;
                            },
                            {} as Record<string, string>,
                          )}
                          loading={isLoadingAssets}
                          search={assetSearch}
                          onSearch={(search) => {
                            setAssetSearch(search);
                          }}
                          multi={false}
                        />
                      </FormControl>
                      <FormDescription>Specific equipment or asset related to this CAPA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Root Cause Analysis Method - New section */}
            <Card className="border-blue-100 shadow-none mb-4">
              <CardHeader>
                <CardTitle className="text-md font-medium flex items-center gap-2">
                  <Info className="size-4" />
                  Root Cause Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-3">
                {/* AI Assist Voice Input for RCA Section */}
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
                  <p className="text-sm text-blue-700">
                    Use your voice to describe the incident investigation, the RCA method used, and the proposed actions
                    to address the root cause(s).
                  </p>
                  <VoiceInput onAnalysisComplete={handleVoiceAnalysis} isPublic={false} isLoading={isAnalyzing} />
                  {/* AI Analysis Loading */}
                  {isAnalyzing && <AnalyzingLoading />}
                </div>

                <div className="my-4">
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem className={autoFilledFields.includes('rcaMethod') ? 'relative' : ''}>
                        <FormLabel>RCA Method Used</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select analysis method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Choose the method used to analyze the root cause</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* RCA Findings - New field */}
                <FormField
                  control={form.control}
                  name="rcaFindings"
                  render={({ field }) => (
                    <FormItem className={autoFilledFields.includes('rcaFindings') ? 'relative' : ''}>
                      {autoFilledFields.includes('rcaFindings') && (
                        <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                      )}
                      <FormLabel>RCA Findings & Conclusion</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Document your findings and root cause analysis conclusion here. If using 5 Whys, include all questions and answers."
                          className="min-h-[120px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Root Cause Category moved into RCA section and renamed */}
                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="rootCause"
                    render={({ field }) => (
                      <FormItem className={autoFilledFields.includes('rootCause') ? 'relative' : ''}>
                        {autoFilledFields.includes('rootCause') && (
                          <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                        )}
                        <FormLabel>Identified Root Cause Category</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            // Clear other root cause when not "other"
                            if (value !== 'other') {
                              form.setValue('otherRootCause', null);
                            }
                          }}
                          value={field.value || ''}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select root cause category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rootCauseEnum.enumValues.map((cause) => (
                              <SelectItem key={cause} value={cause}>
                                {ROOT_CAUSE_MAP[cause]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the primary category for the root cause identified in your analysis
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Show "Other" input when "other" is selected */}
                  {form.watch('rootCause') === 'other' && (
                    <FormField
                      control={form.control}
                      name="otherRootCause"
                      render={({ field }) => (
                        <FormItem className="mt-2">
                          <FormLabel>Specify Other Root Cause</FormLabel>
                          <FormControl>
                            <Input
                              value={(field.value as string) || ''}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                              placeholder="Describe the root cause"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Actions to Address - Renamed from Recommended Action */}
            <FormField
              control={form.control}
              name="actionsToAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Proposed Actions</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Based on the RCA, what specific corrective and preventive actions are planned to eliminate the root cause and prevent recurrence?"
                      className="min-h-[120px]"
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>Include both immediate actions and long-term preventive measures</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Attachments Section - Moved after Proposed Actions */}
            <div className="pt-2 pb-3">
              <div className="mb-2">
                <MediaUpload
                  maxFiles={5}
                  maxSize={20}
                  className="bg-white"
                  files={form.watch('attachments') ?? []}
                  setFiles={(tFiles, files) => {
                    form.setValue('attachments', tFiles);
                    setAttachmentsFiles(files);
                  }}
                />
              </div>
              <div className="flex space-x-1 text-xs">
                <span className="text-muted-foreground">Attachments:</span>
                <span className="text-muted-foreground font-medium">
                  {form.watch('attachments')?.length} of 5 maximum
                </span>
              </div>
            </div>

            {/* Owner, Due Date, and Priority - Responsive Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Owner Select */}
              <FormField
                control={form.control}
                name="ownerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Owner</FormLabel>
                    <FormControl>
                      <AsyncDropdown
                        placeholder="Select an owner"
                        {...field}
                        value={field.value}
                        options={users?.reduce(
                          (acc, user) => {
                            acc[user.id] = user.fullName;
                            return acc;
                          },
                          {} as Record<string, string>,
                        )}
                        loading={isLoadingUsers}
                        search={userSearch}
                        onSearch={(search) => {
                          setUserSearch(search);
                        }}
                        multi={false}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn('w-full pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}
                          >
                            {field.value ? format(field.value, 'PPP') : <span>Select a date</span>}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority Radio Select */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem className={`space-y-3`}>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-4"
                      >
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="low" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Low</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="medium" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Medium</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="high" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">High</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tags - Responsive Grid */}
            <div>
              <FormLabel>Tags</FormLabel>
              <div className={`mt-2 flex flex-wrap gap-2 }`}>
                {capaTagsEnum.enumValues.map((tag) => (
                  <div
                    key={tag}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {selectedTags.includes(tag) && <Check className="size-4" />}
                    {CAPA_TAGS_MAP[tag]}
                  </div>
                ))}
              </div>
              <div className="mt-1 text-sm text-gray-500">
                Select all applicable tags to help with searching and filtering
              </div>
            </div>

            {/* Private to Admins Checkbox */}
            <FormField
              control={form.control}
              name="privateToAdmins"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox checked={field.value || false} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Private to Administrators</FormLabel>
                    <FormDescription>If checked, this CAPA will only be visible to administrators.</FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row justify-end mt-8 space-y-2 sm:space-y-0 sm:space-x-2">
              <Button type="button" variant="outline" onClick={() => navigate('/capas')}>
                Cancel
              </Button>

              {/* Primary Submit Button */}
              <Button type="submit" disabled={isSubmitting} className="min-w-[100px]">
                {isSubmitting ? <Loader2 className="size-4 animate-spin" /> : null}
                {isSubmitting ? 'Creating...' : 'Create CAPA'}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* Success Modal with confetti */}
      <CapaSuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        onCreateAnother={handleCreateAnother}
      />
    </div>
  );
}
