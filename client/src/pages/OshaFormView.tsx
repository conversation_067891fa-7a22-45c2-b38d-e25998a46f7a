// @ts-nocheck

import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { format } from 'date-fns';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import {
  ArrowLeft,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  Edit,
  FileDown,
  MapPin,
  X,
  Link as LinkIcon,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface OshaFormViewProps {
  params: {
    id: string;
  };
}

export default function OshaFormView({ params }: OshaFormViewProps) {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();

  // Get ID from URL path parameter
  const formId = params?.id;

  // Get query parameters (for incident ID if needed)
  const queryParams = new URLSearchParams(window.location.search);
  const incidentId = queryParams.get('incidentId');

  console.log('OshaFormView formId from URL path:', formId);

  // Fetch OSHA forms data
  const { data: oshaForms, isLoading } = useQuery({
    queryKey: ['/api/osha-logs'],
    enabled: true,
  });

  // State to hold the actual form data
  const [formData, setFormData] = useState<any>(null);

  // Make a direct API call for the specific form when viewing by ID
  useEffect(() => {
    const fetchFormById = async () => {
      if (formId) {
        try {
          // Use our new direct endpoint to get a specific form
          const response = await fetch(`/api/osha-forms/${formId}`);

          if (response.status === 404) {
            console.log('Form not found, falling back to logs API');
            // Form not found in direct API, try fetching from logs instead
            const logsResponse = await fetch(`/api/osha-logs`);
            if (!logsResponse.ok) {
              console.error('Error fetching from logs API:', logsResponse.statusText);
              return;
            }

            const formsData = await logsResponse.json();
            console.log('All OSHA forms data:', formsData);

            // Try to find the form in the logs
            const foundForm = formsData.find((form: any) => form.id && form.id.toString() === formId);

            if (foundForm) {
              console.log('Found form in logs API:', foundForm);

              // Use available data
              if (foundForm.completeFormData) {
                console.log('Using completeFormData:', foundForm.completeFormData);
                foundForm.formData = foundForm.completeFormData;
              }

              setFormData(foundForm);
            } else {
              console.log('Form not found with ID:', formId);
            }
            return;
          }

          if (!response.ok) {
            console.error('Error fetching OSHA form data:', response.statusText);
            return;
          }

          // Process the form returned from our direct API
          const foundForm = await response.json();
          console.log('Found form via direct API:', foundForm);

          // Logic to ensure we have form data
          if (foundForm && (foundForm.formData || foundForm.completeFormData || foundForm.rawForm)) {
            // Ensure formData exists and has all fields
            if (foundForm.completeFormData) {
              console.log('Using completeFormData from direct API');
              foundForm.formData = foundForm.completeFormData;
            } else if (foundForm.rawForm && foundForm.rawForm.formData) {
              console.log('Using rawForm.formData from direct API');
              foundForm.formData = foundForm.rawForm.formData;
            }

            setFormData(foundForm);
          } else {
            console.log('Form returned, but no form data found');
          }
        } catch (err) {
          console.error('Error fetching OSHA form:', err);
        }
      } else if (incidentId) {
        // Similar process for incident ID
        try {
          const response = await fetch(`/api/osha-logs`);
          if (!response.ok) {
            console.error('Error fetching OSHA form data:', response.statusText);
            return;
          }

          const formsData = await response.json();

          // Find by linked incident ID
          const foundForm = formsData.find(
            (form: any) => form.linkedIncidentId && form.linkedIncidentId.toString() === incidentId,
          );

          if (foundForm) {
            console.log('Found form for incident:', foundForm);

            // First, check for the completeFormData field which has all the form fields
            if (foundForm.completeFormData) {
              console.log('Using complete form data found in record:', foundForm.completeFormData);
              // Use the complete form data that includes all fields
              foundForm.formData = foundForm.completeFormData;
            }
            // Next check the original form data
            else if (foundForm.formData) {
              console.log('Using original form data:', foundForm.formData);
            }
            // Finally, try the raw form data if available
            else if (foundForm.rawForm && foundForm.rawForm.formData) {
              console.log('Using raw form data:', foundForm.rawForm.formData);
              foundForm.formData = foundForm.rawForm.formData;
            }

            setFormData(foundForm);
          } else {
            console.log('No form found for incident ID:', incidentId);
          }
        } catch (err) {
          console.error('Error fetching OSHA form for incident:', err);
        }
      }
    };

    fetchFormById();
  }, [formId, incidentId]);

  // Helper function to generate OSHA log ID
  function getOshaLogId() {
    if (formData && formData.logId) {
      return formData.logId;
    }

    const year = new Date().getFullYear();
    if (incidentId) {
      return `OSHA-${year}-${incidentId.padStart(4, '0') || '0000'}`;
    } else if (formId) {
      return `OSHA-${year}-${formId.padStart(4, '0') || '0000'}`;
    } else {
      // For standalone entries, use timestamp
      const timestamp = new Date().getTime().toString().slice(-6);
      return `OSHA-${year}-S${timestamp}`;
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto max-w-4xl py-10 px-4 sm:px-6">
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {incidentId ? 'Back to Incident' : 'Back to OSHA Logs'}
        </Button>
        <div className="flex flex-col items-center justify-center p-12">
          <h1 className="text-2xl font-bold mb-4">Loading OSHA Form...</h1>
          <p className="text-gray-500">Please wait while we retrieve the form data</p>
        </div>
      </div>
    );
  }

  // If no form data found
  if (!formData && !isLoading) {
    return (
      <div className="container mx-auto max-w-4xl py-10 px-4 sm:px-6">
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {incidentId ? 'Back to Incident' : 'Back to OSHA Logs'}
        </Button>
        <div className="flex flex-col items-center justify-center p-12">
          <h1 className="text-2xl font-bold mb-4 text-red-600">OSHA Form Not Found</h1>
          <p className="text-gray-600">We couldn't find the requested OSHA form.</p>
          <Button variant="default" onClick={() => setLocation('/osha-logs')} className="mt-6">
            View All OSHA Logs
          </Button>
        </div>
      </div>
    );
  }

  function handleBackToIncident() {
    if (incidentId) {
      setLocation(`/incidents/${incidentId}`);
    } else {
      setLocation('/osha-logs');
    }
  }

  function handleExportPdf() {
    toast({
      title: 'PDF Export',
      description: 'Your OSHA Form 301 has been sent to the printer or saved as PDF.',
      duration: 3000,
    });
  }

  return (
    <div className="container mx-auto max-w-4xl py-10 px-4 sm:px-6">
      <div>
        {/* Back navigation - Single consistent button */}
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {incidentId ? 'Back to Incident' : 'Back to OSHA Logs'}
        </Button>

        {/* Header with title and badge information */}
        <div className="flex flex-col mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-2">
            <h1 className="text-2xl font-bold">OSHA Form 301: Injury and Illness Incident Report</h1>

            {/* Action buttons in right-aligned group matching Incident View */}
            <div className="flex mt-2 sm:mt-0 space-x-2">
              {/* Print button */}
              <Button variant="outline" size="sm" onClick={() => window.print()}>
                <FileDown className="h-4 w-4 mr-2" />
                Print
              </Button>

              {/* Export PDF button */}
              <Button variant="outline" size="sm" onClick={handleExportPdf}>
                <FileDown className="h-4 w-4 mr-2" />
                Export PDF
              </Button>

              {/* Edit button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (formData?.id) {
                    // Set proper mode as query param for the form page
                    setLocation(`/osha-form-301?id=${formData.id}&mode=edit`);
                  } else if (incidentId) {
                    setLocation(`/osha-form-301?incidentId=${incidentId}&mode=edit`);
                  } else {
                    setLocation('/osha-form-301?mode=edit');
                  }
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>

          {/* Status badges row */}
          <div className="flex flex-wrap items-center gap-2 mt-1">
            <Badge className="bg-blue-50 text-blue-700 border-blue-200">Log ID: {getOshaLogId()}</Badge>
            {formData?.status === 'Draft' ? (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                Draft
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3.5 w-3.5 mr-1" />
                Submitted
              </Badge>
            )}

            {/* Show linked incident if one exists */}
            {formData?.linkedIncidentId && (
              <span className="text-sm text-muted-foreground flex items-center">
                <LinkIcon className="h-3.5 w-3.5 mr-1 inline" />
                Linked to Incident #{formData.linkedIncidentId}
              </span>
            )}

            {/* Show if this is a standalone report with no linked incident */}
            {!formData?.linkedIncidentId && (
              <span className="text-sm text-muted-foreground">Standalone OSHA Report</span>
            )}

            {/* Show privacy case badge if applicable */}
            {formData?.privacyCase && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                Privacy Case
              </Badge>
            )}
          </div>
        </div>

        {/* Main content cards */}
        <div className="space-y-6">
          {/* Employee Information card */}
          <Card className="overflow-hidden">
            <CardHeader className="bg-gray-50 border-b py-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  1
                </span>
                Employee Information
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Employee Name</h3>
                  <p className="text-base text-gray-800">
                    {formData.privacyCase
                      ? 'Privacy Case'
                      : formData.employee || formData.formData?.employeeName || 'Not provided'}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Job Title</h3>
                  <p className="text-base text-gray-800">{formData.formData?.jobTitle || 'Not provided'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Incident Details card */}
          <Card className="overflow-hidden">
            <CardHeader className="bg-gray-50 border-b py-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                Incident Details
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Date of Injury
                  </h3>
                  <p className="text-base text-gray-800">
                    {formData.dateTime || formData.formData?.dateOfInjury
                      ? format(new Date(formData.dateTime || formData.formData?.dateOfInjury), 'MMMM d, yyyy')
                      : 'Not provided'}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    Time of Event
                  </h3>
                  <p className="text-base text-gray-800">
                    {formData.formData?.timeOfEvent
                      ? format(new Date(`2000-01-01T${formData.formData.timeOfEvent}`), 'h:mm a')
                      : 'Not provided'}
                  </p>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  Location
                </h3>
                <p className="text-base text-gray-800">
                  {formData.location || formData.formData?.location || 'Not provided'}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Description</h3>
                <div className="text-base text-gray-800 whitespace-pre-wrap">
                  {formData.formData?.description || 'No description provided'}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nature of Injury card */}
          <Card className="overflow-hidden">
            <CardHeader className="bg-gray-50 border-b py-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                Nature of Injury
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Body Part Affected</h3>
                  <p className="text-base text-gray-800">{formData.formData?.bodyPartAffected || 'Not specified'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Type of Injury</h3>
                  <p className="text-base text-gray-800">
                    {formData.formData?.injuryType || formData.title?.split(': ')[1] || 'Not specified'}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Treatment Location</h3>
                <p className="text-base text-gray-800">{formData.formData?.treatmentLocation || 'Not specified'}</p>
              </div>
            </CardContent>
          </Card>

          {/* OSHA Questions card */}
          <Card className="overflow-hidden">
            <CardHeader className="bg-gray-50 border-b py-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                OSHA Questions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div className="flex items-start gap-3">
                  {/* Get hospitalization status */}
                  {(() => {
                    const wasHospitalized = formData.formData?.wasHospitalized || false;
                    return (
                      <>
                        <div
                          className={`mt-0.5 rounded-full w-5 h-5 flex items-center justify-center ${wasHospitalized ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                        >
                          {wasHospitalized ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Employee was hospitalized?</h3>
                          <p className="text-base text-gray-800">{wasHospitalized ? 'Yes' : 'No'}</p>
                        </div>
                      </>
                    );
                  })()}
                </div>

                <div className="flex items-start gap-3">
                  {/* Get death result status */}
                  {(() => {
                    const resultedInDeath = formData.formData?.resultedInDeath || false;
                    return (
                      <>
                        <div
                          className={`mt-0.5 rounded-full w-5 h-5 flex items-center justify-center ${resultedInDeath ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'}`}
                        >
                          {resultedInDeath ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Incident resulted in death?</h3>
                          <p className="text-base text-gray-800">{resultedInDeath ? 'Yes' : 'No'}</p>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Days Away From Work</h3>
                  <p className="text-base text-gray-800">
                    {formData.formData?.daysAwayFromWork
                      ? `${formData.formData.daysAwayFromWork} ${parseInt(formData.formData.daysAwayFromWork) === 1 ? 'day' : 'days'}`
                      : '0 days'}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Days of Restricted Work Activity</h3>
                  <p className="text-base text-gray-800">
                    {formData.formData?.daysOfRestrictedDuty
                      ? `${formData.formData.daysOfRestrictedDuty} ${parseInt(formData.formData.daysOfRestrictedDuty) === 1 ? 'day' : 'days'}`
                      : '0 days'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Footer with metadata */}
          <div className="mt-8 border-t pt-6">
            <div className="flex flex-wrap justify-between text-sm text-muted-foreground">
              <div>
                <p>Submitted by: {formData.submittedBy || formData.formData?.oshaSubmittedBy || 'Unknown'}</p>
                <p>
                  Date submitted:{' '}
                  {formData.submittedAt
                    ? format(new Date(formData.submittedAt), 'MMMM d, yyyy')
                    : formData.formData?.oshaSubmittedAt
                      ? format(new Date(formData.formData.oshaSubmittedAt), 'MMMM d, yyyy')
                      : 'Not available'}
                </p>
              </div>
              <div>
                <p>OSHA Record ID: {getOshaLogId()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
