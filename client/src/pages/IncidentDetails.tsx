import { ArchiveConfirmationDialog } from '@/components/incidents/details/archive-confirmation-dialog';
import { CommentsSection } from '@/components/incidents/details/comments';
import { IncidentDetailsError } from '@/components/incidents/details/incident-details-error';
import { IncidentDetailsLoading } from '@/components/incidents/details/incident-details-loading';
import { Insight } from '@/components/incidents/details/insight';
import { OshaDetails } from '@/components/incidents/details/osha-details';
import { Timeline } from '@/components/incidents/details/timeline';
import { SeverityBadge } from '@/components/incidents/list/severity-badge';
import { StatusBadge } from '@/components/incidents/list/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import {
  Archive,
  Calendar,
  ChevronLeft,
  Copy,
  Download,
  Edit,
  Mail,
  MapPin,
  MoreVertical,
  Printer,
  Tag,
  User,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

// Main component
export default function IncidentDetails({ params }: { params: { id: string } }) {
  const incidentId = params.id;
  const [_, navigate] = useLocation();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const { hasPermission } = usePermissions();

  const utils = trpc.useUtils();

  const {
    data: incident,
    isLoading,
    error,
  } = trpc.incident.getById.useQuery({
    id: incidentId,
  });

  const { mutateAsync: updateIncident } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: incidentId });
    },
  });

  if (isLoading) {
    return <IncidentDetailsLoading />;
  }

  if (error || !incident) {
    return <IncidentDetailsError />;
  }

  // Format the datetime
  const formattedDate = format(new Date(incident.reportedAt), "MMMM d, yyyy 'at' h:mm a");

  // Determine if OSHA reportable
  const isOshaReportable = incident.oshaReportable;

  // Function to handle archiving/unarchiving an incident
  const handleArchive = async () => {
    try {
      await updateIncident({
        id: incidentId,
        archived: !incident.archived,
      });

      // Show success toast
      toast(incident.archived ? 'Incident Unarchived' : 'Incident Archived', {
        description: incident.archived
          ? `The incident has been restored to the active list with its previous status.`
          : `The incident has been archived with its status preserved as ${incident.status}. It will not appear in the default view.`,
      });

      // Close the confirm dialog
      setShowArchiveConfirm(false);

      if (!incident.archived) {
        navigate(ROUTES.INCIDENT_LIST);
      }
    } catch (error) {
      console.error('Error archiving/unarchiving incident:', error);
      toast('Error', {
        description: `Failed to ${incident.archived ? 'unarchive' : 'archive'} the incident. Please try again.`,
      });
    }
  };

  return (
    <div className="container mx-auto py-4 px-4">
      {/* Archive Confirmation Dialog */}
      <ArchiveConfirmationDialog
        archived={incident?.archived || false}
        showArchiveConfirm={showArchiveConfirm}
        setShowArchiveConfirm={setShowArchiveConfirm}
        handleArchive={handleArchive}
      />

      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" onClick={() => navigate(ROUTES.INCIDENT_LIST)}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Back</span>
        </Button>
      </div>

      {incident && (
        <>
          {/* Incident Header */}
          <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
            {/* Desktop View */}
            <div className="hidden md:block w-full">
              <div className="flex flex-wrap items-center gap-2 mb-1">
                <span className="text-sm font-medium text-muted-foreground">{incident.slug}</span>
                <StatusBadge status={incident.status} />
                <SeverityBadge severity={incident.severity} />
                {isOshaReportable && (
                  <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
                    OSHA Reportable
                  </Badge>
                )}
                <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                  {incident.type === 'incident' ? 'Incident' : 'Near Miss'}
                </Badge>
                {incident.archived && (
                  <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                    <Archive className="h-3 w-3 mr-1" />
                    Archived
                  </Badge>
                )}
              </div>
              <h1 className="text-2xl md:text-3xl font-bold mb-2">{incident.title}</h1>
            </div>

            {/* Mobile View - with menu in title area */}
            <div className="md:hidden w-full">
              <div className="flex flex-wrap items-center gap-2 mb-1">
                <span className="text-sm font-medium text-muted-foreground">{incident.slug}</span>
                <StatusBadge status={incident.status || 'Open'} />
                <SeverityBadge severity={incident.severity} />
                {isOshaReportable && (
                  <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
                    OSHA Reportable
                  </Badge>
                )}
                <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                  {incident.type === 'incident' ? 'Incident' : 'Near Miss'}
                </Badge>
                {incident.archived && (
                  <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                    <Archive className="h-3 w-3 mr-1" />
                    Archived
                  </Badge>
                )}
              </div>

              {/* Mobile title row with menu */}
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">{incident.title}</h1>

                {/* Mobile menu in the title row */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-9 w-9 p-0 flex items-center justify-center"
                      aria-label="Open actions menu"
                    >
                      <MoreVertical className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>Incident Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {hasPermission(
                      MODULES.EHS_INCIDENT,
                      ALLOWED_ACTIONS.EDIT,
                      incident.reportedByUser?.id,
                      incident.status,
                    ) && (
                      <DropdownMenuItem
                        className="h-11 cursor-pointer"
                        onClick={() => navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id))}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Edit Incident</span>
                      </DropdownMenuItem>
                    )}

                    <DropdownMenuSeparator />

                    {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.ARCHIVE, incident.reportedByUser?.id) && (
                      <DropdownMenuItem className="h-11 cursor-pointer" onClick={() => setShowArchiveConfirm(true)}>
                        <Archive className="mr-2 h-4 w-4" />
                        <span>{incident.archived ? 'Unarchive Incident' : 'Archive Incident'}</span>
                      </DropdownMenuItem>
                    )}

                    {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE) && (
                      <DropdownMenuItem
                        className="h-11 cursor-pointer"
                        onClick={() => {
                          toast('Duplicate Report', {
                            description: 'Duplicate functionality coming soon.',
                          });
                        }}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        <span>Duplicate Report</span>
                      </DropdownMenuItem>
                    )}

                    {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.TAG_TEAMMATES) && (
                      <DropdownMenuItem
                        className="h-11 cursor-pointer"
                        onClick={() => {
                          toast('Add Tags', {
                            description: 'Tagging functionality coming soon.',
                          });
                        }}
                      >
                        <Tag className="mr-2 h-4 w-4" />
                        <span>Add Tags</span>
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Desktop buttons - only shown on desktop */}
            <div className="hidden md:flex gap-2 self-start">
              {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EXPORT) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  aria-label="Print incident details"
                  onClick={() => {
                    toast('Print', {
                      description: 'Print functionality will be available soon.',
                    });
                  }}
                >
                  <Printer className="h-4 w-4" />
                  <span>Print</span>
                </Button>
              )}

              {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EXPORT) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  aria-label="Export incident details"
                  onClick={() => {
                    toast('Export', {
                      description: 'Export functionality will be available soon.',
                    });
                  }}
                >
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
              )}

              {hasPermission(
                MODULES.EHS_INCIDENT,
                ALLOWED_ACTIONS.EDIT,
                incident.reportedByUser?.id,
                incident.status,
              ) && (
                <Button size="sm" onClick={() => navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id))}>
                  <Edit className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
              )}

              {/* Desktop-only Options Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.ARCHIVE, incident.reportedByUser?.id) && (
                    <DropdownMenuItem onClick={() => setShowArchiveConfirm(true)}>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>{incident.archived ? 'Unarchive Incident' : 'Archive Incident'}</span>
                    </DropdownMenuItem>
                  )}
                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE) && (
                    <DropdownMenuItem
                      onClick={() => {
                        toast('Duplicate Report', {
                          description: 'Duplicate functionality coming soon.',
                        });
                      }}
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      <span>Duplicate Report</span>
                    </DropdownMenuItem>
                  )}
                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.TAG_TEAMMATES) && (
                    <DropdownMenuItem
                      onClick={() => {
                        toast('Add Tags', {
                          description: 'Tagging functionality coming soon.',
                        });
                      }}
                    >
                      <Tag className="mr-2 h-4 w-4" />
                      <span>Add Tags</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Context bar with metadata */}
          <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1.5" />
              <span>{formattedDate}</span>
            </div>

            <div className="hidden sm:block text-muted-foreground mx-2">•</div>

            <div className="flex items-center">
              <User className="h-4 w-4 mr-1.5" />
              <span>{incident.reportedByUser?.fullName ?? incident.reportedByName}</span>
            </div>
            <div className="hidden sm:block text-muted-foreground mx-2">•</div>

            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-1.5" />
              <span>{incident.reportedByUser?.email ?? incident.reportedByEmail}</span>
            </div>
          </div>

          {/* Main content grid */}
          <div
            className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
              incident.archived ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
            }`}
          >
            {/* Left column - main content */}
            <div className="md:col-span-2 space-y-4">
              {/* AI Safety Insight - Enhanced */}
              <Insight incident={incident} />

              {/* OSHA Submission Banner - Show green success banner if submitted, yellow alert if not */}
              {incident.oshaReportable && <OshaDetails incident={incident} />}

              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Description</CardTitle>
                </CardHeader>
                <CardContent>{incident.description || 'No description provided.'}</CardContent>
              </Card>

              {/* Media */}
              {incident.media && incident.media.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Media</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Carousel className="w-full">
                      <CarouselContent>
                        {incident.media.map((file, index) => (
                          <CarouselItem key={index} className="basis-full sm:basis-1/2 md:basis-1/3">
                            <div className="overflow-hidden rounded-md bg-muted h-40 flex items-center justify-center relative">
                              <img src={file.url} alt={file.name} className="h-full w-full object-cover" />
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <CarouselPrevious className="left-2" />
                      <CarouselNext className="right-2" />
                    </Carousel>
                  </CardContent>
                </Card>
              )}

              {/* Immediate Actions */}
              {incident.immediateActions && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Immediate Actions Taken</CardTitle>
                  </CardHeader>
                  <CardContent>{incident.immediateActions}</CardContent>
                </Card>
              )}

              {/* Root Cause */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Root Cause</CardTitle>
                </CardHeader>
                <CardContent>
                  {incident.rootCause ? (
                    <Badge className="capitalize px-2 py-1" variant="outline">
                      {incident.rootCause.replace(/_/g, ' ')}
                    </Badge>
                  ) : (
                    <p className="text-muted-foreground">Not provided</p>
                  )}
                </CardContent>
              </Card>

              {/* Comments Section - Moved to match CAPA view layout */}
              {hasPermission(MODULES.EHS_INCIDENT, 'comment') && (
                <CommentsSection entityId={incident.id} entityType="incident" />
              )}
            </div>

            {/* Right column - metadata & timeline */}
            <div className="space-y-4">
              {incident.location && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Location</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2 mb-4">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{incident.location?.name}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <p className="text-sm font-medium mb-2">Linked Assets</p>
                      <div className="flex flex-wrap items-center gap-2">
                        {incident.assets.map((asset) => (
                          <Badge variant="outline" key={asset.id}>
                            {asset.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
              {hasPermission(MODULES.EHS_INCIDENT, 'view-audit-trail') && (
                <Timeline entityId={incidentId} entityType="incident" />
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
