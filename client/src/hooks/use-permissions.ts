import { useAppContext } from '@/contexts/app-context';
import { PERMISSION_LEVELS, MODULES, ALLOWED_ACTIONS } from '@shared/user-permissions';

type PermissionModule = typeof MODULES[keyof typeof MODULES];
type PermissionAction = typeof ALLOWED_ACTIONS[keyof typeof ALLOWED_ACTIONS];

export const usePermissions = () => {
  const { user } = useAppContext();

  const hasPermission = (
    module: PermissionModule,
    action: PermissionAction,
    resourceOwnerId?: string,
    resourceStatus?: string
  ): boolean => {
    if (!user?.permissions) return false;

    const permission = user.permissions.find((p) => p.dataObject === module && p.action === action);

    if (!permission) return false;

    if (action === 'edit') {
      const isAdmin = user?.userAccountType === '1';
      if (isAdmin) return true;
      if (!resourceOwnerId || !resourceStatus) return false;
      return (
        permission.permissionLevel === PERMISSION_LEVELS.FULL ||
        (permission.permissionLevel === PERMISSION_LEVELS.PARTIAL && resourceOwnerId === user.id && resourceStatus === 'open')
      );
    }

    if (action === 'export') {
      const isAdmin = user?.userAccountType === '1';
      if (isAdmin) return true;
      return (
        permission.permissionLevel === PERMISSION_LEVELS.FULL ||
        (permission.permissionLevel === PERMISSION_LEVELS.PARTIAL && resourceOwnerId === user.id)
      );
    }

    if (action === 'create') {
      return permission.permissionLevel === PERMISSION_LEVELS.FULL;
    }

    if (permission.permissionLevel === PERMISSION_LEVELS.FULL) {
      return true;
    }

    if (permission.permissionLevel === PERMISSION_LEVELS.PARTIAL) {
      if (resourceOwnerId) {
        return resourceOwnerId === user.id;
      }
      return true;
    }

    return false;
  };

  return { hasPermission };
};
