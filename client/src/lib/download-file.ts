export const handleDownload = async (
  url: string,
  fileName: string,
  toast?: (msg: string, options?: unknown) => void,
) => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Error downloading file:', error);
    if (toast) {
      toast('Error downloading file', {
        description: 'There was a problem downloading the file. Please try again.',
      });
    }
  }
};
