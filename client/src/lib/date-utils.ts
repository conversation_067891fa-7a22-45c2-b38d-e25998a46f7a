/**
 * Helper function to parse API date as local time (preserve the time value)
 * 
 * This function takes a date input and adjusts it to preserve the time value
 * in the user's local timezone, preventing timezone conversion issues.
 * 
 * @param dateInput - The date to parse (string or Date object)
 * @returns Date object adjusted to local time
 */
export const parseAsLocalTime = (dateInput: string | Date): Date => {
  const dateString = typeof dateInput === 'string' ? dateInput : dateInput.toISOString();
  
  const date = new Date(dateString);
  // Get the user's timezone offset in minutes (e.g., -180 for UTC-3)
  const timezoneOffsetMinutes = date.getTimezoneOffset();

  // Add that offset to the UTC time
  const adjustedDate = new Date(date.getTime() + timezoneOffsetMinutes * 60 * 1000);
  return adjustedDate;
}; 