import { createTRPCReact, httpBatchLink, httpLink, loggerLink, splitLink } from '@trpc/react-query';
import type { AppRouter } from '@server/trpc/router';
import superjson from 'superjson';

export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    loggerLink({
      enabled: (op) =>
        process.env.NODE_ENV === 'development' ||
        process.env.NODE_ENV === 'test' ||
        (op.direction === 'down' && op.result instanceof Error),
    }),
    splitLink({
      condition: (op) => op.path === 'user.me',
      true: httpLink({
        url: '/ehs/trpc',
        transformer: superjson,
      }),
      false: httpBatchLink({
        url: '/ehs/trpc',
        transformer: superjson,
      }),
    }),
  ],
});
