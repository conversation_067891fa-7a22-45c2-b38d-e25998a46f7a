@import 'react-day-picker/dist/style.css' layer(base);

@import 'tailwindcss';

@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

@theme {
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary-300: #e2f2ff;
  --color-primary-500: #0c6ff9;
  --color-primary-700: #0954bd;
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive-300: #ffeaeb;
  --color-destructive-500: #e01e5a;
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-success-300: #e6f6e7;
  --color-success-500: #00875a;

  --color-warning-500: #ec7d10;

  --color-caution-500: #f3ca40;

  --color-neutral-100: #fafafa;
  --color-neutral-300: #f2f2f2;
  --color-neutral-500: #dfe3e8;
  --color-neutral-700: #b8b8b8;
  --color-neutral-900: #767676;
  --color-neutral-black: #323233;
  --color-neutral-white: #ffffff;

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer utilities {
  .rdp {
    --rdp-cell-size: 32px;
    --rdp-accent-color: #0c6ff9;
    --rdp-accent-color-dark: #065ecb;
    --rdp-accent-color-hover: rgba(12, 111, 249, 0.2);
    --rdp-background-color: #e7edff;
  }

  .rdp-day_selected,
  .rdp-day_selected:focus-visible,
  .rdp-day_selected:hover {
    background-color: var(--rdp-accent-color);
    color: white;
  }

  /* AI flagged CAPA row highlight */
  .ai-flagged-row {
    background-color: #fef3f3;
    border-left: 2px solid #fca5a5;
  }

  /* Sparkle animations for AI Coach */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  @keyframes pulse-glow {
    0% {
      opacity: 0.6;
      transform: scale(0.95);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
    100% {
      opacity: 0.6;
      transform: scale(0.95);
    }
  }

  @keyframes pulse-subtle {
    0% {
      border-color: rgba(251, 146, 60, 0.5);
    }
    50% {
      border-color: rgba(251, 146, 60, 0.9);
    }
    100% {
      border-color: rgba(251, 146, 60, 0.5);
    }
  }

  .sparkle-float {
    animation: float 6s ease-in-out infinite;
  }

  .sparkle-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s ease-in-out infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}