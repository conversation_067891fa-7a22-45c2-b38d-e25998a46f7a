@import "tailwindcss";
@import "tw-animate-css";
@import "@radix-ui/colors/indigo.css";
@import "@radix-ui/colors/sky.css";
@import "@radix-ui/colors/green.css";
@import "@radix-ui/colors/amber.css";
@import "@radix-ui/colors/red.css";
@import "@radix-ui/colors/slate.css";
@import "@radix-ui/colors/indigo-dark.css";
@import "@radix-ui/colors/sky-dark.css";
@import "@radix-ui/colors/green-dark.css";
@import "@radix-ui/colors/amber-dark.css";
@import "@radix-ui/colors/red-dark.css";
@import "@radix-ui/colors/slate-dark.css";
@import "@radix-ui/colors/indigo-alpha.css";
@import "@radix-ui/colors/slate-alpha.css";
@import "@radix-ui/colors/indigo-dark-alpha.css";
@import "@radix-ui/colors/slate-dark-alpha.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --panel-bg-solid: #ffffff;
  --panel-bg-translucent: rgba(255, 255, 255, 0.6);
  --overlay-scrim: rgba(0, 0, 0, 0.6);

  /* Background and foreground */
  --background: var(--slate-1);
  --foreground: var(--slate-12);

  /* Card and popover */
  --card: var(--slate-1);
  --card-foreground: var(--slate-12);
  --popover: var(--slate-1);
  --popover-foreground: var(--slate-12);

  /* Primary colors (Indigo) */
  --primary: var(--indigo-9);
  --primary-foreground: white;

  /* Secondary colors */
  --secondary: var(--slate-3);
  --secondary-foreground: var(--slate-12);

  /* Muted colors */
  --muted: var(--slate-3);
  --muted-foreground: var(--slate-10);

  /* Accent colors */
  --accent: var(--slate-3);
  --accent-foreground: var(--slate-12);

  /* Destructive colors */
  --destructive: var(--red-9);

  /* Border and input colors */
  --border: var(--slate-6);
  --input: var(--slate-6);
  --ring: var(--slate-8);

  /* Chart colors */
  --chart-1: var(--indigo-9);
  --chart-2: var(--sky-9);
  --chart-3: var(--green-9);
  --chart-4: var(--amber-9);
  --chart-5: var(--red-9);

  /* Sidebar colors */
  --sidebar: var(--slate-2);
  --sidebar-foreground: var(--slate-12);
  --sidebar-primary: var(--indigo-9);
  --sidebar-primary-foreground: white;
  --sidebar-accent: var(--slate-3);
  --sidebar-accent-foreground: var(--slate-12);
  --sidebar-border: var(--slate-6);
  --sidebar-ring: var(--slate-8);
}

.dark {
  --panel-bg-solid: #141726;
  --panel-bg-translucent: rgba(20, 23, 38, 0.6);
  --overlay-scrim: rgba(0, 0, 0, 0.8);

  /* Background and foreground */
  --background: var(--slate-1);
  --foreground: var(--slate-12);

  /* Card and popover */
  --card: var(--slate-2);
  --card-foreground: var(--slate-12);
  --popover: var(--slate-2);
  --popover-foreground: var(--slate-12);

  /* Primary colors (Indigo) */
  --primary: var(--indigo-9);
  --primary-foreground: white;

  /* Secondary colors */
  --secondary: var(--slate-4);
  --secondary-foreground: var(--slate-12);

  /* Muted colors */
  --muted: var(--slate-4);
  --muted-foreground: var(--slate-9);

  /* Accent colors */
  --accent: var(--slate-4);
  --accent-foreground: var(--slate-12);

  /* Destructive colors */
  --destructive: var(--red-9);

  /* Border and input colors */
  --border: var(--slate-alpha-7);
  --input: var(--slate-alpha-7);
  --ring: var(--slate-8);

  /* Chart colors */
  --chart-1: var(--indigo-9);
  --chart-2: var(--sky-9);
  --chart-3: var(--green-9);
  --chart-4: var(--amber-9);
  --chart-5: var(--red-9);

  /* Sidebar colors */
  --sidebar: var(--slate-2);
  --sidebar-foreground: var(--slate-12);
  --sidebar-primary: var(--indigo-9);
  --sidebar-primary-foreground: white;
  --sidebar-accent: var(--slate-4);
  --sidebar-accent-foreground: var(--slate-12);
  --sidebar-border: var(--slate-alpha-7);
  --sidebar-ring: var(--slate-8);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
