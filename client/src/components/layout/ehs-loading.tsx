export const EhsLoading = () => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/90 backdrop-blur-sm">
      <div className="flex flex-col items-center space-y-6">
        {/* Safety-themed multi-layer spinner */}
        <div className="relative flex items-center justify-center">
          {/* Outer ring - Warning/Caution */}
          <div className="w-16 h-16 border-4 border-amber-200 border-t-amber-500 border-r-orange-500 border-b-transparent border-l-transparent rounded-full animate-spin"></div>

          {/* Middle ring - Success/Safety */}
          <div className="absolute w-12 h-12 border-4 border-emerald-200 border-t-emerald-500 border-l-emerald-500 border-b-transparent border-r-transparent rounded-full animate-spin [animation-duration:0.8s] [animation-direction:reverse]"></div>

          {/* Inner core - Primary/Professional */}
          <div className="absolute w-8 h-8 border-4 border-blue-200 border-t-blue-500 border-b-transparent border-l-transparent border-r-transparent rounded-full animate-spin [animation-duration:1.2s]"></div>

          {/* Center safety symbol */}
          <div className="absolute w-4 h-4 rounded-full bg-gradient-to-br from-amber-500 to-emerald-500 animate-pulse"></div>
        </div>

        {/* EHS Loading text with professional styling */}
        <div className="text-center space-y-2">
          <div className="font-semibold text-base tracking-wide text-gray-900">Environmental Health & Safety</div>
          <div className="font-medium text-sm text-gray-700 animate-pulse">Loading incident management...</div>
        </div>

        {/* Professional progress indicator */}
        <div className="w-32 h-1 bg-gray-300 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-amber-500 via-emerald-500 to-blue-500 rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};
