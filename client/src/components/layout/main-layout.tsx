import { EhsLoading } from '@/components/layout/ehs-loading';
import { Navbar } from '@/components/layout/navbar';
import { Sidebar } from '@/components/layout/sidebar';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { useAppContext } from '@/contexts/app-context';
import { useIsMobile } from '@/hooks/use-mobile';
import type React from 'react';
import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [location, navigate] = useLocation();
  const { isLoading, isError, isSuccess, user } = useAppContext();

  useEffect(() => {
    if (isError && !isLoading) {
      console.error(isError);
      window.location.replace(ROUTES.LOGIN);
    }

    if (isSuccess && !user?.featureFlags.webEHS) {
      window.location.replace(ROUTES.UPKEEP_WORK_ORDERS);
    }
  }, [isError, isLoading, isSuccess, user]);

  useEffect(() => {
    if (location === ROUTES.BASE) {
      navigate(ROUTES.INCIDENT_LIST);
    }
  }, [location, navigate]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (isMobile && sidebarOpen) {
        const target = e.target as HTMLElement;
        if (!target.closest('.sidebar') && !target.closest('.menu-trigger')) {
          setSidebarOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, sidebarOpen]);

  return (
    <div className="h-screen flex flex-row bg-gray-50">
      {/* Global Loading Overlay */}
      {isLoading && <EhsLoading />}

      <div
        className={`sidebar ${isMobile ? 'fixed z-30 inset-y-0 left-0 transform transition duration-200 ease-in-out' : ''} ${
          isMobile && sidebarOpen ? 'translate-x-0' : isMobile ? '-translate-x-full' : ''
        }`}
      >
        <Sidebar onCloseMobile={() => setSidebarOpen(false)} />
      </div>

      {/* Mobile overlay when sidebar is open */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-20" onClick={() => setSidebarOpen(false)} />
      )}

      <div className="flex-1 flex flex-col overflow-hidden h-full">
        <Navbar onMenuClick={() => setSidebarOpen(!sidebarOpen)} isMobile={isMobile} />
        <div className="relative flex-1 overflow-hidden">
          <main className="absolute inset-0 bg-gray-50 overflow-y-auto">{children}</main>
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
