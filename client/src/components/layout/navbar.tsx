import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { cn } from '@/lib/utils';
import { AlertTriangle, ClipboardCheck, Menu, Plus, QrCode } from 'lucide-react';
import { useLocation } from 'wouter';

interface TopNavbarProps {
  onMenuClick?: () => void;
  isMobile?: boolean;
}

export const Navbar = ({ onMenuClick, isMobile = false }: TopNavbarProps) => {
  const [location, navigate] = useLocation();

  // Set page title based on the current location
  const getPageTitle = () => {
    if (location.includes(ROUTES.INCIDENT_NEW)) {
      return 'Report Safety Issue';
    }

    return 'UpKeep EHS';
  };

  return (
    <header className="bg-white border-b border-gray-200 py-3 px-4 sm:px-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isMobile && (
            <Button variant="ghost" size="icon" className="menu-trigger -ml-1 mr-1" onClick={onMenuClick}>
              <Menu className="w-5 h-5 text-gray-700" />
            </Button>
          )}
          <h1 className="text-xl font-semibold text-gray-800 truncate max-w-[200px] sm:max-w-none">{getPageTitle()}</h1>
        </div>

        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" className={cn(isMobile ? 'w-9 h-9 p-0' : 'px-4 py-2 h-auto')}>
                <Plus className={isMobile ? 'w-5 h-5' : 'mr-1 w-4 h-4'} />
                {!isMobile && 'Create'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-52 rounded-lg p-1 shadow-lg border-gray-200">
              <DropdownMenuItem
                className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                onClick={() => navigate(ROUTES.INCIDENT_NEW)}
              >
                <AlertTriangle className="mr-2 h-4 w-4 text-primary-500" />
                Report Safety Issue
              </DropdownMenuItem>
              <DropdownMenuItem
                className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                onClick={() => {
                  // Force navigation even if already on access points page
                  navigate(ROUTES.ACCESS_POINTS_LIST, { replace: true });
                  // Use setTimeout to ensure navigation completes before navigating to new
                  setTimeout(() => {
                    navigate(ROUTES.ACCESS_POINTS_NEW);
                  }, 0);
                }}
              >
                <QrCode className="mr-2 h-4 w-4 text-primary-500" />
                Create Access Point
              </DropdownMenuItem>
              <DropdownMenuItem
                className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                onClick={() => navigate(ROUTES.CAPA_NEW)}
              >
                <ClipboardCheck className="mr-2 h-4 w-4 text-primary-500" />
                Create CAPA
              </DropdownMenuItem>

              {/* <DropdownMenuItem
                className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                onClick={() => navigate({ to: "/osha-form-301" })}
              >
                <PenLine className="mr-2 h-4 w-4 text-primary-500" />
                OSHA Form 301 (Blank)
              </DropdownMenuItem> */}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};
