import { SeverityBadge } from '@/components/incidents/list/severity-badge';
import { StatusBadge } from '@/components/incidents/list/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Download, Flag, Info, MapPin, MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const IncidentsTable = ({
  incidents,
  activeFilterCount,
  resetFilters,
}: {
  incidents: RouterOutputs['incident']['list'];
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  const [_, navigate] = useLocation();
  const { hasPermission } = usePermissions();

  const utils = trpc.useUtils();

  const { mutateAsync: updateIncident, error: updateIncidentError } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.list.invalidate();
    },
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Report ID & Title</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
            <TableHead className="w-[110px]">Status</TableHead>
            <TableHead className="w-[110px]">Severity</TableHead>
            <TableHead>Location</TableHead>
            <TableHead className="w-[130px]">Date & Time</TableHead>
            <TableHead className="text-center">OSHA</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {incidents.data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={10} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center">
                  <Info className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No incidents found</p>
                  {activeFilterCount > 0 && (
                    <Button variant="link" onClick={resetFilters} className="mt-2">
                      Clear filters
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ) : (
            incidents.data.map((incident: RouterOutputs['incident']['list']['data'][number]) => {
              return (
                <TableRow
                  key={incident.id}
                  className={`cursor-pointer hover:bg-muted/50 ${
                    incident.archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''
                  }`}
                  onClick={() => navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id))}
                >
                  <TableCell>
                    <div>
                      <div className="flex items-center gap-1">
                        <span className="font-medium">{incident.slug}</span>
                        {incident.archived && (
                          <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                            <Archive className="h-3 w-3 mr-1" />
                            Archived
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground line-clamp-1">{incident.title}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={`
                    ${
                      incident.type === 'incident'
                        ? 'bg-blue-50 text-blue-700 border-blue-200'
                        : 'bg-amber-50 text-amber-700 border-amber-200'
                    } 
                    font-medium capitalize whitespace-nowrap`}
                      variant="outline"
                    >
                      {incident.type === 'incident' ? 'Incident' : 'Near Miss'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={incident.status} />
                  </TableCell>
                  <TableCell>
                    <SeverityBadge severity={incident.severity} />
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            <span className="truncate max-w-[150px]">{incident.location?.name || 'No location'}</span>
                          </div>
                        </TooltipTrigger>
                        {incident.location && (
                          <TooltipContent>
                            <p>{incident.location?.name}</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>{format(new Date(incident.reportedAt), 'MMM d, yyyy h:mm a')}</TableCell>

                  <TableCell align="center">
                    {incident.oshaReportable ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge
                              className="bg-red-50 text-red-600 border-red-200 h-6 w-6 p-0 flex items-center justify-center"
                              variant="outline"
                            >
                              <Flag className="h-3 w-3" />
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>OSHA Reportable</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <span className="text-muted-foreground">No</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id))}>
                          <Info className="mr-2 h-4 w-4" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                        {hasPermission(
                          MODULES.EHS_INCIDENT,
                          ALLOWED_ACTIONS.EDIT,
                          incident.reportedBy ?? undefined,
                          incident.status,
                        ) && (
                          <DropdownMenuItem onClick={() => navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id))}>
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>Edit</span>
                          </DropdownMenuItem>
                        )}
                        {hasPermission(
                          MODULES.EHS_INCIDENT,
                          ALLOWED_ACTIONS.EXPORT,
                          incident.reportedBy ?? undefined,
                        ) && (
                          <DropdownMenuItem
                            onClick={() => {
                              toast('Export', {
                                description: 'Export functionality coming soon.',
                              });
                            }}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            <span>Export</span>
                          </DropdownMenuItem>
                        )}
                        {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.ARCHIVE) && (
                          <DropdownMenuItem
                            onClick={() => {
                              updateIncident({
                                id: incident.id,
                                archived: !incident.archived,
                              });
                            }}
                          >
                            <Archive className="mr-2 h-4 w-4" />
                            <span>{incident.archived ? 'Unarchive' : 'Archive'}</span>
                          </DropdownMenuItem>
                        )}
                        {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.DELETE) && (
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => {
                              toast('Delete', {
                                description: 'Delete functionality coming soon.',
                              });
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
};
