import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { STATUS_MAP, type Location } from '@shared/schema.types';
import { Archive, Filter, Flag } from 'lucide-react';

type Filters = {
  status: (typeof statusEnum.enumValues)[number][];
  type: (typeof reportTypeEnum.enumValues)[number][];
  severity: (typeof severityEnum.enumValues)[number][];
  locationIds: string[];
  oshaReportable: boolean | null;
  includeArchived: boolean;
};

export const MobileFilters = ({
  toggleFilter,
  filters,
  toggleOshaFilter,
  setFilters,
  activeFilterCount,
  resetFilters,
  locations,
}: {
  toggleFilter: (type: 'status' | 'type' | 'severity' | 'locationIds', value: any) => void;
  filters: Filters;
  toggleOshaFilter: () => void;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  locations: Location[] | undefined;
}) => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" className="ml-2 md:hidden">
          <Filter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent side="right">
        <SheetHeader>
          <SheetTitle>Filter Incidents</SheetTitle>
          <SheetDescription>Apply filters to narrow down your incident list.</SheetDescription>
        </SheetHeader>

        <div className="p-4 space-4 flex flex-col gap-4">
          <div>
            <h3 className="font-medium mb-2">Status</h3>
            <div className="space-y-2">
              {statusEnum.enumValues.map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={status}
                    checked={filters.status.includes(status)}
                    onCheckedChange={() => toggleFilter('status', status)}
                  />
                  <label htmlFor={status}>{STATUS_MAP[status]}</label>
                </div>
              ))}
            </div>
          </div>

          {/* Type Filter */}
          <div>
            <h3 className="font-medium mb-2">Type</h3>
            <div className="space-y-2">
              {reportTypeEnum.enumValues.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={type}
                    checked={filters.type.includes(type)}
                    onCheckedChange={() => toggleFilter('type', type)}
                  />
                  <label className="capitalize" htmlFor={type}>
                    {type.replace('_', ' ')}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Location Filter */}
          <div>
            <h3 className="font-medium mb-2">Location</h3>
            <div className="space-y-2">
              {locations?.map((location) => (
                <div key={location.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={location.id}
                    checked={filters.locationIds.includes(location.id)}
                    onCheckedChange={() => toggleFilter('locationIds', location.id)}
                  />
                  <label className="capitalize" htmlFor={location.id}>
                    {location.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Severity Filter */}
          <div>
            <h3 className="font-medium mb-2">Severity</h3>
            <div className="space-y-2">
              {severityEnum.enumValues.map((severity) => (
                <div key={severity} className="flex items-center space-x-2">
                  <Checkbox
                    id={severity}
                    checked={filters.severity.includes(severity)}
                    onCheckedChange={() => toggleFilter('severity', severity)}
                  />
                  <span className="capitalize">{severity}</span>
                </div>
              ))}
            </div>
          </div>

          {/* OSHA Reportable Filter */}
          <div className="flex flex-col">
            <h3 className="font-medium mb-2">OSHA Reportable</h3>
            <Button
              variant={filters.oshaReportable !== null ? 'default' : 'outline'}
              size="sm"
              onClick={toggleOshaFilter}
              className="justify-start"
            >
              <Flag className="h-4 w-4 mr-2" />
              OSHA Reportable
              {filters.oshaReportable !== null && (
                <span className="ml-2 text-xs">({filters.oshaReportable ? 'Yes' : 'No'})</span>
              )}
            </Button>
          </div>

          {/* Include Archived Filter */}
          <div className="flex flex-col">
            <h3 className="font-medium mb-2">Archive Status</h3>
            <Button
              variant={filters.includeArchived ? 'default' : 'outline'}
              size="sm"
              onClick={() =>
                setFilters((prev) => ({
                  ...prev,
                  includeArchived: !prev.includeArchived,
                }))
              }
              className={`justify-start ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
            >
              <Archive className="h-4 w-4 mr-2" />
              Include Archived
            </Button>
          </div>
        </div>

        <SheetFooter className="pt-4">
          <SheetClose asChild>
            <Button variant="outline" className="w-full" onClick={resetFilters}>
              Reset All Filters
            </Button>
          </SheetClose>
          <SheetClose asChild>
            <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
