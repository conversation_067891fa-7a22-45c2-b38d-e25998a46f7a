import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { STATUS_MAP, Location } from '@shared/schema.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

type Filters = {
  status: (typeof statusEnum.enumValues)[number][];
  type: (typeof reportTypeEnum.enumValues)[number][];
  severity: (typeof severityEnum.enumValues)[number][];
  oshaReportable: boolean | null;
  includeArchived: boolean;
  locationIds: string[];
};

export const Filters = ({
  toggleFilter,
  filters,
  toggleOshaFilter,
  setFilters,
  activeFilterCount,
  resetFilters,
  locations,
}: {
  toggleFilter: (type: 'status' | 'type' | 'severity' | 'locationIds', value: any) => void;
  filters: Filters;
  toggleOshaFilter: () => void;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  locations: Location[] | undefined;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {statusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                onSelect={() => toggleFilter('status', status)}
                checked={filters.status.includes(status)}
              >
                {STATUS_MAP[status]}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {reportTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('type', type);
                }}
                checked={filters.type.includes(type)}
              >
                <span className="capitalize">{type.replace('_', ' ')}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Severity Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Severity
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {severityEnum.enumValues.map((severity) => (
              <DropdownMenuCheckboxItem
                key={severity}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('severity', severity);
                }}
                checked={filters.severity.includes(severity)}
              >
                <label className="capitalize">{severity}</label>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Location
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {locations?.map((location) => (
              <DropdownMenuCheckboxItem
                key={location.id}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('locationIds', location.id);
                }}
                checked={filters.locationIds?.includes(location.id)}
              >
                <label className="capitalize">{location.name}</label>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* OSHA Reportable Filter */}
        <Button variant={filters.oshaReportable !== null ? 'default' : 'outline'} size="sm" onClick={toggleOshaFilter}>
          OSHA Reportable
          {filters.oshaReportable !== null && (
            <span className="ml-2 text-xs">({filters.oshaReportable ? 'Yes' : 'No'})</span>
          )}
        </Button>

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Reset
          </Button>
        )}
      </div>
    </div>
  );
};
