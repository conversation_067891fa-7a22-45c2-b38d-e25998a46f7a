import { SeverityBadge } from '@/components/incidents/list/severity-badge';
import { StatusBadge } from '@/components/incidents/list/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, Info, MapPin, MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const IncidentsMobileView = ({
  incidents,
  activeFilterCount,
  resetFilters,
}: {
  incidents: RouterOutputs['incident']['list'];
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  const [_, navigate] = useLocation();
  const { hasPermission } = usePermissions();
  const utils = trpc.useUtils();

  const { mutateAsync: updateIncident, error: updateIncidentError } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.list.invalidate();
    },
  });

  // Render mobile card view
  const renderMobileCard = (incident: RouterOutputs['incident']['list']['data'][number]) => {
    return (
      <Card
        key={incident.id}
        className={`mb-4 cursor-pointer transition-colors hover:bg-muted/50 ${
          incident.archived ? 'bg-amber-50/50 hover:bg-amber-50/80 border-amber-200' : ''
        }`}
        onClick={() => navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id))}
      >
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center">
              <p className="font-medium text-sm mr-2">{incident.slug}</p>
              {incident.oshaReportable && (
                <Badge className="bg-red-50 text-red-600 border-red-200 mr-1" variant="outline">
                  OSHA
                </Badge>
              )}
              {incident.archived && (
                <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                  <Archive className="h-3 w-3 mr-1" />
                  Archived
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              <StatusBadge status={incident.status} />
              <SeverityBadge severity={incident.severity} />
            </div>
          </div>

          <h3 className="font-medium mb-2 line-clamp-2">{incident.title}</h3>

          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-center mb-1">
              <Badge className="bg-slate-50 text-slate-700 mr-2 capitalize whitespace-nowrap" variant="outline">
                {incident.type === 'incident' ? 'Incident' : 'Near Miss'}
              </Badge>
              <span className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {format(new Date(incident.reportedAt), 'MMM d, yyyy')}
              </span>
            </div>

            <div className="flex items-center mb-1">
              <MapPin className="h-3 w-3 mr-1" />
              {incident?.location?.name || 'No location'}
            </div>

            {/* {linkedCapa && (
              <div className="flex items-center text-blue-600 font-medium">
                <ClipboardCheck className="h-3 w-3 mr-1" />
                {linkedCapa}
              </div>
            )} */}
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                className="h-8 px-2 rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id));
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="h-8 px-2 rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id));
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-2 rounded-full"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.ARCHIVE) && (
                    <DropdownMenuItem
                      className={incident.archived ? 'text-amber-600' : 'text-red-600'}
                      onClick={async (e) => {
                        e.stopPropagation();
                        await updateIncident({
                          id: incident.id,
                          archived: !incident.archived,
                        });
                        if (updateIncidentError) {
                          toast('Action Failed', {
                            description: `Could not ${incident.archived ? 'unarchive' : 'archive'} the incident. Please try again.`,
                          });
                        } else {
                          toast(incident.archived ? 'Incident Unarchived' : 'Incident Archived', {
                            description: incident.archived
                              ? `${incident.slug} has been restored to its previous status.`
                              : `${incident.slug} has been archived.`,
                          });
                        }
                      }}
                    >
                      {incident.archived ? (
                        <>
                          <Archive className="h-4 w-4 mr-2" />
                          Unarchive
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Archive
                        </>
                      )}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };
  return (
    <div className="space-y-4">
      {incidents.data.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Info className="h-12 w-12 text-muted-foreground mb-3" />
          <p className="text-muted-foreground text-center mb-2">No incidents found</p>
          {activeFilterCount > 0 && (
            <Button variant="link" onClick={resetFilters}>
              Clear filters
            </Button>
          )}
        </div>
      ) : (
        incidents.data.map((incident) => renderMobileCard(incident))
      )}
    </div>
  );
};
