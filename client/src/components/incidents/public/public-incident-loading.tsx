import { <PERSON>, <PERSON><PERSON>he<PERSON>, AlertTriangle } from 'lucide-react';
import { motion } from 'motion/react';

export function PublicIncidentLoading() {
  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-8">
        {/* Animated Loading Icon */}
        <div className="relative">
          {/* Outer rotating ring */}
          <motion.div
            className="w-20 h-20 border-4 border-blue-200 dark:border-blue-800 rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          />

          {/* Inner loading ring */}
          <motion.div
            className="absolute inset-2 w-16 h-16 border-4 border-t-blue-600 dark:border-t-blue-400 border-r-transparent border-b-transparent border-l-transparent rounded-full"
            animate={{ rotate: -360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          />

          {/* Center safety icon */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0.7 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 1, repeat: Infinity, repeatType: 'reverse' }}
            >
              <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </motion.div>
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-4">
          <motion.h2
            className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Preparing Your Safety Report Form
          </motion.h2>

          <motion.p
            className="text-lg text-muted-foreground max-w-md"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            Verifying access permissions and loading incident reporting capabilities...
          </motion.p>
        </div>

        {/* Loading Steps */}
        <motion.div
          className="w-full max-w-md space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="flex items-center space-x-3 text-sm text-muted-foreground">
            <motion.div
              className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8 }}
            >
              <ShieldCheck className="w-3 h-3 text-green-600 dark:text-green-400" />
            </motion.div>
            <span>Validating access credentials</span>
            <motion.div
              className="flex-1 h-0.5 bg-green-200 dark:bg-green-800 rounded"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: 0.8, duration: 0.5 }}
              style={{ transformOrigin: 'left' }}
            />
          </div>

          <div className="flex items-center space-x-3 text-sm text-muted-foreground">
            <motion.div
              className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.0 }}
            >
              <motion.div
                className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
            </motion.div>
            <span>Loading safety protocols</span>
            <motion.div
              className="flex-1 h-0.5 bg-blue-200 dark:bg-blue-800 rounded"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 0.7 }}
              transition={{ delay: 1.0, duration: 0.8 }}
              style={{ transformOrigin: 'left' }}
            />
          </div>

          <div className="flex items-center space-x-3 text-sm text-muted-foreground">
            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <div className="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full" />
            </div>
            <span>Initializing incident form</span>
            <div className="flex-1 h-0.5 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        </motion.div>

        {/* Safety Message */}
        <motion.div
          className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-lg"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.2 }}
        >
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-left">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Safety First</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Your safety is our priority. This secure form helps us track and prevent incidents to keep everyone
                safe.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Loading Dots */}
        <motion.div
          className="flex space-x-1"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.4 }}
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"
              animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
              }}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}
