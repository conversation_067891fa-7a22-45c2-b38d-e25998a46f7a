import { Loader2, Spark<PERSON> } from 'lucide-react';
import { motion } from 'motion/react';

export const AnalyzingLoading = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="p-4 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg"
    >
      <div className="flex items-center gap-3">
        <Loader2 className="h-5 w-5 animate-spin text-indigo-600" />
        <div className="flex-1">
          <p className="text-sm font-medium text-indigo-900">Analyzing your voice input...</p>
          <p className="text-xs text-indigo-700">AI is processing your incident report and filling out the form</p>
        </div>
        <Sparkles className="h-5 w-5 text-indigo-500" />
      </div>
    </motion.div>
  );
};
