import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { ArrowLeft } from 'lucide-react';
import { useLocation } from 'wouter';

export const EditIncidentError = () => {
  const [_, navigate] = useLocation();
  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div>
        <div className="flex items-center gap-2 mb-6">
          <Button variant="ghost" onClick={() => window.history.back()} className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Error Loading Incident</CardTitle>
            <CardDescription>
              We couldn't load the incident data. Please try again or go back to the incident list.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate(ROUTES.INCIDENT_LIST)}>Return to Incident Log</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
