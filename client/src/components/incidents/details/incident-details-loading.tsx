import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft } from 'lucide-react';

export function IncidentDetailsLoading() {
  return (
    <div className="container mx-auto py-4 px-4">
      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" className="pl-0 hover:pl-0 hover:bg-transparent" disabled>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Incident Log
        </Button>
      </div>

      {/* Incident Header */}
      <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
        <div className="w-full">
          {/* Badges row */}
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <Skeleton className="h-5 w-20" /> {/* Slug */}
            <Skeleton className="h-6 w-16" /> {/* Status badge */}
            <Skeleton className="h-6 w-20" /> {/* Severity badge */}
            <Skeleton className="h-6 w-28" /> {/* Type badge */}
          </div>
          {/* Title */}
          <Skeleton className="h-8 md:h-10 w-3/4 mb-2" />
        </div>

        {/* Desktop buttons */}
        <div className="hidden md:flex gap-2 self-start">
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-10" />
        </div>
      </div>

      {/* Context bar with metadata */}
      <div className="flex flex-wrap items-center text-sm mb-4 gap-y-2">
        <Skeleton className="h-4 w-32" /> {/* Date */}
        <div className="hidden sm:block mx-2">•</div>
        <Skeleton className="h-4 w-24" /> {/* Reporter name */}
        <div className="hidden sm:block mx-2">•</div>
        <Skeleton className="h-4 w-40" /> {/* Email */}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Left column - main content */}
        <div className="md:col-span-2 space-y-4">
          {/* AI Safety Insight */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 rounded-full" />
                <Skeleton className="h-5 w-32" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6 mb-2" />
              <Skeleton className="h-4 w-4/5" />
            </CardContent>
          </Card>

          {/* Description */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6 mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>

          {/* Media placeholder */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-16" />
            </CardHeader>
            <CardContent>
              <div className="flex gap-3">
                <Skeleton className="h-32 w-32 rounded-md" />
                <Skeleton className="h-32 w-32 rounded-md" />
                <Skeleton className="h-32 w-32 rounded-md" />
              </div>
            </CardContent>
          </Card>

          {/* Immediate Actions */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-4/5" />
            </CardContent>
          </Card>

          {/* Root Cause */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-6 w-32 rounded-full" />
            </CardContent>
          </Card>

          {/* Comments Section */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-20" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-24 mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
                <div className="flex gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-28 mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - metadata & timeline */}
        <div className="space-y-4">
          {/* Location */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-16" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-24 mb-2" />
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-24 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-16" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Skeleton className="h-3 w-3 rounded-full mt-1" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
                <div className="flex gap-3">
                  <Skeleton className="h-3 w-3 rounded-full mt-1" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-5/6 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="flex gap-3">
                  <Skeleton className="h-3 w-3 rounded-full mt-1" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-4/5 mb-1" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
