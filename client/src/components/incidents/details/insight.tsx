import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { AlertCircle, CheckCircle2, ClipboardCheck, Edit, Wrench } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { usePermissions } from '@/hooks/use-permissions';
import { MODULES, ALLOWED_ACTIONS } from '@shared/user-permissions';

// Enhanced AI summary with formatted text for better visual presentation
const generateAISummary = (incident: RouterOutputs['incident']['getById']) => {
  // This would ideally come from the API but for now we'll generate it client-side
  const severity = incident.severity || 'unknown severity';
  const type = incident.type === 'incident' ? 'An incident' : 'A near miss';
  const dateTime = format(new Date(incident.reportedAt), "MMMM d, yyyy 'at' h:mm a");

  return (
    <>
      <span className="font-semibold">{type}</span> of{' '}
      <span className="font-semibold text-indigo-700">{severity} severity</span> was reported at on {dateTime}.
      {incident.oshaReportable ? (
        <span className="text-red-600 font-medium block mt-2">
          ⚠️ This incident requires OSHA reporting within 24 hours.
        </span>
      ) : (
        <span className="text-green-600 font-medium block mt-2">
          ✓ No OSHA reporting is required for this incident.
        </span>
      )}
    </>
  );
};

export const Insight = ({ incident }: { incident: RouterOutputs['incident']['getById'] }) => {
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();

  const { mutateAsync: updateIncident } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: incident.id });
      utils.auditTrail.get.invalidate({ entityId: incident.id, entityType: 'incident' });
    },
  });

  const actions: Record<string, { label: string; icon: React.ElementType; onClick: () => void; separator?: boolean }> =
    {
      'create-capa': {
        label: 'Create CAPA',
        icon: ClipboardCheck,
        onClick: () => {
          navigate(`${ROUTES.CAPA_NEW}?incidentId=${incident.id}`);
        },
      },
      'edit-incident': {
        label: 'Edit Incident',
        icon: Edit,
        onClick: () => {
          navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id));
        },
      },
      'mark-as-reviewed': {
        label: 'Mark as Reviewed',
        icon: CheckCircle2,
        separator: true,
        onClick: async () => {
          await updateIncident({
            id: incident.id,
            status: 'in_review',
          });

          toast('Mark as Reviewed', {
            description: 'Incident marked as reviewed.',
          });
        },
      },
      'close-without-action': {
        label: 'Close Without Action',
        icon: AlertCircle,
        onClick: async () => {
          await updateIncident({
            id: incident.id,
            status: 'closed',
          });

          toast('Close Without Action', {
            description: 'Incident closed without further action.',
          });
        },
      },
    };

  return (
    <div className="relative overflow-hidden bg-blue-50 rounded-lg border border-blue-100 shadow-xs mb-6 before:absolute before:top-0 before:left-0 before:w-1 before:h-full before:bg-indigo-500">
      <div className="p-5">
        <p className="text-gray-800 leading-relaxed text-[15px]">{generateAISummary(incident)}</p>
        <div className="mt-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button>
                      <Wrench className="h-4 w-4" />
                      Take Action
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="start" data-testid="action-menu">
                    <DropdownMenuLabel>Incident Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {Object.entries(actions).map(([key, action]) => {
                      if (key === 'mark-as-reviewed' && !hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.MARK_REVIEWED, incident.reportedByUser?.id)) {
                        return null;
                      }
                      return (
                        <div key={key}>
                          {action.separator && <DropdownMenuSeparator />}
                          <DropdownMenuItem key={key} onClick={action.onClick}>
                            <action.icon className="mr-2 h-4 w-4" />
                            {action.label}
                          </DropdownMenuItem>
                        </div>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent>
                <p>Log a CAPA, create a work order, or update the incident.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};
