import {
  AlertDialog,
  AlertDialogTitle,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogDescription,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';

export const ArchiveConfirmationDialog = ({
  archived,
  showArchiveConfirm,
  setShowArchiveConfirm,
  handleArchive,
}: {
  archived: boolean;
  showArchiveConfirm: boolean;
  handleArchive: () => void;
  setShowArchiveConfirm: (show: boolean) => void;
}) => {
  return (
    <AlertDialog open={showArchiveConfirm} onOpenChange={setShowArchiveConfirm}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{archived ? 'Unarchive Incident' : 'Archive Incident'}</AlertDialogTitle>
          <AlertDialogDescription>
            {archived
              ? `This will restore the incident to the active list with its previous status. Unarchived incidents will appear in the default incident log view.`
              : "This will remove the incident from the active list while preserving its current status. Archived incidents can be viewed by selecting the 'Include Archived' filter in the incident log."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleArchive} className={archived ? '' : 'bg-red-600 hover:bg-red-700'}>
            {archived ? 'Unarchive' : 'Archive'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
