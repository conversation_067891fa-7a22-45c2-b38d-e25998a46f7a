'use client';

import * as React from 'react';

import { Calendar } from '@/components/ui/calendar';
import { type DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

export function DateRangePicker({
  label,
  range,
  setRange,
}: {
  label?: string;
  range: DateRange | undefined;
  setRange: (date: DateRange) => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button id="date-range" variant="outline" size="sm" className="justify-start text-left font-normal">
          <CalendarIcon className="mr-2 h-4 w-4" />
          {range?.from && range?.to ? (
            <>
              {format(range.from, 'LLL dd, y')} - {format(range.to, 'LLL dd, y')}
            </>
          ) : (
            <span>{label || 'Pick a date range'}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          defaultMonth={range?.from}
          selected={
            range?.from
              ? ({
                  from: range.from,
                  to: range.to,
                } as DateRange)
              : undefined
          }
          onSelect={(range: DateRange | undefined) => {
            if (range) {
              setRange(range);
            }
          }}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  );
}
