import React, { useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Shield, CheckCircle2, RotateCcw, Home } from 'lucide-react';
import { useLocation } from 'wouter';
import confetti from 'canvas-confetti';
import { ROUTES } from '@/constants/ROUTE_PATHS';

interface CapaSuccessModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  capaId?: string;
  capaTitle?: string;
  onCreateAnother: () => void;
}

export const CapaSuccessModal = ({ open, onOpenChange, onCreateAnother }: CapaSuccessModalProps) => {
  const [_, navigate] = useLocation();

  // Trigger confetti when modal opens - more controlled burst
  useEffect(() => {
    if (open) {
      // Define confetti colors - red theme
      const colors = ['#FF5757', '#FF0000', '#D10000', '#FF7D7D', '#FF9F9F'];

      // Single controlled burst of confetti
      setTimeout(() => {
        confetti({
          particleCount: 80, // Reduced from 150
          spread: 65,
          origin: { y: 0.5, x: 0.5 }, // Center of screen
          colors: colors,
          startVelocity: 30,
          gravity: 1,
          ticks: 200, // Reduced from 300
          shapes: ['square', 'circle'],
          scalar: 0.9,
          disableForReducedMotion: true,
        });
      }, 200); // Small delay to ensure modal is visible
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] rounded-lg overflow-hidden max-h-[90vh] p-4 sm:p-6 w-[95vw] sm:w-auto">
        <DialogHeader className="pb-2">
          <div className="flex flex-col items-center text-center">
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              {/* Sparkle effect behind the shield */}
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute top-0 left-0 w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
                <div className="absolute top-2 right-1 w-1 h-1 bg-yellow-300 rounded-full animate-pulse delay-100"></div>
                <div className="absolute bottom-3 left-2 w-1.5 h-1.5 bg-yellow-300 rounded-full animate-pulse delay-200"></div>
                <div className="absolute bottom-2 right-3 w-2 h-2 bg-yellow-300 rounded-full animate-pulse delay-300"></div>
              </div>
              <div className="absolute top-1 right-1">
                <CheckCircle2 className="h-6 w-6 text-green-600 bg-white rounded-full" />
              </div>
            </div>
            <DialogTitle className="text-xl font-semibold">🎉 CAPA Created!</DialogTitle>
            <p className="mt-3 text-muted-foreground max-w-full px-2 whitespace-normal break-words leading-normal">
              Thank you for taking action. You've helped move us one step closer to a safer, stronger workplace.
            </p>
          </div>
        </DialogHeader>

        <div className="flex flex-col sm:flex-row justify-center sm:space-x-4 sm:justify-between gap-3 mt-6">
          {/* <Button
            variant="outline"
            className="flex-1 flex items-center justify-center"
            onClick={() => {
              onOpenChange(false);
              navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId));
            }}
          >
            <CheckCircle2 className="mr-2 h-4 w-4" />
            <span className="whitespace-nowrap">View CAPA</span>
          </Button> */}
          <Button
            variant="outline"
            className="flex-1 flex items-center justify-center"
            onClick={() => {
              onOpenChange(false);
              onCreateAnother();
            }}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            <span className="whitespace-nowrap">Create Another</span>
          </Button>
          <Button
            className="flex-1 flex items-center justify-center"
            onClick={() => {
              onOpenChange(false);
              navigate(ROUTES.CAPA_LIST);
            }}
          >
            <Home className="mr-2 h-4 w-4" />
            <span className="whitespace-nowrap">Go to Dashboard</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
