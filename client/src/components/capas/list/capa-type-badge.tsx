import { Badge } from '@/components/ui/badge';
import { capaTypeEnum } from '@shared/schema';
import { CAPA_TYPE_MAP } from '@shared/schema.types';

export const CapaTypeBadge = ({ type }: { type: (typeof capaTypeEnum.enumValues)[number] }) => {
  const colorMap = {
    [capaTypeEnum.enumValues[0]]: 'bg-indigo-50 text-indigo-700 border-indigo-200',
    [capaTypeEnum.enumValues[1]]: 'bg-emerald-50 text-emerald-700 border-emerald-200',
    [capaTypeEnum.enumValues[2]]: 'bg-purple-50 text-purple-700 border-purple-200',
  };

  const typeLabel = CAPA_TYPE_MAP[type];

  const color = colorMap[type];

  return (
    <Badge className={`${color} font-medium border px-2 py-1 capitalize`} variant="outline">
      {typeLabel}
    </Badge>
  );
};
