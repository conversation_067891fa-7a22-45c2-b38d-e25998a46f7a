import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from '@/components/ui/collapsible';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  Sheet,
  SheetTrigger,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetClose,
} from '@/components/ui/sheet';
import { statusEnum, capaTypeEnum, capaPriorityEnum, capaTagsEnum } from '@shared/schema';
import { STATUS_MAP, CAPA_TYPE_MAP, CAPA_PRIORITY_MAP, User } from '@shared/schema.types';
import { Filter, ChevronDown } from 'lucide-react';
import { DateRange } from 'react-day-picker';

type Filters = {
  status: (typeof statusEnum.enumValues)[number][];
  type: (typeof capaTypeEnum.enumValues)[number][];
  priority: (typeof capaPriorityEnum.enumValues)[number][];
  owner: string[];
  dateRange: DateRange | undefined;
  includeArchived: boolean;
  tags: (typeof capaTagsEnum.enumValues)[number][];
};

export const CapaMobileFilters = ({
  filters,
  toggleFilter,
  activeFilterCount,
  resetFilters,
  users,
  setFilters,
}: {
  filters: Filters;
  toggleFilter: (type: 'status' | 'type' | 'priority' | 'owner', value: any) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  users: User[];
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
}) => {
  return (
    <div className="md:hidden mb-4">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className="w-full">
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {activeFilterCount > 0 && (
              <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter CAPAs</SheetTitle>
            <SheetDescription>Apply filters to narrow down the CAPA list.</SheetDescription>
          </SheetHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-4">
              {/* Status Collapsible Section */}
              <Collapsible defaultOpen className="bg-muted rounded-md p-3">
                <CollapsibleTrigger className="flex items-center justify-between w-full">
                  <h3 className="text-sm font-medium">Status</h3>
                  <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  {statusEnum.enumValues.map((status) => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}-mobile`}
                        checked={filters.status.includes(status)}
                        onCheckedChange={() => toggleFilter('status', status)}
                      />
                      <label htmlFor={`status-${status}-mobile`} className="text-sm cursor-pointer">
                        {STATUS_MAP[status]}
                      </label>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>

              {/* Type Collapsible Section */}
              <Collapsible defaultOpen className="bg-muted rounded-md p-3">
                <CollapsibleTrigger className="flex items-center justify-between w-full">
                  <h3 className="text-sm font-medium">Type</h3>
                  <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  {capaTypeEnum.enumValues.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}-mobile`}
                        checked={filters.type.includes(type)}
                        onCheckedChange={() => toggleFilter('type', type)}
                      />
                      <label htmlFor={`type-${type}-mobile`} className="text-sm cursor-pointer">
                        {CAPA_TYPE_MAP[type]}
                      </label>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>

              {/* Priority Collapsible Section */}
              <Collapsible defaultOpen className="bg-muted rounded-md p-3">
                <CollapsibleTrigger className="flex items-center justify-between w-full">
                  <h3 className="text-sm font-medium">Priority</h3>
                  <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  {capaPriorityEnum.enumValues.map((priority) => (
                    <div key={priority} className="flex items-center space-x-2">
                      <Checkbox
                        id={`priority-${priority}-mobile`}
                        checked={filters.priority.includes(priority)}
                        onCheckedChange={() => toggleFilter('priority', priority)}
                      />
                      <label htmlFor={`priority-${priority}-mobile`} className="text-sm cursor-pointer">
                        {CAPA_PRIORITY_MAP[priority]}
                      </label>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>

              {/* Owner Collapsible Section */}

              <Collapsible defaultOpen className="bg-muted rounded-md p-3">
                <CollapsibleTrigger className="flex items-center justify-between w-full">
                  <h3 className="text-sm font-medium">Owner</h3>
                  <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  {users?.map((user) => (
                    <div key={user.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`user-${user.id}-mobile`}
                        checked={filters.owner.includes(user.id)}
                        onCheckedChange={() => toggleFilter('owner', user.id)}
                      />
                      <label htmlFor={`user-${user.id}-mobile`} className="text-sm cursor-pointer">
                        {user.fullName}
                      </label>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>

              {/* Due Date Filter */}
              <DateRangePicker
                label="Due Date"
                range={filters.dateRange}
                setRange={(range) => {
                  setFilters((prev) => ({ ...prev, dateRange: range }));
                }}
              />

              {/* Additional Filters Collapsible Section */}
              <Collapsible defaultOpen className="bg-muted rounded-md p-3">
                <CollapsibleTrigger className="flex items-center justify-between w-full">
                  <h3 className="text-sm font-medium">Additional Filters</h3>
                  <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-archived-mobile"
                      checked={filters.includeArchived}
                      onCheckedChange={() =>
                        setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))
                      }
                    />
                    <label htmlFor="include-archived-mobile" className="text-sm cursor-pointer">
                      Include Archived
                    </label>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button variant="outline" onClick={resetFilters} className="w-full sm:w-auto">
                Reset Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button type="submit" className="w-full sm:w-auto">
                Apply Filters
              </Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
