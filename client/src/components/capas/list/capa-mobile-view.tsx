import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { CapasEmpty } from '@/components/capas/list/capas-empty';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { StatusBadge } from '@/components/incidents/list/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { RouterInputs, RouterOutputs } from '@shared/router.types';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { CAPA_TAGS_MAP } from '@shared/schema.types';
import { format } from 'date-fns';
import { Archive, Calendar, Download, Eye, Link, MoreHorizontal, Pencil, Trash2, User } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const CapaMobileView = ({
  capas,
  activeFilterCount,
  resetFilters,
  updateCapa,
}: {
  capas?: RouterOutputs['capa']['list'];
  activeFilterCount: number;
  resetFilters: () => void;
  updateCapa: (capa: RouterInputs['capa']['update']) => void;
}) => {
  const [_, navigate] = useLocation();

  // Render mobile card view
  const renderMobileCard = (capa: RouterOutputs['capa']['list']['data'][number]) => {
    return (
      <Card
        key={capa.id}
        className={`mb-4 cursor-pointer transition-colors hover:bg-muted/50 ${
          capa.archived ? 'bg-amber-50/50 hover:bg-amber-50/80 border-amber-200' : ''
        }`}
        onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id))}
      >
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center">
              <p className="font-medium text-sm mr-2">{capa.slug}</p>
              {capa.archived && (
                <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                  <Archive className="h-3 w-3 mr-1" />
                  Archived
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              <StatusBadge status={capa.status as (typeof statusEnum.enumValues)[number]} />
              <PriorityBadge priority={capa.priority as (typeof capaPriorityEnum.enumValues)[number]} />
            </div>
          </div>

          <h3 className="font-medium mb-2 line-clamp-2">{capa.title}</h3>

          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-center mb-1">
              <CapaTypeBadge type={capa.type as (typeof capaTypeEnum.enumValues)[number]} />

              {/* Display tags if available */}
              {capa.tags && capa.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 ml-2">
                  {capa.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="bg-gray-100 text-xs px-1.5 py-0">
                      {CAPA_TAGS_MAP[tag as (typeof capaTagsEnum.enumValues)[number]] || tag}
                    </Badge>
                  ))}
                </div>
              )}

              {capa.dueDate && (
                <span className="flex items-center ml-2">
                  <Calendar className="h-3 w-3 mr-1" />
                  {format(new Date(capa.dueDate), 'MMM d, yyyy')}
                </span>
              )}
            </div>

            <div className="flex items-center">
              <User className="h-3 w-3 mr-2" />
              {capa.owner?.fullName || 'Unassigned'}
            </div>

            {capa.incidentId && (
              <div className="flex items-center text-blue-600 font-medium">
                <Button
                  variant="link"
                  className="text-blue-600 p-0 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(capa.incidentId!));
                  }}
                >
                  <Link className="h-3 w-3" />
                  {capa.incidentSlug}
                </Button>
              </div>
            )}
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                className="h-8 px-2 rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="h-8 px-2 rounded-full"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-2 rounded-full"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                  <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className={capa.archived ? 'text-amber-600' : 'text-red-600'}
                    onClick={async (e) => {
                      e.stopPropagation();

                      try {
                        await updateCapa({
                          id: capa.id,
                          archived: !capa.archived,
                        });
                        toast(capa.archived ? 'CAPA Unarchived' : 'CAPA Archived', {
                          description: `${capa.slug} has been ${capa.archived ? 'unarchived' : 'archived'}.`,
                        });
                      } catch (error) {
                        toast('Action Failed', {
                          description: `Could not ${capa.archived ? 'unarchive' : 'archive'} the CAPA. Please try again.`,
                        });
                      }
                    }}
                  >
                    {capa.archived ? (
                      <>
                        <Archive className="h-4 w-4 mr-2" />
                        Unarchive
                      </>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Archive
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {capas?.data.length === 0 ? (
        <CapasEmpty
          activeFilterCount={activeFilterCount}
          onCreateCapa={() => navigate(ROUTES.CAPA_NEW)}
          onResetFilters={resetFilters}
        />
      ) : (
        capas?.data.map(renderMobileCard)
      )}
    </div>
  );
};
