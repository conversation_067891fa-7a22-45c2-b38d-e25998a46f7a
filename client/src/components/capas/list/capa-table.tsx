import { CapaTags } from '@/components/capas/list/capa-tags';
import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { CapasEmpty } from '@/components/capas/list/capas-empty';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { StatusBadge } from '@/components/incidents/list/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { RouterInputs, RouterOutputs } from '@shared/router.types';
import { capaPriorityEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { format } from 'date-fns';
import {
    Archive,
    Calendar,
    Eye,
    Link,
    MoreHorizontal,
    Pencil,
    Trash2,
    User
} from 'lucide-react';
import { toast } from 'sonner';

export const CapaTable = ({
  capas,
  activeFilterCount,
  resetFilters,
  navigate,
  updateCapa,
}: {
  capas?: RouterOutputs['capa']['list'];
  activeFilterCount: number;
  resetFilters: () => void;
  navigate: (path: string) => void;
  updateCapa: (capa: RouterInputs['capa']['update']) => void;
}) => {
  return (
    <div className="rounded-md border overflow-hidden">
      {capas?.data.length === 0 ? (
        <CapasEmpty
          activeFilterCount={activeFilterCount}
          onCreateCapa={() => navigate(ROUTES.CAPA_NEW)}
          onResetFilters={resetFilters}
        />
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="max-w-[350px]">CAPA</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Linked Incident</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {capas?.data.map((capa: RouterOutputs['capa']['list']['data'][number]) => {
              return (
                <TableRow
                  key={capa.id}
                  className={`cursor-pointer ${capa.archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
                  onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id))}
                >
                  <TableCell className="max-w-[350px]">
                    <div className="flex flex-col">
                      <div className="font-medium">{capa.slug}</div>
                      <div className="text-sm text-muted-foreground truncate">{capa.title}</div>
                      {capa.archived && (
                        <Badge className="mt-1 w-fit bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                          <Archive className="h-3 w-3 mr-1" />
                          Archived
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <CapaTypeBadge type={capa.type as (typeof capaTypeEnum.enumValues)[number]} />
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={capa.status as (typeof statusEnum.enumValues)[number]} />
                  </TableCell>
                  <TableCell>
                    <PriorityBadge priority={capa.priority as (typeof capaPriorityEnum.enumValues)[number]} />
                  </TableCell>
                  <TableCell>
                    {capa.incidentId ? (
                      <div
                        className="text-blue-600 font-medium flex items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(capa.incidentId!));
                        }}
                      >
                        <Link className="h-4 w-4 mr-1.5" />
                        {capa.incidentSlug}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">—</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1.5 text-gray-400" />
                      <span>{capa?.owner?.fullName || 'Unassigned'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {capa.dueDate ? (
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1.5 text-gray-400" />
                        {format(new Date(capa.dueDate), 'MMM d, yyyy')}
                      </div>
                    ) : (
                      <span className="text-gray-400">No due date</span>
                    )}
                  </TableCell>
                  <TableCell>{capa.tags && capa.tags.length > 0 && <CapaTags tags={capa.tags} />}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-1" onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className={capa.archived ? 'text-amber-600' : 'text-red-600'}
                            onClick={async () => {
                              try {
                                await updateCapa({
                                  id: capa.id,
                                  archived: !capa.archived,
                                });

                                toast(capa.archived ? 'CAPA Unarchived' : 'CAPA Archived', {
                                  description: `${capa.slug} has been ${capa.archived ? 'unarchived' : 'archived'}.`,
                                });
                              } catch (error) {
                                toast('Action Failed', {
                                  description: `Could not ${capa.archived ? 'unarchive' : 'archive'} the CAPA. Please try again.`,
                                });
                              }
                            }}
                          >
                            {capa.archived ? (
                              <>
                                <Archive className="h-4 w-4 mr-2" />
                                Unarchive
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Archive
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
    </div>
  );
};
