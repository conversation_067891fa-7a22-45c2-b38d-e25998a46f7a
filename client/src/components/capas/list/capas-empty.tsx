import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface CapaEmptyStateProps {
  activeFilterCount: number;
  onCreateCapa: () => void;
  onResetFilters: () => void;
}

export const CapasEmpty = ({ activeFilterCount, onCreateCapa, onResetFilters }: CapaEmptyStateProps) => {
  const hasActiveFilters = activeFilterCount > 0;

  return (
    <div className="flex flex-col items-center justify-center py-16 px-6">
      <div className="text-center max-w-md mx-auto">
        <div className="text-4xl mb-4">📋</div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-3">No Active CAPAs Found</h2>
        <p className="text-gray-600 mb-6 leading-relaxed">
          {hasActiveFilters
            ? 'No CAPAs match your current filters. Try adjusting your search criteria or reset filters to see all CAPAs.'
            : 'It looks like there are no corrective or preventive actions currently being tracked. Start by creating a new CAPA to address an identified issue or potential risk.'}
        </p>
        <div className="space-y-4">
          <Button onClick={onCreateCapa}>
            <Plus className="h-4 w-4 mr-2" />
            Create CAPA
          </Button>
          {hasActiveFilters && (
            <div>
              <Button variant="outline" onClick={onResetFilters} className="px-4 py-2">
                Reset Filters
              </Button>
            </div>
          )}
        </div>
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500 mb-1">💡 Tip: Check 'Show Archived' to view past or completed CAPAs.</p>
        </div>
      </div>
    </div>
  );
};
