import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RouterOutputs } from '@shared/router.types';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { CAPA_PRIORITY_MAP, CAPA_TAGS_MAP, CAPA_TYPE_MAP, STATUS_MAP, User } from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';
import { DateRange } from 'react-day-picker';

type Filters = {
  status: (typeof statusEnum.enumValues)[number][];
  type: (typeof capaTypeEnum.enumValues)[number][];
  priority: (typeof capaPriorityEnum.enumValues)[number][];
  owner: string[];
  dateRange: DateRange | undefined;
  includeArchived: boolean;
  tags: (typeof capaTagsEnum.enumValues)[number][];
};

export const CapaFilters = ({
  filters,
  toggleFilter,
  activeFilterCount,
  resetFilters,
  capas,
  users,
  setFilters,
}: {
  filters: Filters;
  toggleFilter: (type: 'status' | 'type' | 'priority' | 'owner' | 'tags', value: any) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  capas: RouterOutputs['capa']['list']['data'];
  users: User[];
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex flex-wrap gap-3 items-center">
        {/* Status Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {statusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('status', status);
                }}
                checked={filters.status.includes(status)}
              >
                <span>{STATUS_MAP[status]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.type.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {capaTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('type', type);
                }}
                checked={filters.type.includes(type)}
              >
                <span>{CAPA_TYPE_MAP[type]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Priority Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Priority
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.priority.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Priority</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {capaPriorityEnum.enumValues.map((priority) => (
              <DropdownMenuCheckboxItem
                key={priority}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('priority', priority);
                }}
                checked={filters.priority.includes(priority)}
              >
                <span>{CAPA_PRIORITY_MAP[priority]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Tags Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Tags
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.tags.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Tags</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {capaTagsEnum.enumValues.map((tag) => (
              <DropdownMenuCheckboxItem
                key={tag}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('tags', tag);
                }}
                checked={filters.tags.includes(tag)}
              >
                <span>{CAPA_TAGS_MAP[tag]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Owner Filter Dropdown */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Owner
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.owner.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px] max-h-[300px] overflow-y-auto">
            <DropdownMenuLabel>Filter by Owner</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {users?.map((user) => (
              <DropdownMenuCheckboxItem
                key={user.id}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('owner', user.id);
                }}
                checked={filters.owner.includes(user.id)}
              >
                <span>{user.fullName}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Due Date Filter */}
        <DateRangePicker
          label="Due Date"
          range={filters.dateRange}
          setRange={(range) => {
            setFilters((prev) => ({ ...prev, dateRange: range }));
          }}
        />

        {/* Show Archived Checkbox */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5 mr-1.5" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Active Filter Summary */}
      {activeFilterCount > 0 && (
        <div className="flex items-center text-sm text-muted-foreground mt-2">
          <Filter className="h-4 w-4 mr-2" />
          <span>
            {capas?.length} CAPA{capas?.length === 1 ? '' : 's'} found with {activeFilterCount} active filter
            {activeFilterCount === 1 ? '' : 's'}
          </span>
        </div>
      )}
    </div>
  );
};
