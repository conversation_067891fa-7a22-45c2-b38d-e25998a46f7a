import { Badge } from '@/components/ui/badge';
import { capaEffectivenessStatusEnum } from '@shared/schema';
import { CAPA_EFFECTIVENESS_STATUS_MAP } from '@shared/schema.types';

export const EffectivenessBadge = ({
  effectiveness,
}: {
  effectiveness: (typeof capaEffectivenessStatusEnum.enumValues)[number];
}) => {
  const label = CAPA_EFFECTIVENESS_STATUS_MAP[effectiveness];

  const colorMap = {
    effective: 'bg-green-100 text-green-800',
    partial: 'bg-yellow-100 text-yellow-800',
    not_effective: 'bg-red-100 text-red-800',
    not_evaluated: 'bg-gray-100 text-gray-800',
  };

  return (
    <Badge className={`${colorMap[effectiveness]} font-medium flex items-center border px-2 py-1`} variant="outline">
      {label}
    </Badge>
  );
};
