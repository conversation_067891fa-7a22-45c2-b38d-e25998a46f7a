import { Skeleton } from '@/components/ui/skeleton';

export const CapaLoading = () => {
  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      {/* Back Navigation */}
      <div className="mb-4">
        <Skeleton className="h-9 w-20" />
      </div>

      {/* Title Header */}
      <div className="flex flex-col md:flex-row justify-between items-start mb-4 gap-3">
        {/* Desktop View */}
        <div className="hidden md:block w-full">
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-28" />
          </div>
          <Skeleton className="h-8 w-3/4" />
        </div>

        {/* Mobile View */}
        <div className="md:hidden w-full">
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-28" />
          </div>
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 flex-1 mr-3" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>

        {/* Desktop buttons */}
        <div className="hidden md:flex gap-2 self-start">
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-10" />
        </div>
      </div>

      {/* Location and Date Bar */}
      <div className="flex flex-wrap items-center text-sm text-gray-500 mb-6 gap-y-2 gap-x-2">
        <Skeleton className="h-4 w-32" />
        <div className="hidden sm:block text-muted-foreground">•</div>
        <Skeleton className="h-4 w-40" />
        <div className="hidden sm:block text-muted-foreground">•</div>
        <Skeleton className="h-4 w-24" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main content column */}
        <div className="lg:col-span-2 space-y-6">
          {/* AI Safety Insight */}
          <div className="bg-blue-50 rounded-lg border border-blue-200 shadow-sm">
            <div className="p-5">
              <Skeleton className="h-4 w-full mb-3" />
              <Skeleton className="h-4 w-5/6 mb-3" />
              <Skeleton className="h-4 w-4/5 mb-4" />
              <Skeleton className="h-4 w-3/4 mb-4" />

              <div className="mt-4 pt-3 border-t border-blue-100">
                <Skeleton className="h-4 w-48 mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            </div>
          </div>

          {/* Proposed Actions Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-32" />
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <div className="flex items-start">
                  <Skeleton className="h-4 w-4 mt-1 mr-3 rounded-full" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <div className="flex items-start">
                  <Skeleton className="h-4 w-4 mt-1 mr-3 rounded-full" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
                <div className="flex items-start">
                  <Skeleton className="h-4 w-4 mt-1 mr-3 rounded-full" />
                  <Skeleton className="h-4 w-4/5" />
                </div>
              </div>
            </div>
          </div>

          {/* Root Cause Analysis Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-40" />
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <div>
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-4 w-56" />
                </div>
                <div>
                  <Skeleton className="h-4 w-44 mb-2" />
                  <div className="bg-gray-50 rounded-md p-3 border">
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-5/6 mb-2" />
                    <Skeleton className="h-4 w-4/5" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Comments Section */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="p-6">
              <Skeleton className="h-32 w-full" />
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Owner Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-16" />
            </div>
            <div className="p-6">
              <div className="flex items-center">
                <Skeleton className="h-8 w-8 rounded-full mr-3" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            </div>
          </div>

          {/* Status Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-16" />
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <Skeleton className="h-6 w-20" />
                <div>
                  <Skeleton className="h-4 w-24 mb-2" />
                  <div className="flex flex-wrap gap-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Priority Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-16" />
            </div>
            <div className="p-6">
              <Skeleton className="h-6 w-20" />
            </div>
          </div>

          {/* Tags Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-12" />
            </div>
            <div className="p-6">
              <div className="flex flex-wrap gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-14" />
              </div>
            </div>
          </div>

          {/* Attachments Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 mr-2" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 mr-2" />
                    <Skeleton className="h-4 w-28" />
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </div>
          </div>

          {/* Timeline Card */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-6 border-b">
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-3/4 mb-1" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-2/3 mb-1" />
                    <Skeleton className="h-3 w-1/3" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
