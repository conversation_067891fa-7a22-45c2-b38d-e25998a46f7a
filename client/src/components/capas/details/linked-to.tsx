import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { cn } from '@/lib/utils';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { AlertTriangle, Wrench } from 'lucide-react';
import { useLocation } from 'wouter';

export const LinkedTo = ({
  capa,
  workOrders,
}: {
  capa: RouterOutputs['capa']['getById'];
  workOrders: RouterOutputs['workOrder']['getByCapa'] | undefined;
}) => {
  const [_, navigate] = useLocation();
  const items = [];

  // Add incident link if exists
  if (capa.incidentId) {
    items.push(
      <div key="incident" className="flex items-center mb-2 min-w-0">
        <AlertTriangle className="h-4 w-4 mr-2 text-amber-500 flex-shrink-0" />
        <span className="mr-1 flex-shrink-0">Incident</span>
        <span
          className="text-blue-600 hover:underline cursor-pointer truncate"
          onClick={() => navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(capa.incidentId!))}
        >
          #{capa.incidentSlug}
        </span>
      </div>,
    );
  }

  //   // Add audit link if exists
  //   if (capa.linkedAuditId) {
  //     items.push(
  //       <div key="audit" className="flex items-center mb-2 min-w-0">
  //         <FileText className="h-4 w-4 mr-2 text-blue-500 flex-shrink-0" />
  //         <span className="mr-1 flex-shrink-0">Audit</span>
  //         <span
  //           className="text-blue-600 hover:underline cursor-pointer truncate"
  //           onClick={() => onNavigate(`/audits/${capa.linkedAuditId}`)}
  //         >
  //           #{capa.linkedAuditId}
  //         </span>
  //       </div>,
  //     );
  //   }

  // Return formatted items or "No linked items" message if no links at all
  if (items.length === 0 && (!workOrders || workOrders.data.length === 0)) {
    return <span className="text-gray-500">No linked items</span>;
  }

  return (
    <div className="space-y-3 min-w-0">
      {/* Return incident and audit links */}
      {items.length > 0 && <div className="mb-2 min-w-0">{items}</div>}

      {/* Add work orders if they exist as collapsible cards */}
      {workOrders?.data?.length && workOrders?.data?.length > 0 && (
        <div className="space-y-3">
          {workOrders?.data.map((workOrder) => (
            <Accordion key={`wo-${workOrder.id}`} type="single" collapsible className="border rounded-md">
              <AccordionItem value={workOrder.id} className="border-none">
                <AccordionTrigger className="px-3 py-2 hover:bg-gray-50 data-[state=open]:bg-gray-50 min-w-0">
                  <div className="flex flex-1 items-center min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full text-left gap-2 min-w-0">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <span className="font-medium text-sm flex-shrink-0">{workOrder.workOrderNumber}:</span>
                        <span className="text-sm truncate min-w-0 flex-1">{workOrder.title}</span>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Badge
                          className={cn(
                            'text-xs whitespace-nowrap',
                            workOrder.currentStatus === 'Open' && 'bg-blue-100 text-blue-800 hover:bg-blue-100',
                            workOrder.currentStatus === 'In Progress' &&
                              'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
                            workOrder.currentStatus === 'Completed' && 'bg-green-100 text-green-800 hover:bg-green-100',
                            workOrder.currentStatus === 'Cancelled' && 'bg-gray-100 text-gray-800 hover:bg-gray-100',
                          )}
                        >
                          <span className="truncate max-w-[80px]">{workOrder.currentStatus}</span>
                        </Badge>
                        <Badge variant="outline" className="text-xs whitespace-nowrap capitalize">
                          <span className="truncate max-w-[60px]">{workOrder.priority || 'medium'}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-3 pb-3">
                  <div className="space-y-3 text-sm">
                    {/* Work Order Details */}
                    <div className="min-w-0">
                      <h4 className="font-medium mb-1 break-words">{workOrder.title}</h4>
                      {/* <p className="text-gray-600 mb-3 whitespace-pre-line">{workOrder.description}</p> */}
                    </div>

                    {/* Work Order Fields */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <p className="text-gray-500 text-xs">Priority</p>
                        <p className="capitalize">{workOrder.priority || 'Medium'}</p>
                      </div>

                      <div>
                        <p className="text-gray-500 text-xs">Due Date</p>
                        <p>{workOrder.dueDate ? format(new Date(workOrder.dueDate), 'MMM d, yyyy') : 'Not set'}</p>
                      </div>
                    </div>

                    {/* View Full Work Order Link */}
                    <div className="mt-4 pt-2 border-t">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="secondary"
                              size="sm"
                              className="mt-2 w-full sm:w-auto flex items-center gap-1 min-w-0 cursor-pointer"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                window.location.href = ROUTES.BUILD_WORK_ORDER_DETAILS_PATH(workOrder.id);
                              }}
                            >
                              <Wrench className="h-4 w-4 flex-shrink-0" />
                              <span className="truncate">View Full Work Order in CMMS</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Opens the full Work Order in the CMMS module</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ))}
        </div>
      )}
    </div>
  );
};
