import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { accessPointStatusEnum } from '@shared/schema';
import { Location } from '@shared/schema.types';
import { format } from 'date-fns';
import { Archive, CalendarIcon, Filter } from 'lucide-react';
import { Label } from '../ui/label';
import { type DateRange } from 'react-day-picker';

type Filters = {
  status: 'all' | (typeof accessPointStatusEnum.enumValues)[number];
  locationId: string[];
  createdBy: string[];
  includeArchived: boolean;
  dateRange: DateRange | undefined;
};

interface AccessPointsMobileFiltersProps {
  activeFilterCount: number;
  filters: Filters;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
  locations: Location[];
  userOptions: Array<{ id: string; name: string }>;
  isLoadingUsers: boolean;
  resetFilters: () => void;
}

export const AccessPointsMobileFilters = ({
  activeFilterCount,
  filters,
  setFilters,
  locations,
  userOptions,
  isLoadingUsers,
  resetFilters,
}: AccessPointsMobileFiltersProps) => {
  const handleFilterChange = (key: keyof Filters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // status options array combining 'all' with enum values
  const statusOptions = ['all', ...accessPointStatusEnum.enumValues] as const;

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" className="ml-2 md:hidden">
          <Filter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent side="right">
        <SheetHeader>
          <SheetTitle>Filter Access Points</SheetTitle>
          <SheetDescription>Apply filters to narrow down your access point list.</SheetDescription>
        </SheetHeader>

        <div className="p-4 space-y-4">
          {/* Status Filter */}
          <div>
            <h3 className="font-medium mb-2">Status</h3>
            <div className="space-y-2">
              {statusOptions.map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={status}
                    checked={filters.status === status}
                    onCheckedChange={() =>
                      setFilters((prev) => ({
                        ...prev,
                        status: status,
                      }))
                    }
                  />
                  <label className="capitalize" htmlFor={status}>
                    {status}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Location Filter */}
          <div>
            <h3 className="font-medium mb-2">Location</h3>
            <div className="space-y-2">
              {locations.map((location) => (
                <div key={location.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={location.id}
                    checked={filters.locationId.includes(location.id)}
                    onCheckedChange={() => setFilters({ ...filters, locationId: [...filters.locationId, location.id] })}
                  />
                  <label htmlFor={location.id}>{location.name}</label>
                </div>
              ))}
            </div>
          </div>

          {/* Created By Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Created By</Label>
            <div className="space-y-2">
              {isLoadingUsers ? (
                <div className="text-sm text-muted-foreground">Loading users...</div>
              ) : (
                userOptions.map((user) => (
                  <div key={user.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`user-${user.id}`}
                      checked={filters.createdBy.includes(user.id)}
                      onCheckedChange={() => {
                        setFilters((prev) => ({
                          ...prev,
                          createdBy: prev.createdBy.includes(user.id)
                            ? prev.createdBy.filter((id) => id !== user.id)
                            : [...prev.createdBy, user.id],
                        }));
                      }}
                    />
                    <Label htmlFor={`user-${user.id}`} className="text-sm">
                      {user.name}
                    </Label>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Date Range</Label>
            <div className="grid gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button id="date-range" variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateRange?.from && filters.dateRange?.to ? (
                      <>
                        {format(filters.dateRange.from, 'LLL dd, y')} - {format(filters.dateRange.to, 'LLL dd, y')}
                      </>
                    ) : (
                      <span>Select date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    defaultMonth={filters.dateRange?.from}
                    selected={{
                      from: filters.dateRange?.from,
                      to: filters.dateRange?.to,
                    }}
                    onSelect={(range) => {
                      handleFilterChange('dateRange', {
                        from: range?.from,
                        to: range?.to,
                      });
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Archive Filter */}
          <div className="flex flex-col">
            <h3 className="font-medium mb-2">Archive Status</h3>
            <Button
              variant={filters.includeArchived ? 'default' : 'outline'}
              size="sm"
              onClick={() =>
                setFilters((prev) => ({
                  ...prev,
                  includeArchived: !prev.includeArchived,
                }))
              }
              className={`justify-start ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
            >
              <Archive className="h-4 w-4 mr-2" />
              Include Archived
            </Button>
          </div>
        </div>

        <SheetFooter className="pt-4">
          <SheetClose asChild>
            <Button variant="outline" className="w-full" onClick={resetFilters}>
              Reset All Filters
            </Button>
          </SheetClose>
          <SheetClose asChild>
            <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
