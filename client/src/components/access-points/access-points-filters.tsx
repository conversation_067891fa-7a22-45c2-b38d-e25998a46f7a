import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { accessPointStatusEnum } from '@shared/schema';
import type { Location } from '@shared/schema.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';
import { type DateRange } from 'react-day-picker';

type Filters = {
  status: 'all' | (typeof accessPointStatusEnum.enumValues)[number];
  locationId: string[];
  createdBy: string[];
  includeArchived: boolean;
  dateRange: DateRange | undefined;
};

interface UserOption {
  id: string;
  name: string;
}

interface AccessPointsFiltersProps {
  filters: Filters;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
  locations: Location[];
  userOptions: UserOption[];
  isLoadingUsers: boolean;
  activeFilterCount: number;
  resetFilters: () => void;
  handleFilterChange: (key: keyof Filters, value: any) => void;
}

export const AccessPointsFilters = ({
  filters,
  setFilters,
  locations,
  userOptions,
  isLoadingUsers,
  activeFilterCount,
  resetFilters,
  handleFilterChange,
}: AccessPointsFiltersProps) => {
  const statusOptions = ['all', ...accessPointStatusEnum.enumValues] as const;

  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {statusOptions.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                checked={filters.status === status}
                onCheckedChange={() =>
                  setFilters((prev) => ({
                    ...prev,
                    status: status,
                  }))
                }
              >
                <span className="capitalize">{status}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Location
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {locations.map((location) => (
              <DropdownMenuCheckboxItem
                key={location.id}
                className="flex items-center space-x-2"
                checked={filters.locationId.includes(location.id)}
                onCheckedChange={() =>
                  setFilters((prev) => ({
                    ...prev,
                    locationId: prev.locationId.includes(location.id)
                      ? prev.locationId.filter((id) => id !== location.id)
                      : [...prev.locationId, location.id],
                  }))
                }
              >
                <span>{location.name}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Created By Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Created By
              {filters.createdBy.length > 0 && (
                <Badge className="ml-1 h-5 w-5 p-0 flex items-center justify-center rounded-full">
                  {filters.createdBy.length}
                </Badge>
              )}
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-[250px]">
            {isLoadingUsers ? (
              <DropdownMenuItem disabled>Loading users...</DropdownMenuItem>
            ) : (
              userOptions.map((user) => (
                <DropdownMenuCheckboxItem
                  key={user.id}
                  className="flex items-center space-x-2"
                  checked={filters.createdBy.includes(user.id)}
                  onCheckedChange={() => {
                    setFilters((prev) => ({
                      ...prev,
                      createdBy: prev.createdBy.includes(user.id)
                        ? prev.createdBy.filter((id) => id !== user.id)
                        : [...prev.createdBy, user.id],
                    }));
                  }}
                >
                  <span>{user.name}</span>
                </DropdownMenuCheckboxItem>
              ))
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Date Range Filter */}
        <DateRangePicker
          range={filters.dateRange}
          setRange={(range) => {
            handleFilterChange('dateRange', {
              from: range?.from,
              to: range?.to,
            });
          }}
        />

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Reset
          </Button>
        )}
      </div>
    </div>
  );
}; 