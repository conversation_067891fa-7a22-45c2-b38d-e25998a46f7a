import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { AccessPoint, Location } from '@shared/schema.types';

interface ArchiveConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  accessPoint: AccessPoint | null;
  locations: Location[];
  onConfirm: () => void;
  isLoading: boolean;
}

export const ArchiveConfirmationDialog = ({
  isOpen,
  onClose,
  accessPoint,
  locations,
  onConfirm,
  isLoading,
}: ArchiveConfirmationDialogProps) => {
  if (!accessPoint) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{accessPoint.archived ? 'Unarchive' : 'Archive'} Access Point</DialogTitle>
          <DialogDescription>
            Are you sure you want to {accessPoint.archived ? 'unarchive' : 'archive'} this access point?
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <div className="space-y-2">
            <p>
              <strong>Name:</strong> {accessPoint.name}
            </p>
            <p>
              <strong>Location:</strong>{' '}
              {locations.find((loc) => loc.id === accessPoint.locationId)?.name ||
                accessPoint.locationId ||
                '—'}
            </p>
          </div>
        </div>
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant={accessPoint.archived ? 'default' : 'destructive'}
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                {accessPoint.archived ? 'Unarchiving...' : 'Archiving...'}
              </>
            ) : accessPoint.archived ? (
              'Unarchive'
            ) : (
              'Archive'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 