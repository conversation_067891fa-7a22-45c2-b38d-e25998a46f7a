import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import type { AccessPoint, Location } from '@shared/schema.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, QrCode, Trash2, XCircle } from 'lucide-react';

interface AccessPointsTableProps {
  accessPoints: AccessPoint[];
  locations: Location[];
  onViewQRCode: (accessPoint: AccessPoint, qrCodeUrl: string) => void;
  onUpdateStatus: (id: string, status: 'active' | 'inactive') => void;
  onArchive: (accessPoint: AccessPoint) => void;
  qrCodeUrl: string;
}

export const AccessPointsTable = ({
  accessPoints,
  locations,
  onViewQRCode,
  onUpdateStatus,
  onArchive,
  qrCodeUrl,
}: AccessPointsTableProps) => {
  return (
    <div className="rounded-md border hidden md:block">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Access Point Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {accessPoints.map((accessPoint) => (
            <TableRow key={accessPoint.id}>
              <TableCell className="font-medium">{accessPoint.name}</TableCell>
              <TableCell>
                {locations.find((loc) => loc.id === accessPoint.locationId)?.name ||
                  accessPoint.locationId ||
                  '—'}
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {(accessPoint as any).createdByUser
                      ? `${(accessPoint as any).createdByUser.firstName} ${(accessPoint as any).createdByUser.lastName}`
                      : '--'}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {(accessPoint as any).createdByUser?.username || accessPoint.createdBy}
                  </span>
                </div>
              </TableCell>
              <TableCell>{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</TableCell>
              <TableCell>
                <Badge
                  className={`${
                    accessPoint.status === 'active'
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-amber-50 text-amber-700 border-amber-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* View QR Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View QR Code</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Activate/Deactivate Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() =>
                          onUpdateStatus(
                            accessPoint.id,
                            accessPoint.status === 'active' ? 'inactive' : 'active'
                          )
                        }
                      >
                        {accessPoint.status === 'active' ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Archive/Unarchive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={() => onArchive(accessPoint)}
                      >
                        {accessPoint.archived ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.archived ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}; 