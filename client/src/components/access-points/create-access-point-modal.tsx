import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { AccessPoint, CreateAccessPointFormSchema, Location } from '@shared/schema.types';
import { QrCode } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'wouter';
import { z } from 'zod';
import { AsyncDropdown } from '../ui/async-dropdown';

interface CreateAccessPointModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  locationsLoading: boolean;
  isGenerating: boolean;
  handleGenerateQR: (accessPoint: AccessPoint) => void;
  locations: Location[];
}

export const CreateAccessPointModal = ({
  isModalOpen,
  setIsModalOpen,
  locationsLoading,
  isGenerating,
  handleGenerateQR,
  locations,
}: CreateAccessPointModalProps) => {
  const [location, navigate] = useLocation();
  const utils = trpc.useUtils();
  const createAccessPoint = trpc.accessPoint.create.useMutation({
    onSuccess: () => {
      utils.accessPoint.list.invalidate();
    },
  });

  const form = useForm<z.infer<typeof CreateAccessPointFormSchema>>({
    resolver: zodResolver(CreateAccessPointFormSchema),
    defaultValues: {
      name: undefined,
      locationId: undefined,
    },
    mode: 'onSubmit',
  });

  const onSubmit = async (values: z.infer<typeof CreateAccessPointFormSchema>) => {
    // Prevent multiple submissions
    if (createAccessPoint.isPending || isGenerating) {
      return;
    }

    try {
      const createdAccessPoint = await createAccessPoint.mutateAsync({
        name: values.name,
        locationId: values.locationId,
      });

      handleGenerateQR(createdAccessPoint);
      form.reset();
    } catch (error) {
      // Error handling is already done by tRPC mutation
      console.error('Error creating access point:', error);
    }
  };

  const isSubmitting = createAccessPoint.isPending || isGenerating;

  return (
    <Dialog
      open={isModalOpen}
      onOpenChange={(open) => {
        setIsModalOpen(open);
        if (!open && location === ROUTES.ACCESS_POINTS_NEW) {
          navigate(ROUTES.ACCESS_POINTS_LIST);
        }
        // Reset form when modal closes
        if (!open) {
          form.reset();
        }
      }}
    >
      <DialogContent className="sm:max-w-[425px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>Create New Access Point</DialogTitle>
              <DialogDescription>Create a QR code access point for incident reporting.</DialogDescription>
            </DialogHeader>

            <div className="space-y-4 mt-8 mb-8">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="locationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <AsyncDropdown
                        options={locations.reduce(
                          (acc, location) => {
                            acc[location.id] = location.name;
                            return acc;
                          },
                          {} as Record<string, string>,
                        )}
                        onSearch={() => {}}
                        value={field.value}
                        onChange={field.onChange}
                        loading={locationsLoading}
                        placeholder="Select a location"
                        multi={false}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsModalOpen(false);
                  if (location === ROUTES.ACCESS_POINTS_NEW) {
                    navigate(ROUTES.ACCESS_POINTS_LIST);
                  }
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={!form.formState.isValid || isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <QrCode className="mr-2 h-4 w-4" />
                    Generate QR Code
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
