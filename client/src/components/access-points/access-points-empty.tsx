import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { MapPin, Plus, QrCode, User } from 'lucide-react';

interface AccessPointsEmptyProps {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
  onCreateAccessPoint: () => void;
}

export const AccessPointsEmpty = ({ hasActiveFilters, onResetFilters, onCreateAccessPoint }: AccessPointsEmptyProps) => {
  const isMobile = useIsMobile();
  
  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <QrCode className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>No access points found</h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No access points match your current filters. Try adjusting your search criteria or clear filters to see all
            access points.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no access points exist at all
  return (
    <div className="text-center py-16">
      <div className="mx-auto w-32 h-32 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-full flex items-center justify-center mb-6 shadow-lg">
        <QrCode className="h-16 w-16 text-blue-600" />
      </div>
      <h3 className="text-2xl font-bold text-gray-900 mb-3">
        Transform Safety Reporting with QR Access Points
      </h3>
      <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
        Deploy smart QR codes throughout your facility to enable instant incident reporting. 
        Employees, contractors, and visitors can simply scan to report safety concerns from any location, 
        ensuring comprehensive coverage and faster response times.
      </p>
      
      <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-10 text-left">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <QrCode className="h-6 w-6 text-blue-600" />
          </div>
          <h4 className="font-semibold text-gray-900 mb-2">Quick Deployment</h4>
          <p className="text-sm text-gray-600">Generate and place QR codes at strategic locations for instant access to reporting forms.</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
            <MapPin className="h-6 w-6 text-green-600" />
          </div>
          <h4 className="font-semibold text-gray-900 mb-2">Location Tracking</h4>
          <p className="text-sm text-gray-600">Automatically capture precise incident locations based on QR code placement.</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
            <User className="h-6 w-6 text-purple-600" />
          </div>
          <h4 className="font-semibold text-gray-900 mb-2">Universal Access</h4>
          <p className="text-sm text-gray-600">Enable reporting for all personnel types - employees, contractors, and visitors.</p>
        </div>
      </div>
      
      <Button 
        onClick={onCreateAccessPoint}
        size="lg"
        className="gap-2 px-8 py-6 text-lg font-semibold"
      >
        <Plus className="h-5 w-5" />
        Create Access Point
      </Button>
    </div>
  );
}; 