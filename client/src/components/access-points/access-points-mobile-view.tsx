import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { AccessPoint, Location } from '@shared/schema.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, MapPin, QrCode, Trash2, User, XCircle } from 'lucide-react';

interface AccessPointsMobileViewProps {
  accessPoints: AccessPoint[];
  locations: Location[];
  onViewQRCode: (accessPoint: AccessPoint, qrCodeUrl: string) => void;
  onUpdateStatus: (id: string, status: 'active' | 'inactive') => void;
  onArchive: (accessPoint: AccessPoint) => void;
  qrCodeUrl: string;
}

export const AccessPointsMobileView = ({
  accessPoints,
  locations,
  onViewQRCode,
  onUpdateStatus,
  onArchive,
  qrCodeUrl,
}: AccessPointsMobileViewProps) => {
  const renderMobileCard = (accessPoint: AccessPoint) => {
    return (
      <div key={accessPoint.id} className="bg-white border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 mb-1">{accessPoint.name}</h3>
            <div className="flex items-center text-sm text-gray-500 mb-2">
              <MapPin className="h-3 w-3 mr-1" />
              {locations.find((loc) => loc.id === accessPoint.locationId)?.name ||
                accessPoint.locationId ||
                'No location'}
            </div>
          </div>
          <Badge
            className={`${
              accessPoint.status === 'active'
                ? 'bg-green-50 text-green-700 border-green-200'
                : 'bg-amber-50 text-amber-700 border-amber-200'
            } font-medium flex items-center border px-2 py-1`}
            variant="outline"
          >
            {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <User className="h-3 w-3 mr-1" />
            <div className="flex flex-col">
              <span className="font-medium">
                {(accessPoint as any).createdByUser
                  ? `${(accessPoint as any).createdByUser.firstName} ${(accessPoint as any).createdByUser.lastName}`
                  : '--'}
              </span>
              <span className="text-xs text-muted-foreground">
                {(accessPoint as any).createdByUser?.username || accessPoint.createdBy}
              </span>
            </div>
          </div>
          <span>{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</span>
        </div>
        
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="flex-1"
            onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
          >
            <QrCode className="h-3 w-3 mr-1" />
            View QR
          </Button>
          <Button 
            size="sm" 
            variant="outline"
            onClick={() =>
              onUpdateStatus(
                accessPoint.id,
                accessPoint.status === 'active' ? 'inactive' : 'active'
              )
            }
          >
            {accessPoint.status === 'active' ? (
              <XCircle className="h-3 w-3 mr-1" />
            ) : (
              <CheckCircle className="h-3 w-3 mr-1" />
            )}
            {accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}
          </Button>
          <Button 
            size="sm" 
            variant="outline"
            className="text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300"
            onClick={() => onArchive(accessPoint)}
          >
            {accessPoint.archived ? <Archive className="h-3 w-3" /> : <Trash2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4 block md:hidden">
      {accessPoints.map((accessPoint) => renderMobileCard(accessPoint))}
    </div>
  );
}; 