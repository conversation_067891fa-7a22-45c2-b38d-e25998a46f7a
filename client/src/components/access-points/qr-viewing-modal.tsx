import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ROUTES } from '@/constants/ROUTE_PATHS';
import { AccessPoint, Location } from '@shared/schema.types';
import { Copy, Download } from 'lucide-react';
import { QRCodeSVG } from 'qrcode.react';
import { useLocation } from 'wouter';

export const QrViewingModal = ({
  isQrModalOpen,
  setIsQrModalOpen,
  accessPoint,
  qrCodeUrl,
  locations,
}: {
  isQrModalOpen: boolean;
  setIsQrModalOpen: (isOpen: boolean) => void;
  accessPoint: AccessPoint | undefined;
  qrCodeUrl: string;
  locations: Location[];
}) => {
  const [location, navigate] = useLocation();

  // Handle copy to clipboard
  const handleCopyUrl = async () => {
    if (!qrCodeUrl) return;

    try {
      await navigator.clipboard.writeText(qrCodeUrl);
      // You could add a toast notification here if needed
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  // Add download QR code function
  const handleDownloadQR = () => {
    if (!accessPoint || !qrCodeUrl) return;

    // Get the SVG element
    const svg = document.querySelector('.QRCode') as SVGElement;
    if (!svg) return;

    // Create a canvas with larger dimensions
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size (A4 proportions at 72 DPI)
    canvas.width = 595; // A4 width at 72 DPI
    canvas.height = 842; // A4 height at 72 DPI

    // Fill background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Set up text styling
    ctx.textAlign = 'center';
    ctx.fillStyle = 'black';

    // Draw header (Access Point Name)
    ctx.font = 'bold 24px Arial';
    ctx.fillText(accessPoint.name, canvas.width / 2, 80);

    // Create an image from the SVG
    const img = new Image();
    const svgData = new XMLSerializer().serializeToString(svg);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);

    img.onload = () => {
      // Calculate dimensions and positions
      const qrSize = 300; // QR code size
      const padding = 20; // Padding inside the border
      const borderWidth = 1; // Border width
      const boxSize = qrSize + padding * 2; // Total size including padding
      const boxX = (canvas.width - boxSize) / 2;
      const boxY = 120; // Position below header
      const qrX = boxX + padding; // QR code position inside box
      const qrY = boxY + padding;

      // Draw the bordered box
      ctx.strokeStyle = '#666666'; // Gray border
      ctx.lineWidth = borderWidth;
      ctx.fillStyle = 'white';
      ctx.fillRect(boxX, boxY, boxSize, boxSize);
      ctx.strokeRect(boxX, boxY, boxSize, boxSize);

      // Draw QR code
      ctx.drawImage(img, qrX, qrY, qrSize, qrSize);

      // Draw location name below the box
      ctx.font = '18px Arial';
      ctx.fillStyle = 'black';
      const locationName =
        locations.find((loc: Location) => loc.id === accessPoint.locationId)?.name || accessPoint.locationId;
      ctx.fillText(`Location: ${locationName}`, canvas.width / 2, boxY + boxSize + 40);

      // Draw instruction message
      ctx.font = '16px Arial';
      ctx.fillText(
        'Please scan this QR code to report an incident into EHS platform',
        canvas.width / 2,
        boxY + boxSize + 80,
      );

      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (!blob) return;
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${accessPoint.name}-QR.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 'image/png');

      URL.revokeObjectURL(svgUrl);
    };

    img.src = svgUrl;
  };

  return (
    <Dialog open={isQrModalOpen} onOpenChange={setIsQrModalOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{accessPoint ? accessPoint.name : 'Access Point'} QR Code</DialogTitle>
          <DialogDescription>Scan this QR code to report an incident at this location.</DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-4">
          {qrCodeUrl ? (
            <div className="p-4 bg-white rounded-lg border">
              <QRCodeSVG
                value={qrCodeUrl}
                size={280}
                bgColor={'#ffffff'}
                fgColor={'#000000'}
                level={'L'}
                className="QRCode"
              />
            </div>
          ) : (
            <div className="w-[280px] h-[280px] bg-muted flex items-center justify-center rounded-lg">
              <p className="text-muted-foreground">No QR code available</p>
            </div>
          )}
          <div className="mt-4 text-center">
            {qrCodeUrl && (
              <div className="flex items-center justify-center gap-2 mb-2 px-2">
                <p className="text-xs text-muted-foreground font-mono break-all flex-1 text-left">{qrCodeUrl}</p>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyUrl}
                  className="h-6 w-6 p-0 flex-shrink-0"
                  title="Copy URL"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            )}
            {accessPoint && accessPoint.locationId && (
              <p className="text-sm text-muted-foreground">
                Location: {locations.find((loc) => loc.id === accessPoint.locationId)?.name || accessPoint.locationId}
              </p>
            )}
            {accessPoint && (
              <p className="text-sm text-primary-600 mt-2">
                This QR code has been saved to your access points list and will remain after refresh.
              </p>
            )}
          </div>
        </div>
        <DialogFooter className="sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsQrModalOpen(false);
              if (location === ROUTES.ACCESS_POINTS_NEW) {
                navigate(ROUTES.ACCESS_POINTS_LIST);
              }
            }}
          >
            Close
          </Button>
          <Button
            type="button"
            disabled={!qrCodeUrl}
            onClick={() => {
              handleDownloadQR();
              setIsQrModalOpen(false);
              if (location === ROUTES.ACCESS_POINTS_NEW) {
                navigate(ROUTES.ACCESS_POINTS_LIST);
              }
            }}
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
