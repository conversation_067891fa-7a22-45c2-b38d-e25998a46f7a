import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { Mic, Sparkles, Square, Zap } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useRef, useState, useEffect } from 'react';
import { toast } from 'sonner';

interface VoiceInputProps {
  onAnalysisComplete: (text: string) => void;
  isPublic?: boolean;
  isLoading?: boolean;
}

// Maximum recording duration in seconds
const MAX_RECORDING_DURATION = 60;

// Supported MIME types in order of preference
const SUPPORTED_MIME_TYPES = [
  'audio/webm;codecs=opus',
  'audio/webm',
  'audio/mp4',
  'audio/mp4;codecs=mp4a.40.2',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg;codecs=opus',
  'audio/ogg',
];

// Browser detection utility
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
  const isIOSSafari = isIOS && isSafari;

  return {
    isIOS,
    isSafari,
    isIOSSafari,
    isChrome: /Chrome/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
  };
};

// Get the best supported MIME type for this browser
const getSupportedMimeType = (): string => {
  if (!window.MediaRecorder) {
    return 'audio/wav'; // Fallback
  }

  for (const mimeType of SUPPORTED_MIME_TYPES) {
    if (MediaRecorder.isTypeSupported(mimeType)) {
      return mimeType;
    }
  }

  // Fallback for older browsers or iOS
  return 'audio/mp4';
};

export function VoiceInput({ onAnalysisComplete, isPublic = false }: VoiceInputProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [hasTranscript, setHasTranscript] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isSupported, setIsSupported] = useState(true);
  const [browserInfo] = useState(getBrowserInfo);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<number | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const isMobile = useIsMobile();

  // Check browser support on mount
  useEffect(() => {
    const checkSupport = () => {
      // Check if basic APIs are available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setIsSupported(false);
        return;
      }

      // Check MediaRecorder support
      if (!window.MediaRecorder) {
        setIsSupported(false);
        return;
      }

      // For iOS, we need additional checks
      if (browserInfo.isIOSSafari) {
        // iOS Safari supports MediaRecorder starting from iOS 14.3
        const iOSVersion = navigator.userAgent.match(/OS (\d+)_(\d+)/);
        if (iOSVersion) {
          const major = parseInt(iOSVersion[1]);
          const minor = parseInt(iOSVersion[2]);
          if (major < 14 || (major === 14 && minor < 3)) {
            setIsSupported(false);
            return;
          }
        }
      }

      setIsSupported(true);
    };

    checkSupport();
  }, [browserInfo]);

  // Cleanup timer and stream on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  const transcribeMutationCall = isPublic ? trpc.ai.transcribePublic : trpc.ai.transcribe;

  const { mutateAsync: transcribe, isPending: isTranscribing } = transcribeMutationCall.useMutation({
    onSuccess: (data) => {
      // Handle the transcript data
      if (data?.text) {
        setTranscript(data.text);
        setHasTranscript(true);
      }

      toast('Transcription Complete', {
        description: 'Your recording has been processed.',
      });

      onAnalysisComplete(data.text);
    },
    onError: (error) => {
      console.error('Transcription error:', error);
      toast('Transcription Error', {
        description: 'Failed to transcribe audio. Please try again.',
      });
    },
  });

  const startRecording = async () => {
    try {
      // Reset previous data
      setTranscript('');
      setHasTranscript(false);
      audioChunksRef.current = [];
      setRecordingTime(0);

      // Check support again
      if (!isSupported) {
        throw new Error(
          browserInfo.isIOSSafari
            ? 'Voice recording requires iOS 14.3 or later. Please update your iOS or use the text input below.'
            : 'Your browser does not support audio recording. Please try Chrome, Firefox, Safari, or Edge.',
        );
      }

      // Request microphone access with specific constraints for iOS
      const constraints: MediaStreamConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          // iOS-specific constraints
          ...(browserInfo.isIOSSafari && {
            sampleRate: 44100,
            channelCount: 1,
          }),
        },
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      // Get the best supported MIME type
      const mimeType = getSupportedMimeType();
      console.log(`Using MIME type: ${mimeType} for ${browserInfo.isIOSSafari ? 'iOS Safari' : 'this browser'}`);

      // Set up the media recorder with appropriate options
      const mediaRecorderOptions: MediaRecorderOptions = {
        mimeType,
        // iOS Safari works better with higher bitrates
        ...(browserInfo.isIOSSafari && {
          audioBitsPerSecond: 128000,
        }),
      };

      const mediaRecorder = new MediaRecorder(stream, mediaRecorderOptions);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        toast('Recording Error', {
          description: 'An error occurred during recording. Please try again.',
        });
        stopRecording();
      };

      // Start recording with timeslice for iOS compatibility
      if (browserInfo.isIOSSafari) {
        // iOS Safari works better with timeslices
        mediaRecorder.start(1000);
      } else {
        mediaRecorder.start();
      }

      setIsRecording(true);

      // Start timer for recording duration
      timerRef.current = window.setInterval(() => {
        setRecordingTime((prev) => {
          const newTime = prev + 1;
          if (newTime >= MAX_RECORDING_DURATION) {
            // Auto stop recording when max duration is reached
            if (timerRef.current) {
              window.clearInterval(timerRef.current);
            }
            stopRecording();
            return MAX_RECORDING_DURATION;
          }
          return newTime;
        });
      }, 1000);

      toast('Recording started', {
        description: `Speak clearly and describe the incident in detail. Maximum ${MAX_RECORDING_DURATION} seconds.`,
      });
    } catch (error) {
      console.error('Error starting recording:', error);

      let errorMessage = 'Unable to access your microphone. Please check permissions and try again.';

      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = browserInfo.isIOSSafari
            ? 'Microphone access denied. Please go to Settings > Safari > Camera & Microphone and allow access for this site.'
            : 'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = browserInfo.isIOSSafari
            ? 'Voice recording is not supported on this version of iOS Safari. Please update to iOS 14.3 or later.'
            : 'Voice recording is not supported in this browser. Please try Chrome, Firefox, or Safari.';
        } else {
          errorMessage = error.message;
        }
      }

      toast('Microphone Access Error', {
        description: errorMessage,
      });
    }
  };

  const sendToTranscriptionAPI = async (audioBlob: Blob) => {
    try {
      // Check if audio is too large (warn at 1MB which is a safe size for most APIs)
      if (audioBlob.size > 1024 * 1024) {
        console.warn(`Large audio file (${(audioBlob.size / (1024 * 1024)).toFixed(2)}MB). May exceed server limits.`);
      }

      // Convert Blob to Base64
      const buffer = await audioBlob.arrayBuffer();
      const audioBase64 = btoa(new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''));

      // Send to transcription API using the mutation
      await transcribe({ audioBase64 });
    } catch (error) {
      console.error('Error preparing audio for transcription:', error);
      toast('Audio Processing Error', {
        description: 'Failed to process the audio. Please try again.',
      });
    }
  };

  const stopRecording = () => {
    // Clear the timer
    if (timerRef.current) {
      window.clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Stop media recorder and process audio
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      // We need to set up the onstop handler before calling stop()
      mediaRecorderRef.current.onstop = async () => {
        if (audioChunksRef.current.length > 0) {
          // Create audio blob from chunks with the correct MIME type
          const mimeType = getSupportedMimeType();
          const audioBlob = new Blob(audioChunksRef.current, {
            type: mimeType,
          });

          // Send to transcription API
          await sendToTranscriptionAPI(audioBlob);
        } else {
          console.warn('No audio data captured during recording');
          toast('Recording Error', {
            description: 'No audio was captured. Please try again.',
          });
        }

        // Clean up the stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      // Now stop the recorder which will trigger the onstop event
      mediaRecorderRef.current.stop();
    }

    setIsRecording(false);
    setRecordingTime(0);
  };

  // Format time for display (MM:SS)
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const recordButtonVariants = {
    recording: {
      scale: [1, 1.05, 1],
      transition: {
        repeat: Number.POSITIVE_INFINITY,
        repeatType: 'reverse' as const,
        duration: 1.5,
      },
    },
    idle: {
      scale: 1,
    },
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        repeat: Number.POSITIVE_INFINITY,
        repeatType: 'reverse' as const,
        duration: 1.5,
      },
    },
  };

  // Show unsupported message if browser doesn't support recording
  if (!isSupported) {
    return (
      <motion.div
        className="rounded-lg p-4 sm:p-6 space-y-4 bg-yellow-50/50 border border-yellow-200 shadow-md"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Voice Recording Unavailable</h3>
            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium">
              Not Supported
            </span>
          </div>
          <p className="text-sm text-gray-600">
            {browserInfo.isIOSSafari
              ? 'Voice recording requires iOS 14.3 or later. Please update your iOS or use the text input to describe the incident.'
              : 'Voice recording is not supported in this browser. Please use Chrome, Firefox, Safari, or Edge, or use the text input below.'}
          </p>
        </div>

        <div className="mt-4">
          <Textarea
            placeholder="Please type your incident description here..."
            className="min-h-[100px] font-medium bg-white border-yellow-200 text-gray-800 rounded-md"
            value={transcript}
            onChange={(e) => {
              setTranscript(e.target.value);
              setHasTranscript(e.target.value.length > 0);
            }}
            onBlur={() => {
              if (transcript.trim()) {
                onAnalysisComplete(transcript);
              }
            }}
          />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="rounded-lg p-4 sm:p-6 space-y-4 sm:space-y-5 bg-indigo-50/50 border border-indigo-200 shadow-md"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="space-y-2">
        <div className="flex justify-center items-center gap-2">
          <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded-full font-medium flex items-center">
            <Zap className="w-3 h-3 mr-1" />
            AI Assist
          </span>
          {browserInfo.isIOSSafari && (
            <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full font-medium">
              iOS Compatible
            </span>
          )}
        </div>
        <p className="text-xs sm:text-sm text-gray-600 text-center">
          {`${isMobile ? 'Tap' : 'Click'} to record your voice report (max ${MAX_RECORDING_DURATION}s)`}
          {browserInfo.isIOSSafari && (
            <span className="block mt-1 text-xs text-blue-600">💡 First tap may request microphone permission</span>
          )}
        </p>
      </div>

      <div className="flex flex-col items-center space-y-3 sm:space-y-4">
        {/* Record/Stop Button */}
        <motion.div
          className="relative cursor-pointer"
          variants={recordButtonVariants}
          animate={isRecording ? 'recording' : 'idle'}
          onClick={isRecording ? stopRecording : startRecording}
        >
          {isRecording && (
            <motion.div
              className="absolute inset-0 rounded-full bg-rose-100/40 -m-1"
              variants={pulseVariants}
              animate="animate"
            />
          )}

          <Button
            type="button"
            size="lg"
            className={cn(
              'rounded-full flex items-center justify-center p-0',
              isMobile ? 'h-16 w-16' : 'h-20 w-20',
              isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-600 hover:bg-indigo-700',
            )}
            disabled={isTranscribing}
          >
            <AnimatePresence mode="wait">
              {isRecording ? (
                <motion.div
                  key="square"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Square className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
                </motion.div>
              ) : (
                <motion.div
                  key="mic"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Mic className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
                </motion.div>
              )}
            </AnimatePresence>
          </Button>
        </motion.div>

        {/* Recording timer */}
        {isRecording && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center gap-1 text-red-600 font-medium"
          >
            <span className={`${recordingTime >= MAX_RECORDING_DURATION - 5 ? 'animate-pulse' : ''}`}>
              {formatTime(recordingTime)}
            </span>
            <span className="text-xs">/ {formatTime(MAX_RECORDING_DURATION)}</span>
          </motion.div>
        )}

        {/* Text label for action */}
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-xs sm:text-sm font-medium">
          {isRecording ? (
            <span className="text-red-600 font-semibold">{isMobile ? 'Tap to stop' : 'Click to stop recording'}</span>
          ) : isMobile ? (
            'Tap to record'
          ) : (
            'Click to start recording'
          )}
        </motion.div>
      </div>

      <AnimatePresence>
        {(hasTranscript || isRecording || isTranscribing) && (
          <motion.div
            className="space-y-2 sm:space-y-3"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="relative">
              <Textarea
                placeholder={
                  isMobile ? 'Voice transcript will appear here...' : 'Your recorded description will appear here...'
                }
                className={`${isMobile ? 'min-h-[80px]' : 'min-h-[100px]'} font-medium bg-white border-indigo-200 text-gray-800 rounded-md text-sm sm:text-base ${hasTranscript ? 'focus:border-indigo-300 focus:ring-indigo-200' : ''}`}
                value={transcript}
                onChange={(e) => setTranscript(e.target.value)}
                disabled={isRecording || isTranscribing}
              />

              {isTranscribing && (
                <div className="absolute inset-0 bg-white/90 flex items-center justify-center rounded-md backdrop-blur-sm">
                  <div className="flex flex-col items-center gap-1 px-2">
                    <div className="relative">
                      {/* Multiple pulsing rings for enhanced effect */}
                      <div className="absolute inset-0 animate-ping opacity-20 bg-indigo-400 rounded-full" />
                      <div
                        className="absolute inset-0 animate-ping opacity-40 bg-indigo-500 rounded-full"
                        style={{ animationDelay: '0.5s' }}
                      />
                      <div className="absolute inset-0 animate-pulse opacity-60 bg-indigo-300 rounded-full" />
                      <Sparkles
                        className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-indigo-600 relative z-10 animate-spin`}
                        style={{ animationDuration: '3s' }}
                      />
                    </div>
                    <div className="flex flex-col items-center">
                      <p className="text-xs sm:text-sm font-semibold text-gray-800 text-center">
                        {isMobile ? 'Transcribing audio...' : 'Transcribing your audio...'}
                      </p>
                      <div className="text-xs text-indigo-600 flex items-center gap-1">
                        <Zap className="h-3 w-3 animate-pulse" />
                        <span className="animate-pulse">Converting speech to text</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {hasTranscript && !isTranscribing && !isRecording && (
              <motion.div
                className="flex items-center gap-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <p className="text-xs text-indigo-700 font-medium flex items-center">
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI-generated inputs (editable)
                </p>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
