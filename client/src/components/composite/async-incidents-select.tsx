import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { PopoverAnchor, PopoverPortal } from '@radix-ui/react-popover';
import { Incident } from '@shared/schema.types';
import { Check, ChevronsUpDown, Loader2, Search } from 'lucide-react';
import * as React from 'react';

interface AsyncIncidentSelectProps {
  options?: Record<Incident['id'], Incident>;
  value?: Incident['id'] | null;
  onChange: (selected: Incident['id'] | undefined) => void;
  onSearch: (term: string) => void;
  search?: string;
  className?: string;
  loading?: boolean;
  placeholder?: string;
}

export const AsyncIncidentsSelect = ({
  options = {},
  search = '',
  onSearch,
  onChange,
  className,
  loading,
  value: selected = null,
  placeholder = 'Select incident...',
  ...props
}: AsyncIncidentSelectProps) => {
  const [open, setOpen] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.activeElement === inputRef.current && e.key === 'Backspace' && onChange) {
        onChange(undefined);
      }

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onChange, selected]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    onSearch(event.target.value);
  };

  const onSelect = (incidentId: Incident['id']) => {
    if (!onChange) return;
    onChange(incidentId);
    setOpen(false);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const renderSelectedValue = () => {
    if (loading) {
      return <span className="text-muted-foreground text-base md:text-sm">Loading...</span>;
    }

    if (selected && options[selected]) {
      const incident = options[selected];
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {incident.slug} — {incident.title}
          </span>
          <Badge variant="secondary" className="text-xs">
            {incident.status}
          </Badge>
          <span className="text-xs text-muted-foreground">({formatDate(incident.reportedAt)})</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          aria-expanded={open}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            'h-9 cursor-pointer',
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {loading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverAnchor />
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search incidents..."
                onChange={handleSearchChange}
                ref={inputRef}
              />
            </div>
            <Separator />
            <ScrollArea className="p-2" type="always" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !loading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No incidents found</li>
                )}
                {Object.values(options).map((incident) => (
                  <li
                    onClick={() => onSelect(incident.id)}
                    className="flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3"
                    key={incident.id}
                  >
                    <Check
                      className={cn('size-4 flex-shrink-0', selected === incident.id ? 'opacity-100' : 'opacity-0')}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="font-medium">
                        {incident.slug} — {incident.title}
                      </span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {incident.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">({formatDate(incident.reportedAt)})</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </ScrollArea>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
