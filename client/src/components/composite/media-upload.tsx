import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { TransientFileSchema } from '@shared/schema.types';
import { Camera, FileImage, FileVideo, Upload, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { z } from 'zod';

type TransientFile = z.infer<typeof TransientFileSchema>;

interface MediaUploadProps {
  maxFiles?: number;
  maxSize?: number; // in MB
  className?: string;
  disabled?: boolean;
  files: TransientFile[];
  setFiles: (tFiles: TransientFile[], files: File[]) => void;
}

export function MediaUpload({
  maxFiles = 3,
  maxSize = 20,
  className,
  disabled = false,
  files,
  setFiles,
}: MediaUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<TransientFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [remainingCount, setRemainingCount] = useState(maxFiles);

  const inputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (files) {
      setUploadedFiles(files);
    }
  }, [files]);

  // Update remaining count
  useEffect(() => {
    setRemainingCount(maxFiles - uploadedFiles.length);
  }, [uploadedFiles, maxFiles]);

  // Formats for accepted file types
  const acceptedFormats = '.jpg,.jpeg,.png,.heic,.mp4,.mov';

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  // Process selected files
  const handleFiles = (fileList: FileList | null) => {
    if (!fileList) return;

    const newFiles: TransientFile[] = [];
    const updatedFiles = [...uploadedFiles];

    // Return if adding would exceed max files
    if (fileList.length + uploadedFiles.length > maxFiles) {
      alert(`You can only upload a maximum of ${maxFiles} files.`);
      return;
    }

    const files = Array.from(fileList);

    for (const file of files) {
      // Check file type (expanded for mobile formats)
      if (!file.type.match(/^(image\/(jpeg|png|heic)|video\/(mp4|quicktime))$/)) {
        alert(`File ${file.name} is not a supported format. Please upload JPG, PNG, HEIC, MP4, or MOV files.`);
        return;
      }

      // Check file size (convert MB to bytes)
      if (file.size > maxSize * 1024 * 1024) {
        alert(`File ${file.name} exceeds the maximum size of ${maxSize}MB.`);
        return;
      }

      // Store the original File object with preview metadata
      newFiles.push({
        name: file.name,
        url: URL.createObjectURL(file),
        type: file.type,
        size: file.size,
      });
    }

    // Update state with new files
    const combinedFiles = [...updatedFiles, ...newFiles];
    setUploadedFiles(combinedFiles);

    setFiles(combinedFiles, files);

    // Reset input values so same file can be uploaded again if needed
    if (inputRef.current) {
      inputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }
  };

  // Remove a file
  const removeFile = (name: string) => {
    const updatedFiles = uploadedFiles.filter((item) => item.name !== name);
    setUploadedFiles(updatedFiles);
    setFiles?.(updatedFiles, []);
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Handle camera click
  const handleCameraClick = () => {
    cameraInputRef.current?.click();
  };

  // Handle gallery click
  const handleGalleryClick = () => {
    inputRef.current?.click();
  };

  return (
    <>
      {/* Camera input (hidden) */}
      <input
        ref={cameraInputRef}
        type="file"
        onChange={(e) => handleFileChange(e)}
        accept="image/*,video/*"
        capture="environment"
        className="hidden"
        aria-hidden="true"
      />

      {/* Gallery input (hidden) */}
      <input
        ref={inputRef}
        type="file"
        onChange={(e) => handleFileChange(e)}
        multiple
        accept={acceptedFormats}
        className="hidden"
        aria-hidden="true"
      />

      {/* Main upload container */}
      <div
        ref={containerRef}
        className={cn(
          'relative rounded-[8px] border border-dashed border-border transition-colors',
          'p-4 sm:p-4', // Default padding
          'bg-[#F9FAFB] sm:bg-background', // Subtle background on mobile
          'min-h-[44px]', // Minimum touch target size
          dragActive ? 'bg-primary/5 border-primary/30' : '',
          className,
        )}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        {/* Header with title and description */}
        <div className="flex flex-col items-center sm:items-start mb-4">
          {/* Title with icon */}
          <div className="flex items-center gap-2 mb-2 w-full justify-center sm:justify-start">
            <div className="shrink-0 flex items-center justify-center h-6 w-6 rounded-full bg-primary/10">
              <Upload className="h-3 w-3 text-primary" />
            </div>
            <div className="text-[14px] font-medium">Add a photo or video (optional)</div>
          </div>

          {/* Description text - centered on mobile, left-aligned on desktop */}
          <div className="w-full">
            <div className="text-[12px] text-muted-foreground text-center sm:text-left">
              Upload supporting media to help clarify what happened.
            </div>

            <div className="text-[11px] text-muted-foreground mt-1 text-center sm:text-left">
              Formats: .jpg, .png, .heic, .mp4, .mov — max {maxSize}MB each (3 max)
            </div>
          </div>
        </div>

        {/* CTA buttons - different for mobile and desktop */}
        {/* Mobile buttons (camera and gallery) */}
        <div className="flex flex-col gap-2 justify-center mt-3 sm:hidden">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleCameraClick}
            className="text-xs h-10 flex items-center justify-center gap-2"
            disabled={disabled || uploadedFiles.length >= maxFiles}
          >
            <Camera className="h-4 w-4" />
            Take Photo or Video
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleGalleryClick}
            className="text-xs h-10 flex items-center justify-center gap-2"
            disabled={disabled || uploadedFiles.length >= maxFiles}
          >
            <FileImage className="h-4 w-4" />
            Choose from Library
          </Button>
        </div>

        {/* Desktop button (browse files) */}
        <div className="hidden sm:flex justify-start mt-3">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleGalleryClick}
            className="text-xs h-9 px-3"
            disabled={disabled || uploadedFiles.length >= maxFiles}
            aria-label="Browse files to upload"
          >
            <Upload className="h-4 w-4 mr-2" />
            Browse Files
          </Button>
        </div>

        {/* File counter when files are uploaded */}
        {uploadedFiles.length > 0 && (
          <div className="mt-3 flex justify-center sm:justify-start">
            <div className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary">
              {remainingCount} {remainingCount === 1 ? 'file' : 'files'} remaining
            </div>
          </div>
        )}

        {/* Preview area for uploaded files */}
        {uploadedFiles.length > 0 && (
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
            {uploadedFiles.map((file, index) => (
              <div
                key={file.name}
                className="relative rounded-md border bg-white sm:bg-muted/20 p-2 group shadow-xs"
                aria-label={`Preview of uploaded file: ${file.name}`}
              >
                <div className="aspect-video relative flex items-center justify-center overflow-hidden rounded">
                  {file.type?.startsWith('image/') ? (
                    <img src={file.url} alt={`Preview of ${file.name}`} className="h-full w-full object-cover" />
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full w-full bg-muted">
                      <FileVideo className="h-7 w-7 sm:h-8 sm:w-8 text-muted-foreground mb-1" />
                      <span className="text-xs text-muted-foreground truncate max-w-[90%]">{file.name}</span>
                    </div>
                  )}
                </div>

                {/* Remove button */}
                <button
                  type="button"
                  onClick={() => removeFile(file.name)}
                  className="absolute cursor-pointer -top-1.5 -right-1.5 h-5 w-5 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                  aria-label={`Remove file ${file.name}`}
                >
                  <X className="h-3 w-3" />
                </button>

                {/* File caption */}
                <div className="mt-2 text-xs text-center text-muted-foreground">
                  {file.type?.startsWith('image/') ? 'Photo' : 'Video'} {index + 1} of {uploadedFiles.length}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
}
