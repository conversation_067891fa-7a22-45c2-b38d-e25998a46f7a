import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import confetti from 'canvas-confetti';
import { ArrowLeft, FileText, Plus, ShieldCheck } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect } from 'react';
import { Link } from 'wouter';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onViewIncident: () => void;
  onReportAnother?: () => void;
}

export function SuccessModal({ isOpen, onClose, onReportAnother, onViewIncident }: SuccessModalProps) {
  const isMobile = useIsMobile();

  // Trigger confetti animation
  useEffect(() => {
    if (isOpen) {
      // Trigger confetti after a short delay to ensure modal is visible
      const timer = setTimeout(() => {
        triggerConfetti();

        // Trigger haptic feedback on mobile if supported
        if (isMobile && navigator.vibrate) {
          navigator.vibrate([100, 50, 100]);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isOpen, isMobile]);

  // Confetti animation function
  const triggerConfetti = () => {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = {
      startVelocity: 30,
      spread: 360,
      ticks: 60,
      zIndex: 9999,
    };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // Since they are random anyway, we can use the same random call for both colors
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        colors: ['#dc2626', '#ef4444', '#b91c1c'],
      });

      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#dc2626', '#ef4444', '#b91c1c'],
      });
    }, 250);
  };

  // Different styling and layout for mobile vs desktop
  const renderMobileContent = () => (
    <motion.div
      className="fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-xl shadow-2xl"
      initial={{ y: '100%' }}
      animate={{ y: 0 }}
      exit={{ y: '100%' }}
      transition={{ type: 'spring', damping: 30, stiffness: 300 }}
    >
      <div className="p-6 space-y-6">
        <div className="flex flex-col items-center text-center">
          <div className="mb-4 flex items-center justify-center h-20 w-20 rounded-full bg-indigo-50">
            <ShieldCheck className="h-10 w-10 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-center flex items-center gap-2">
            <span>🦺</span> Report Submitted!
          </h2>
          <p className="text-muted-foreground mt-2 mb-4">
            Thank you for speaking up. Your action helps keep everyone safer.
          </p>
        </div>

        <div className="space-y-3">
          <Button variant="outline" className="w-full py-6 text-base" onClick={onViewIncident}>
            <FileText className="mr-2 h-5 w-5" />
            View Report
          </Button>

          <Button variant="outline" className="w-full py-6 text-base" onClick={onReportAnother}>
            <Plus className="mr-2 h-5 w-5" />
            Report Another Incident
          </Button>
        </div>
      </div>
    </motion.div>
  );

  const renderDesktopContent = () => (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative bg-white rounded-xl shadow-xl w-[550px] p-8"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col items-center text-center mb-8">
          <div className="mb-6 flex items-center justify-center h-24 w-24 rounded-full bg-indigo-50">
            <ShieldCheck className="h-12 w-12 text-indigo-600" />
          </div>
          <h2 className="text-3xl font-bold text-center flex items-center gap-2">
            <span>🦺</span> Report Submitted!
          </h2>
          <p className="text-muted-foreground mt-3 text-lg max-w-md">
            Thank you for speaking up. Your action helps keep everyone safer.
          </p>
        </div>

        <div className="flex justify-center space-x-4">
          <Button variant="outline" onClick={onViewIncident} className="min-w-[140px]">
            <FileText className="mr-2 h-4 w-4" />
            View Report
          </Button>

          <Button variant="outline" onClick={onReportAnother} className="min-w-[140px]">
            <Plus className="mr-2 h-4 w-4" />
            Report Another
          </Button>

          {/* <Button onClick={onClose} asChild className="min-w-[140px]">
            <Link to="/">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </Button> */}
        </div>
      </motion.div>
    </motion.div>
  );

  return <AnimatePresence>{isOpen && (isMobile ? renderMobileContent() : renderDesktopContent())}</AnimatePresence>;
}
