import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Check, Search, User as UserIcon, X } from "lucide-react";
import type React from "react";
import { useCallback, useState } from "react";

interface User {
  id: string;
  name: string;
  role?: string;
  department?: string;
  avatar?: string;
}

interface UserSelectProps {
  onSelect: (members: User[]) => void;
  selectedMembers: User[];
  className?: string;
  isMobile?: boolean;
}

// Mock data for team members - in a real app, this would come from an API
const mockTeamMembers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Safety Coordinator",
    department: "Operations",
    avatar: "",
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "Plant Manager",
    department: "Production",
    avatar: "",
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "Maintenance Supervisor",
    department: "Maintenance",
    avatar: "",
  },
  {
    id: "4",
    name: "Olivia Smith",
    role: "EHS Specialist",
    department: "Safety",
    avatar: "",
  },
  {
    id: "5",
    name: "<PERSON>",
    role: "Line Supervisor",
    department: "Production",
    avatar: "",
  },
  {
    id: "6",
    name: "Emily Davis",
    role: "HR Specialist",
    department: "Human Resources",
    avatar: "",
  },
  {
    id: "7",
    name: "David Martinez",
    role: "Quality Control",
    department: "Quality",
    avatar: "",
  },
  {
    id: "8",
    name: "Jessica Brown",
    role: "Operations Manager",
    department: "Operations",
    avatar: "",
  },
];

export function UserSelect({
  onSelect,
  selectedMembers,
  className,
  isMobile = false,
}: UserSelectProps) {
  const [search, setSearch] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  // Filter team members based on search
  const filteredMembers =
    search === ""
      ? [] // Don't show any members when search is empty
      : mockTeamMembers.filter((member) => {
          return (
            member.name.toLowerCase().includes(search.toLowerCase()) ||
            member.role?.toLowerCase().includes(search.toLowerCase()) ||
            member.department?.toLowerCase().includes(search.toLowerCase())
          );
        });

  // Check if a member is selected
  const isSelected = (memberId: string) => {
    return selectedMembers.some((m) => m.id === memberId);
  };

  // Function to toggle member selection
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const handleToggleMember = useCallback(
    (member: User) => {
      if (isSelected(member.id)) {
        // Remove member
        onSelect(selectedMembers.filter((m) => m.id !== member.id));
      } else {
        // Add member
        onSelect([...selectedMembers, member]);
        // Clear search to hide dropdown after selection
        setSearch("");
        setShowDropdown(false);
      }
    },
    [selectedMembers, onSelect],
  );

  // Get name initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Handle dialog confirm
  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSearch(""); // Clear search when closing
  };

  // Remove member from selection
  const removeMember = (member: User, e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(selectedMembers.filter((m) => m.id !== member.id));
  };

  // Handler for search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearch(value);
    setShowDropdown(value.length > 0);
  };

  // Focus handler for input
  const handleFocus = () => {
    if (search.length > 0) {
      setShowDropdown(true);
    }
  };

  return (
    <>
      {/* For mobile: show dialog on click */}
      {isMobile ? (
        <>
          <div
            className={cn(
              "relative w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 cursor-pointer",
              className,
            )}
            onClick={() => setIsDialogOpen(true)}
          >
            {selectedMembers.length > 0 ? (
              <div className="flex flex-wrap gap-1.5 py-1">
                {selectedMembers.map((member) => (
                  <Badge
                    key={member.id}
                    variant="secondary"
                    className="gap-1.5 p-1.5"
                  >
                    <Avatar className="h-5 w-5">
                      <AvatarFallback className="text-[10px]">
                        {getInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{member.name}</span>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="flex items-center gap-2 text-muted-foreground h-8">
                <UserIcon className="h-4 w-4" />
                <span>Search and tag people to notify</span>
              </div>
            )}
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Notify Team Members</DialogTitle>
                <DialogDescription>
                  Select team members who should be notified about this
                  incident.
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <div className="flex items-center border-b px-3 mb-3 rounded-md border">
                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  <Input
                    placeholder="Search team members..."
                    value={search}
                    onChange={handleSearchChange}
                    className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>

                <div className="max-h-[300px] overflow-y-auto">
                  {search !== "" && (
                    <div className="flex flex-col gap-2">
                      {filteredMembers.map((member) => (
                        <div
                          key={member.id}
                          className={cn(
                            "flex items-center justify-between p-2 rounded-md",
                            isSelected(member.id)
                              ? "bg-primary/10"
                              : "hover:bg-muted",
                          )}
                          onClick={() => handleToggleMember(member)}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar className="h-7 w-7">
                              <AvatarFallback>
                                {getInitials(member.name)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="font-medium text-sm">
                                {member.name}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {member.role} • {member.department}
                              </span>
                            </div>
                          </div>

                          {isSelected(member.id) && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}

                      {filteredMembers.length === 0 && (
                        <div className="py-6 text-center text-muted-foreground">
                          No team members found
                        </div>
                      )}
                    </div>
                  )}

                  {search === "" && (
                    <div className="py-6 text-center text-muted-foreground">
                      Start typing to search for team members
                    </div>
                  )}
                </div>
              </div>

              <DialogFooter className="sm:justify-end">
                <Button variant="secondary" onClick={handleDialogClose}>
                  Cancel
                </Button>
                <Button onClick={handleDialogClose}>
                  Confirm ({selectedMembers.length})
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      ) : (
        // For desktop: inline search/select
        <div className={cn("w-full relative", className)}>
          <div className="rounded-md border border-input overflow-hidden">
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search and tag people to notify"
                className="h-9 text-sm border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                value={search}
                onChange={handleSearchChange}
                onFocus={handleFocus}
              />
            </div>

            {/* Selected members badges */}
            {selectedMembers.length > 0 && (
              <div className="flex flex-wrap gap-1.5 p-2 border-t">
                {selectedMembers.map((member) => (
                  <Badge
                    key={member.id}
                    variant="secondary"
                    className="gap-1.5 p-1.5"
                  >
                    <Avatar className="h-5 w-5">
                      <AvatarFallback className="text-[10px]">
                        {getInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{member.name}</span>
                    <button
                      className="ml-1 rounded-full hover:bg-muted"
                      onClick={(e) => removeMember(member, e)}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Dropdown menu */}
            {showDropdown && search !== "" && (
              <div
                className={`max-h-[300px] overflow-y-auto ${selectedMembers.length > 0 ? "" : "border-t"}`}
              >
                {filteredMembers.length > 0 ? (
                  <div className="py-1">
                    {filteredMembers.map((member) => (
                      <div
                        key={member.id}
                        className={`flex items-center justify-between p-2 text-sm cursor-pointer ${
                          isSelected(member.id) ? "bg-muted" : "hover:bg-muted"
                        }`}
                        onClick={() => handleToggleMember(member)}
                      >
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback>
                              {getInitials(member.name)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="font-medium text-sm">
                              {member.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {member.role} • {member.department}
                            </span>
                          </div>
                        </div>

                        {isSelected(member.id) && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-6 text-center text-sm text-muted-foreground">
                    No team members found
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
