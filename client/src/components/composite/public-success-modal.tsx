import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import confetti from 'canvas-confetti';
import { Plus, ShieldCheck } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect } from 'react';

interface PublicSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReportAnother?: () => void;
  reportSlug?: string;
}

export function PublicSuccessModal({ isOpen, onClose, onReportAnother, reportSlug }: PublicSuccessModalProps) {
  const isMobile = useIsMobile();

  // Trigger confetti animation
  useEffect(() => {
    if (isOpen) {
      // Trigger confetti after a short delay to ensure modal is visible
      const timer = setTimeout(() => {
        triggerConfetti();

        // Trigger haptic feedback on mobile if supported
        if (isMobile && navigator.vibrate) {
          navigator.vibrate([100, 50, 100]);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isOpen, isMobile]);

  // Confetti animation function
  const triggerConfetti = () => {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = {
      startVelocity: 30,
      spread: 360,
      ticks: 60,
      zIndex: 9999,
    };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // Colorful confetti like in the screenshot
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });

      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });
    }, 250);
  };

  // Different styling and layout for mobile vs desktop
  const renderMobileContent = () => (
    <motion.div
      className="fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-xl shadow-2xl"
      initial={{ y: '100%' }}
      animate={{ y: 0 }}
      exit={{ y: '100%' }}
      transition={{ type: 'spring', damping: 30, stiffness: 300 }}
    >
      <div className="p-6 space-y-6">
        <div className="flex flex-col items-center text-center">
          <div className="mb-4 flex items-center justify-center h-20 w-20 rounded-full bg-green-100">
            <ShieldCheck className="h-10 w-10 text-green-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-4">Safety Report Submitted Successfully!</h2>

          {reportSlug && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-1">Your Report ID:</p>
              <p className="text-lg font-mono font-bold text-gray-900">{reportSlug}</p>
            </div>
          )}

          <p className="text-sm text-gray-600 leading-relaxed max-w-sm">
            A confirmation email has been sent to the address you provided. Our safety team will review your report and
            take appropriate action.
          </p>
        </div>

        <div className="space-y-3">
          <Button className="w-full py-6 text-base bg-blue-600 hover:bg-blue-700" onClick={onReportAnother}>
            <Plus className="mr-2 h-5 w-5" />
            Submit Another Report
          </Button>
        </div>
      </div>
    </motion.div>
  );

  const renderDesktopContent = () => (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative bg-white rounded-xl shadow-xl w-[550px] p-8"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col items-center text-center mb-8">
          <div className="mb-6 flex items-center justify-center h-24 w-24 rounded-full bg-green-100">
            <ShieldCheck className="h-12 w-12 text-green-600" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Safety Report Submitted Successfully!</h2>

          {reportSlug && (
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-2">Your Report ID:</p>
              <p className="text-2xl font-mono font-bold text-gray-900">{reportSlug}</p>
            </div>
          )}

          <p className="text-gray-600 text-lg max-w-md leading-relaxed">
            A confirmation email has been sent to the address you provided. Our safety team will review your report and
            take appropriate action.
          </p>
        </div>

        <div className="flex justify-center">
          <Button className="min-w-[200px] py-3" onClick={onReportAnother}>
            <Plus className="mr-2 h-4 w-4" />
            Submit Another Report
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );

  return <AnimatePresence>{isOpen && (isMobile ? renderMobileContent() : renderDesktopContent())}</AnimatePresence>;
}
